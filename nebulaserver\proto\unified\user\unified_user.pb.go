// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.10
// 	protoc        v6.33.0
// source: unified_user.proto

package user

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 查询类型枚举
type QueryType int32

const (
	QueryType_QUERY_TYPE_UNSPECIFIED QueryType = 0 // 未指定（默认）
	QueryType_SINGLE_USER            QueryType = 1 // 查询单个用户
	QueryType_DEPARTMENT_USERS       QueryType = 2 // 查询部门用户
	QueryType_ALL_USERS              QueryType = 3 // 查询企业所有用户
)

// Enum value maps for QueryType.
var (
	QueryType_name = map[int32]string{
		0: "QUERY_TYPE_UNSPECIFIED",
		1: "SINGLE_USER",
		2: "DEPARTMENT_USERS",
		3: "ALL_USERS",
	}
	QueryType_value = map[string]int32{
		"QUERY_TYPE_UNSPECIFIED": 0,
		"SINGLE_USER":            1,
		"DEPARTMENT_USERS":       2,
		"ALL_USERS":              3,
	}
)

func (x QueryType) Enum() *QueryType {
	p := new(QueryType)
	*p = x
	return p
}

func (x QueryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryType) Descriptor() protoreflect.EnumDescriptor {
	return file_unified_user_proto_enumTypes[0].Descriptor()
}

func (QueryType) Type() protoreflect.EnumType {
	return &file_unified_user_proto_enumTypes[0]
}

func (x QueryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryType.Descriptor instead.
func (QueryType) EnumDescriptor() ([]byte, []int) {
	return file_unified_user_proto_rawDescGZIP(), []int{0}
}

// 统一用户查询请求
type GetUsersRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	AppId     string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`                                          // 必填：应用ID
	Runtime   string                 `protobuf:"bytes,2,opt,name=runtime,proto3" json:"runtime,omitempty"`                                                   // 可选：平台类型（dtalk/feishu/wework）
	CorpId    string                 `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`                                       // 必填：企业ID
	QueryType QueryType              `protobuf:"varint,4,opt,name=query_type,json=queryType,proto3,enum=unified.user.QueryType" json:"query_type,omitempty"` // 必填：查询类型
	// 以下字段根据 query_type 有选择的使用
	UserId string `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`  // SINGLE_USER 时必填：用户ID
	DeptId uint64 `protobuf:"varint,6,opt,name=dept_id,json=deptId,proto3" json:"dept_id,omitempty"` // DEPARTMENT_USERS 时必填：部门ID
	// 分页参数（适用于 DEPARTMENT_USERS 和 ALL_USERS）
	Page     int32 `protobuf:"varint,7,opt,name=page,proto3" json:"page,omitempty"`                         // 页码，默认1
	PageSize int32 `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"` // 每页数量，默认20
	// 额外参数
	IncludeSubDept bool  `protobuf:"varint,9,opt,name=include_sub_dept,json=includeSubDept,proto3" json:"include_sub_dept,omitempty"` // DEPARTMENT_USERS 时可用：是否包含子部门用户
	IsLeave        int32 `protobuf:"varint,10,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`                       // ALL_USERS 时可用：是否离职：0-在职，1-离职，-1-全部
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUsersRequest) Reset() {
	*x = GetUsersRequest{}
	mi := &file_unified_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersRequest) ProtoMessage() {}

func (x *GetUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_unified_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersRequest.ProtoReflect.Descriptor instead.
func (*GetUsersRequest) Descriptor() ([]byte, []int) {
	return file_unified_user_proto_rawDescGZIP(), []int{0}
}

func (x *GetUsersRequest) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetUsersRequest) GetRuntime() string {
	if x != nil {
		return x.Runtime
	}
	return ""
}

func (x *GetUsersRequest) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *GetUsersRequest) GetQueryType() QueryType {
	if x != nil {
		return x.QueryType
	}
	return QueryType_QUERY_TYPE_UNSPECIFIED
}

func (x *GetUsersRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUsersRequest) GetDeptId() uint64 {
	if x != nil {
		return x.DeptId
	}
	return 0
}

func (x *GetUsersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUsersRequest) GetIncludeSubDept() bool {
	if x != nil {
		return x.IncludeSubDept
	}
	return false
}

func (x *GetUsersRequest) GetIsLeave() int32 {
	if x != nil {
		return x.IsLeave
	}
	return 0
}

// 统一用户查询响应
type GetUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                                        // 状态码：0-成功，其他-失败
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                                                   // 错误信息
	QueryType     QueryType              `protobuf:"varint,3,opt,name=query_type,json=queryType,proto3,enum=unified.user.QueryType" json:"query_type,omitempty"` // 查询类型（回显）
	Data          *UsersData             `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`                                                         // 用户数据（统一返回格式）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsersResponse) Reset() {
	*x = GetUsersResponse{}
	mi := &file_unified_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersResponse) ProtoMessage() {}

func (x *GetUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_unified_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersResponse.ProtoReflect.Descriptor instead.
func (*GetUsersResponse) Descriptor() ([]byte, []int) {
	return file_unified_user_proto_rawDescGZIP(), []int{1}
}

func (x *GetUsersResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUsersResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetUsersResponse) GetQueryType() QueryType {
	if x != nil {
		return x.QueryType
	}
	return QueryType_QUERY_TYPE_UNSPECIFIED
}

func (x *GetUsersResponse) GetData() *UsersData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserDetail struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CorpId         string                 `protobuf:"bytes,2,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UnionId        string                 `protobuf:"bytes,4,opt,name=union_id,json=unionId,proto3" json:"union_id,omitempty"`
	Username       string                 `protobuf:"bytes,5,opt,name=username,proto3" json:"username,omitempty"`
	Name           string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Avatar         string                 `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Mobile         string                 `protobuf:"bytes,8,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Telephone      string                 `protobuf:"bytes,9,opt,name=telephone,proto3" json:"telephone,omitempty"`
	JobNumber      string                 `protobuf:"bytes,10,opt,name=job_number,json=jobNumber,proto3" json:"job_number,omitempty"`
	Title          string                 `protobuf:"bytes,11,opt,name=title,proto3" json:"title,omitempty"`
	Email          string                 `protobuf:"bytes,12,opt,name=email,proto3" json:"email,omitempty"`
	OrgEmail       string                 `protobuf:"bytes,13,opt,name=org_email,json=orgEmail,proto3" json:"org_email,omitempty"`
	WorkPlace      string                 `protobuf:"bytes,14,opt,name=work_place,json=workPlace,proto3" json:"work_place,omitempty"`
	Remark         string                 `protobuf:"bytes,15,opt,name=remark,proto3" json:"remark,omitempty"`
	DepartmentName string                 `protobuf:"bytes,16,opt,name=department_name,json=departmentName,proto3" json:"department_name,omitempty"`
	DeptIdList     []string               `protobuf:"bytes,17,rep,name=dept_id_list,json=deptIdList,proto3" json:"dept_id_list,omitempty"`
	HiredDate      int64                  `protobuf:"varint,18,opt,name=hired_date,json=hiredDate,proto3" json:"hired_date,omitempty"`
	Active         int32                  `protobuf:"varint,19,opt,name=active,proto3" json:"active,omitempty"`
	Admin          int32                  `protobuf:"varint,20,opt,name=admin,proto3" json:"admin,omitempty"`
	Boss           int32                  `protobuf:"varint,21,opt,name=boss,proto3" json:"boss,omitempty"`
	Leader         int32                  `protobuf:"varint,22,opt,name=leader,proto3" json:"leader,omitempty"`
	IsLeave        int32                  `protobuf:"varint,23,opt,name=is_leave,json=isLeave,proto3" json:"is_leave,omitempty"`
	IsLeaveTime    int64                  `protobuf:"varint,24,opt,name=is_leave_time,json=isLeaveTime,proto3" json:"is_leave_time,omitempty"`
	CreatedAt      int64                  `protobuf:"varint,25,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      int64                  `protobuf:"varint,26,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeptId         int64                  `protobuf:"varint,27,opt,name=dept_id,json=deptId,proto3" json:"dept_id,omitempty"` // 🔧 添加缺失的dept_id字段
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserDetail) Reset() {
	*x = UserDetail{}
	mi := &file_unified_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDetail) ProtoMessage() {}

func (x *UserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_unified_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDetail.ProtoReflect.Descriptor instead.
func (*UserDetail) Descriptor() ([]byte, []int) {
	return file_unified_user_proto_rawDescGZIP(), []int{2}
}

func (x *UserDetail) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserDetail) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *UserDetail) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserDetail) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

func (x *UserDetail) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserDetail) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserDetail) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserDetail) GetTelephone() string {
	if x != nil {
		return x.Telephone
	}
	return ""
}

func (x *UserDetail) GetJobNumber() string {
	if x != nil {
		return x.JobNumber
	}
	return ""
}

func (x *UserDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserDetail) GetOrgEmail() string {
	if x != nil {
		return x.OrgEmail
	}
	return ""
}

func (x *UserDetail) GetWorkPlace() string {
	if x != nil {
		return x.WorkPlace
	}
	return ""
}

func (x *UserDetail) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UserDetail) GetDepartmentName() string {
	if x != nil {
		return x.DepartmentName
	}
	return ""
}

func (x *UserDetail) GetDeptIdList() []string {
	if x != nil {
		return x.DeptIdList
	}
	return nil
}

func (x *UserDetail) GetHiredDate() int64 {
	if x != nil {
		return x.HiredDate
	}
	return 0
}

func (x *UserDetail) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *UserDetail) GetAdmin() int32 {
	if x != nil {
		return x.Admin
	}
	return 0
}

func (x *UserDetail) GetBoss() int32 {
	if x != nil {
		return x.Boss
	}
	return 0
}

func (x *UserDetail) GetLeader() int32 {
	if x != nil {
		return x.Leader
	}
	return 0
}

func (x *UserDetail) GetIsLeave() int32 {
	if x != nil {
		return x.IsLeave
	}
	return 0
}

func (x *UserDetail) GetIsLeaveTime() int64 {
	if x != nil {
		return x.IsLeaveTime
	}
	return 0
}

func (x *UserDetail) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserDetail) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *UserDetail) GetDeptId() int64 {
	if x != nil {
		return x.DeptId
	}
	return 0
}

type UsersData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserDetail          `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersData) Reset() {
	*x = UsersData{}
	mi := &file_unified_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersData) ProtoMessage() {}

func (x *UsersData) ProtoReflect() protoreflect.Message {
	mi := &file_unified_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersData.ProtoReflect.Descriptor instead.
func (*UsersData) Descriptor() ([]byte, []int) {
	return file_unified_user_proto_rawDescGZIP(), []int{3}
}

func (x *UsersData) GetUsers() []*UserDetail {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *UsersData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *UsersData) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UsersData) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_unified_user_proto protoreflect.FileDescriptor

const file_unified_user_proto_rawDesc = "" +
	"\n" +
	"\x12unified_user.proto\x12\funified.user\"\xbb\x02\n" +
	"\x0fGetUsersRequest\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12\x18\n" +
	"\aruntime\x18\x02 \x01(\tR\aruntime\x12\x17\n" +
	"\acorp_id\x18\x03 \x01(\tR\x06corpId\x126\n" +
	"\n" +
	"query_type\x18\x04 \x01(\x0e2\x17.unified.user.QueryTypeR\tqueryType\x12\x17\n" +
	"\auser_id\x18\x05 \x01(\tR\x06userId\x12\x17\n" +
	"\adept_id\x18\x06 \x01(\x04R\x06deptId\x12\x12\n" +
	"\x04page\x18\a \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\b \x01(\x05R\bpageSize\x12(\n" +
	"\x10include_sub_dept\x18\t \x01(\bR\x0eincludeSubDept\x12\x19\n" +
	"\bis_leave\x18\n" +
	" \x01(\x05R\aisLeave\"\xa5\x01\n" +
	"\x10GetUsersResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x126\n" +
	"\n" +
	"query_type\x18\x03 \x01(\x0e2\x17.unified.user.QueryTypeR\tqueryType\x12+\n" +
	"\x04data\x18\x04 \x01(\v2\x17.unified.user.UsersDataR\x04data\"\xe0\x05\n" +
	"\n" +
	"UserDetail\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x17\n" +
	"\acorp_id\x18\x02 \x01(\tR\x06corpId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x19\n" +
	"\bunion_id\x18\x04 \x01(\tR\aunionId\x12\x1a\n" +
	"\busername\x18\x05 \x01(\tR\busername\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x16\n" +
	"\x06avatar\x18\a \x01(\tR\x06avatar\x12\x16\n" +
	"\x06mobile\x18\b \x01(\tR\x06mobile\x12\x1c\n" +
	"\ttelephone\x18\t \x01(\tR\ttelephone\x12\x1d\n" +
	"\n" +
	"job_number\x18\n" +
	" \x01(\tR\tjobNumber\x12\x14\n" +
	"\x05title\x18\v \x01(\tR\x05title\x12\x14\n" +
	"\x05email\x18\f \x01(\tR\x05email\x12\x1b\n" +
	"\torg_email\x18\r \x01(\tR\borgEmail\x12\x1d\n" +
	"\n" +
	"work_place\x18\x0e \x01(\tR\tworkPlace\x12\x16\n" +
	"\x06remark\x18\x0f \x01(\tR\x06remark\x12'\n" +
	"\x0fdepartment_name\x18\x10 \x01(\tR\x0edepartmentName\x12 \n" +
	"\fdept_id_list\x18\x11 \x03(\tR\n" +
	"deptIdList\x12\x1d\n" +
	"\n" +
	"hired_date\x18\x12 \x01(\x03R\thiredDate\x12\x16\n" +
	"\x06active\x18\x13 \x01(\x05R\x06active\x12\x14\n" +
	"\x05admin\x18\x14 \x01(\x05R\x05admin\x12\x12\n" +
	"\x04boss\x18\x15 \x01(\x05R\x04boss\x12\x16\n" +
	"\x06leader\x18\x16 \x01(\x05R\x06leader\x12\x19\n" +
	"\bis_leave\x18\x17 \x01(\x05R\aisLeave\x12\"\n" +
	"\ris_leave_time\x18\x18 \x01(\x03R\visLeaveTime\x12\x1d\n" +
	"\n" +
	"created_at\x18\x19 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x1a \x01(\x03R\tupdatedAt\x12\x17\n" +
	"\adept_id\x18\x1b \x01(\x03R\x06deptId\"\x82\x01\n" +
	"\tUsersData\x12.\n" +
	"\x05users\x18\x01 \x03(\v2\x18.unified.user.UserDetailR\x05users\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize*]\n" +
	"\tQueryType\x12\x1a\n" +
	"\x16QUERY_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vSINGLE_USER\x10\x01\x12\x14\n" +
	"\x10DEPARTMENT_USERS\x10\x02\x12\r\n" +
	"\tALL_USERS\x10\x032a\n" +
	"\x12UnifiedUserService\x12K\n" +
	"\bGetUsers\x12\x1d.unified.user.GetUsersRequest\x1a\x1e.unified.user.GetUsersResponse\"\x00B+Z)git.eykj.cn/base/golib/proto/unified/userb\x06proto3"

var (
	file_unified_user_proto_rawDescOnce sync.Once
	file_unified_user_proto_rawDescData []byte
)

func file_unified_user_proto_rawDescGZIP() []byte {
	file_unified_user_proto_rawDescOnce.Do(func() {
		file_unified_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_unified_user_proto_rawDesc), len(file_unified_user_proto_rawDesc)))
	})
	return file_unified_user_proto_rawDescData
}

var file_unified_user_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_unified_user_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_unified_user_proto_goTypes = []any{
	(QueryType)(0),           // 0: unified.user.QueryType
	(*GetUsersRequest)(nil),  // 1: unified.user.GetUsersRequest
	(*GetUsersResponse)(nil), // 2: unified.user.GetUsersResponse
	(*UserDetail)(nil),       // 3: unified.user.UserDetail
	(*UsersData)(nil),        // 4: unified.user.UsersData
}
var file_unified_user_proto_depIdxs = []int32{
	0, // 0: unified.user.GetUsersRequest.query_type:type_name -> unified.user.QueryType
	0, // 1: unified.user.GetUsersResponse.query_type:type_name -> unified.user.QueryType
	4, // 2: unified.user.GetUsersResponse.data:type_name -> unified.user.UsersData
	3, // 3: unified.user.UsersData.users:type_name -> unified.user.UserDetail
	1, // 4: unified.user.UnifiedUserService.GetUsers:input_type -> unified.user.GetUsersRequest
	2, // 5: unified.user.UnifiedUserService.GetUsers:output_type -> unified.user.GetUsersResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_unified_user_proto_init() }
func file_unified_user_proto_init() {
	if File_unified_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_unified_user_proto_rawDesc), len(file_unified_user_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_unified_user_proto_goTypes,
		DependencyIndexes: file_unified_user_proto_depIdxs,
		EnumInfos:         file_unified_user_proto_enumTypes,
		MessageInfos:      file_unified_user_proto_msgTypes,
	}.Build()
	File_unified_user_proto = out.File
	file_unified_user_proto_goTypes = nil
	file_unified_user_proto_depIdxs = nil
}
