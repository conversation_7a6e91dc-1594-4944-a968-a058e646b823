// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Different proto type definitions for testing the Types registry.
syntax = "proto2";

package testprotos;

option go_package = "google.golang.org/protobuf/internal/testprotos/registry";

message Message1 {
  extensions 10 to max;
}

message Message2 {}

message Message3 {}

enum Enum1 {
  ONE = 1;
}

enum Enum2 {
  UNO = 1;
}

enum Enum3 {
  YI = 1;
}

extend Message1 {
  optional string string_field = 11;
  optional Enum1 enum_field = 12;
  optional Message2 message_field = 13;
}

message Message4 {
  optional bool bool_field = 30;

  extend Message1 {
    optional Message2 message_field = 21;
    optional Enum1 enum_field = 22;
    optional string string_field = 23;
  }
}
