<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f301e674-8025-437f-b4f1-ce3ccc74dcce" name="Changes" comment="feat(grpc): 实现用户服务核心功能&#10;&#10;- 新增UserServiceServer构造函数，支持依赖注入userAPI和authAPI&#10;- 完善GetUserInfo接口，支持通过user_id查询用户详情&#10;- 实现GetUserList接口，支持获取部门用户列表&#10;- 添加access_token获取和错误处理机制&#10;- 增加参数校验和初始化状态检查&#10;- 实现钉钉API调用和数据转换逻辑&#10;- 添加详细的日志记录和错误码定义">
      <change beforePath="$PROJECT_DIR$/internal/pkg/grpc/server.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/pkg/grpc/server.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/pkg/grpc/user_service.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/pkg/grpc/user_service.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/dtalk/api/user.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/dtalk/api/user.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../Work/go" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="Master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HttpClientOnboardingState">{
  &quot;isOnboardingCommentShown&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="34lbFodWMkXcJnDn5JXTDZkrPXo" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go Build.go build fdd622server.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "Beta",
    "go.import.settings.migrated": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/goproject/fdd622server",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.goide.configuration.GoLibrariesConfigurableProvider"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="go build fdd622server" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="fdd622server" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="fdd622server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build fdd622server" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-f466f9b0953e-146d08934cbf-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-252.27397.100" />
        <option value="bundled-js-predefined-d6986cc7102b-3aa1da707db6-JavaScript-GO-252.27397.100" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f301e674-8025-437f-b4f1-ce3ccc74dcce" name="Changes" comment="" />
      <created>1761791004523</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1761791004523</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>