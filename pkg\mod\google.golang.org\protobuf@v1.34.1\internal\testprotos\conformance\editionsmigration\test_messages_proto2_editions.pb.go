// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd
//
// Test schema for proto2 messages.  This test schema is used by:
//
// - conformance tests

// LINT: ALLOW_GROUPS

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: editions/golden/test_messages_proto2_editions.proto

package editions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type ForeignEnumProto2 int32

const (
	ForeignEnumProto2_FOREIGN_FOO ForeignEnumProto2 = 0
	ForeignEnumProto2_FOREIGN_BAR ForeignEnumProto2 = 1
	ForeignEnumProto2_FOREIGN_BAZ ForeignEnumProto2 = 2
)

// Enum value maps for ForeignEnumProto2.
var (
	ForeignEnumProto2_name = map[int32]string{
		0: "FOREIGN_FOO",
		1: "FOREIGN_BAR",
		2: "FOREIGN_BAZ",
	}
	ForeignEnumProto2_value = map[string]int32{
		"FOREIGN_FOO": 0,
		"FOREIGN_BAR": 1,
		"FOREIGN_BAZ": 2,
	}
)

func (x ForeignEnumProto2) Enum() *ForeignEnumProto2 {
	p := new(ForeignEnumProto2)
	*p = x
	return p
}

func (x ForeignEnumProto2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnumProto2) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto2_editions_proto_enumTypes[0].Descriptor()
}

func (ForeignEnumProto2) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto2_editions_proto_enumTypes[0]
}

func (x ForeignEnumProto2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForeignEnumProto2.Descriptor instead.
func (ForeignEnumProto2) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0}
}

type TestAllTypesProto2_NestedEnum int32

const (
	TestAllTypesProto2_FOO TestAllTypesProto2_NestedEnum = 0
	TestAllTypesProto2_BAR TestAllTypesProto2_NestedEnum = 1
	TestAllTypesProto2_BAZ TestAllTypesProto2_NestedEnum = 2
	TestAllTypesProto2_NEG TestAllTypesProto2_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypesProto2_NestedEnum.
var (
	TestAllTypesProto2_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypesProto2_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypesProto2_NestedEnum) Enum() *TestAllTypesProto2_NestedEnum {
	p := new(TestAllTypesProto2_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypesProto2_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypesProto2_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto2_editions_proto_enumTypes[1].Descriptor()
}

func (TestAllTypesProto2_NestedEnum) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto2_editions_proto_enumTypes[1]
}

func (x TestAllTypesProto2_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypesProto2_NestedEnum.Descriptor instead.
func (TestAllTypesProto2_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 0}
}

type EnumOnlyProto2_Bool int32

const (
	EnumOnlyProto2_kFalse EnumOnlyProto2_Bool = 0
	EnumOnlyProto2_kTrue  EnumOnlyProto2_Bool = 1
)

// Enum value maps for EnumOnlyProto2_Bool.
var (
	EnumOnlyProto2_Bool_name = map[int32]string{
		0: "kFalse",
		1: "kTrue",
	}
	EnumOnlyProto2_Bool_value = map[string]int32{
		"kFalse": 0,
		"kTrue":  1,
	}
)

func (x EnumOnlyProto2_Bool) Enum() *EnumOnlyProto2_Bool {
	p := new(EnumOnlyProto2_Bool)
	*p = x
	return p
}

func (x EnumOnlyProto2_Bool) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumOnlyProto2_Bool) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto2_editions_proto_enumTypes[2].Descriptor()
}

func (EnumOnlyProto2_Bool) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto2_editions_proto_enumTypes[2]
}

func (x EnumOnlyProto2_Bool) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumOnlyProto2_Bool.Descriptor instead.
func (EnumOnlyProto2_Bool) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{5, 0}
}

type TestAllRequiredTypesProto2_NestedEnum int32

const (
	TestAllRequiredTypesProto2_FOO TestAllRequiredTypesProto2_NestedEnum = 0
	TestAllRequiredTypesProto2_BAR TestAllRequiredTypesProto2_NestedEnum = 1
	TestAllRequiredTypesProto2_BAZ TestAllRequiredTypesProto2_NestedEnum = 2
	TestAllRequiredTypesProto2_NEG TestAllRequiredTypesProto2_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllRequiredTypesProto2_NestedEnum.
var (
	TestAllRequiredTypesProto2_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllRequiredTypesProto2_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllRequiredTypesProto2_NestedEnum) Enum() *TestAllRequiredTypesProto2_NestedEnum {
	p := new(TestAllRequiredTypesProto2_NestedEnum)
	*p = x
	return p
}

func (x TestAllRequiredTypesProto2_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllRequiredTypesProto2_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto2_editions_proto_enumTypes[3].Descriptor()
}

func (TestAllRequiredTypesProto2_NestedEnum) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto2_editions_proto_enumTypes[3]
}

func (x TestAllRequiredTypesProto2_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_NestedEnum.Descriptor instead.
func (TestAllRequiredTypesProto2_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 0}
}

// This proto includes every type of field in both singular and repeated
// forms.
//
// Also, crucially, all messages and enums in this file are eventually
// submessages of this message.  So for example, a fuzz test of TestAllTypes
// could trigger bugs that occur in any message type in this file.  We verify
// this stays true in a unit test.
type TestAllTypesProto2 struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Singular
	OptionalInt32          *int32                            `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalInt64          *int64                            `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalUint32         *uint32                           `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalUint64         *uint64                           `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSint32         *int32                            `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalSint64         *int64                            `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalFixed32        *uint32                           `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFixed64        *uint64                           `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       *int32                            `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       *int64                            `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFloat          *float32                          `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalDouble         *float64                          `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalBool           *bool                             `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalString         *string                           `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                            `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	OptionalNestedMessage  *TestAllTypesProto2_NestedMessage `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessageProto2             `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage" json:"optional_foreign_message,omitempty"`
	OptionalNestedEnum     *TestAllTypesProto2_NestedEnum    `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    *ForeignEnumProto2                `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,enum=protobuf_test_messages.editions.proto2.ForeignEnumProto2" json:"optional_foreign_enum,omitempty"`
	OptionalStringPiece    *string                           `protobuf:"bytes,24,opt,name=optional_string_piece,json=optionalStringPiece" json:"optional_string_piece,omitempty"`
	OptionalCord           *string                           `protobuf:"bytes,25,opt,name=optional_cord,json=optionalCord" json:"optional_cord,omitempty"`
	RecursiveMessage       *TestAllTypesProto2               `protobuf:"bytes,27,opt,name=recursive_message,json=recursiveMessage" json:"recursive_message,omitempty"`
	// Repeated
	RepeatedInt32          []int32                             `protobuf:"varint,31,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                             `protobuf:"varint,32,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                            `protobuf:"varint,33,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                            `protobuf:"varint,34,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                             `protobuf:"zigzag32,35,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                             `protobuf:"zigzag64,36,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                            `protobuf:"fixed32,37,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                            `protobuf:"fixed64,38,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                             `protobuf:"fixed32,39,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                             `protobuf:"fixed64,40,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                           `protobuf:"fixed32,41,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                           `protobuf:"fixed64,42,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                              `protobuf:"varint,43,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedString         []string                            `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                            `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	RepeatedNestedMessage  []*TestAllTypesProto2_NestedMessage `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessageProto2             `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage" json:"repeated_foreign_message,omitempty"`
	RepeatedNestedEnum     []TestAllTypesProto2_NestedEnum     `protobuf:"varint,51,rep,name=repeated_nested_enum,json=repeatedNestedEnum,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnumProto2                 `protobuf:"varint,52,rep,name=repeated_foreign_enum,json=repeatedForeignEnum,enum=protobuf_test_messages.editions.proto2.ForeignEnumProto2" json:"repeated_foreign_enum,omitempty"`
	RepeatedStringPiece    []string                            `protobuf:"bytes,54,rep,name=repeated_string_piece,json=repeatedStringPiece" json:"repeated_string_piece,omitempty"`
	RepeatedCord           []string                            `protobuf:"bytes,55,rep,name=repeated_cord,json=repeatedCord" json:"repeated_cord,omitempty"`
	// Packed
	PackedInt32      []int32                         `protobuf:"varint,75,rep,packed,name=packed_int32,json=packedInt32" json:"packed_int32,omitempty"`
	PackedInt64      []int64                         `protobuf:"varint,76,rep,packed,name=packed_int64,json=packedInt64" json:"packed_int64,omitempty"`
	PackedUint32     []uint32                        `protobuf:"varint,77,rep,packed,name=packed_uint32,json=packedUint32" json:"packed_uint32,omitempty"`
	PackedUint64     []uint64                        `protobuf:"varint,78,rep,packed,name=packed_uint64,json=packedUint64" json:"packed_uint64,omitempty"`
	PackedSint32     []int32                         `protobuf:"zigzag32,79,rep,packed,name=packed_sint32,json=packedSint32" json:"packed_sint32,omitempty"`
	PackedSint64     []int64                         `protobuf:"zigzag64,80,rep,packed,name=packed_sint64,json=packedSint64" json:"packed_sint64,omitempty"`
	PackedFixed32    []uint32                        `protobuf:"fixed32,81,rep,packed,name=packed_fixed32,json=packedFixed32" json:"packed_fixed32,omitempty"`
	PackedFixed64    []uint64                        `protobuf:"fixed64,82,rep,packed,name=packed_fixed64,json=packedFixed64" json:"packed_fixed64,omitempty"`
	PackedSfixed32   []int32                         `protobuf:"fixed32,83,rep,packed,name=packed_sfixed32,json=packedSfixed32" json:"packed_sfixed32,omitempty"`
	PackedSfixed64   []int64                         `protobuf:"fixed64,84,rep,packed,name=packed_sfixed64,json=packedSfixed64" json:"packed_sfixed64,omitempty"`
	PackedFloat      []float32                       `protobuf:"fixed32,85,rep,packed,name=packed_float,json=packedFloat" json:"packed_float,omitempty"`
	PackedDouble     []float64                       `protobuf:"fixed64,86,rep,packed,name=packed_double,json=packedDouble" json:"packed_double,omitempty"`
	PackedBool       []bool                          `protobuf:"varint,87,rep,packed,name=packed_bool,json=packedBool" json:"packed_bool,omitempty"`
	PackedNestedEnum []TestAllTypesProto2_NestedEnum `protobuf:"varint,88,rep,packed,name=packed_nested_enum,json=packedNestedEnum,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum" json:"packed_nested_enum,omitempty"`
	// Unpacked
	UnpackedInt32      []int32                         `protobuf:"varint,89,rep,name=unpacked_int32,json=unpackedInt32" json:"unpacked_int32,omitempty"`
	UnpackedInt64      []int64                         `protobuf:"varint,90,rep,name=unpacked_int64,json=unpackedInt64" json:"unpacked_int64,omitempty"`
	UnpackedUint32     []uint32                        `protobuf:"varint,91,rep,name=unpacked_uint32,json=unpackedUint32" json:"unpacked_uint32,omitempty"`
	UnpackedUint64     []uint64                        `protobuf:"varint,92,rep,name=unpacked_uint64,json=unpackedUint64" json:"unpacked_uint64,omitempty"`
	UnpackedSint32     []int32                         `protobuf:"zigzag32,93,rep,name=unpacked_sint32,json=unpackedSint32" json:"unpacked_sint32,omitempty"`
	UnpackedSint64     []int64                         `protobuf:"zigzag64,94,rep,name=unpacked_sint64,json=unpackedSint64" json:"unpacked_sint64,omitempty"`
	UnpackedFixed32    []uint32                        `protobuf:"fixed32,95,rep,name=unpacked_fixed32,json=unpackedFixed32" json:"unpacked_fixed32,omitempty"`
	UnpackedFixed64    []uint64                        `protobuf:"fixed64,96,rep,name=unpacked_fixed64,json=unpackedFixed64" json:"unpacked_fixed64,omitempty"`
	UnpackedSfixed32   []int32                         `protobuf:"fixed32,97,rep,name=unpacked_sfixed32,json=unpackedSfixed32" json:"unpacked_sfixed32,omitempty"`
	UnpackedSfixed64   []int64                         `protobuf:"fixed64,98,rep,name=unpacked_sfixed64,json=unpackedSfixed64" json:"unpacked_sfixed64,omitempty"`
	UnpackedFloat      []float32                       `protobuf:"fixed32,99,rep,name=unpacked_float,json=unpackedFloat" json:"unpacked_float,omitempty"`
	UnpackedDouble     []float64                       `protobuf:"fixed64,100,rep,name=unpacked_double,json=unpackedDouble" json:"unpacked_double,omitempty"`
	UnpackedBool       []bool                          `protobuf:"varint,101,rep,name=unpacked_bool,json=unpackedBool" json:"unpacked_bool,omitempty"`
	UnpackedNestedEnum []TestAllTypesProto2_NestedEnum `protobuf:"varint,102,rep,name=unpacked_nested_enum,json=unpackedNestedEnum,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum" json:"unpacked_nested_enum,omitempty"`
	// Map
	MapInt32Int32           map[int32]int32                              `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Int64           map[int64]int64                              `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Uint32         map[uint32]uint32                            `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Uint64         map[uint64]uint64                            `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Sint32         map[int32]int32                              `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapSint64Sint64         map[int64]int64                              `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapFixed32Fixed32       map[uint32]uint32                            `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapFixed64Fixed64       map[uint64]uint64                            `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapSfixed32Sfixed32     map[int32]int32                              `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapSfixed64Sfixed64     map[int64]int64                              `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapInt32Float           map[int32]float32                            `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapInt32Double          map[int32]float64                            `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolBool             map[bool]bool                                `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringString         map[string]string                            `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringBytes          map[string][]byte                            `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedMessage  map[string]*TestAllTypesProto2_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringForeignMessage map[string]*ForeignMessageProto2             `protobuf:"bytes,72,rep,name=map_string_foreign_message,json=mapStringForeignMessage" json:"map_string_foreign_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedEnum     map[string]TestAllTypesProto2_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum"`
	MapStringForeignEnum    map[string]ForeignEnumProto2                 `protobuf:"bytes,74,rep,name=map_string_foreign_enum,json=mapStringForeignEnum" json:"map_string_foreign_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.proto2.ForeignEnumProto2"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypesProto2_OneofUint32
	//	*TestAllTypesProto2_OneofNestedMessage
	//	*TestAllTypesProto2_OneofString
	//	*TestAllTypesProto2_OneofBytes
	//	*TestAllTypesProto2_OneofBool
	//	*TestAllTypesProto2_OneofUint64
	//	*TestAllTypesProto2_OneofFloat
	//	*TestAllTypesProto2_OneofDouble
	//	*TestAllTypesProto2_OneofEnum
	OneofField          isTestAllTypesProto2_OneofField         `protobuf_oneof:"oneof_field"`
	Data                *TestAllTypesProto2_Data                `protobuf:"group,201,opt,name=Data,json=data" json:"data,omitempty"`
	Multiwordgroupfield *TestAllTypesProto2_MultiWordGroupField `protobuf:"group,204,opt,name=MultiWordGroupField,json=multiwordgroupfield" json:"multiwordgroupfield,omitempty"`
	// default values
	DefaultInt32    *int32   `protobuf:"varint,241,opt,name=default_int32,json=defaultInt32,def=-123456789" json:"default_int32,omitempty"`
	DefaultInt64    *int64   `protobuf:"varint,242,opt,name=default_int64,json=defaultInt64,def=-9123456789123456789" json:"default_int64,omitempty"`
	DefaultUint32   *uint32  `protobuf:"varint,243,opt,name=default_uint32,json=defaultUint32,def=2123456789" json:"default_uint32,omitempty"`
	DefaultUint64   *uint64  `protobuf:"varint,244,opt,name=default_uint64,json=defaultUint64,def=10123456789123456789" json:"default_uint64,omitempty"`
	DefaultSint32   *int32   `protobuf:"zigzag32,245,opt,name=default_sint32,json=defaultSint32,def=-123456789" json:"default_sint32,omitempty"`
	DefaultSint64   *int64   `protobuf:"zigzag64,246,opt,name=default_sint64,json=defaultSint64,def=-9123456789123456789" json:"default_sint64,omitempty"`
	DefaultFixed32  *uint32  `protobuf:"fixed32,247,opt,name=default_fixed32,json=defaultFixed32,def=2123456789" json:"default_fixed32,omitempty"`
	DefaultFixed64  *uint64  `protobuf:"fixed64,248,opt,name=default_fixed64,json=defaultFixed64,def=10123456789123456789" json:"default_fixed64,omitempty"`
	DefaultSfixed32 *int32   `protobuf:"fixed32,249,opt,name=default_sfixed32,json=defaultSfixed32,def=-123456789" json:"default_sfixed32,omitempty"`
	DefaultSfixed64 *int64   `protobuf:"fixed64,250,opt,name=default_sfixed64,json=defaultSfixed64,def=-9123456789123456789" json:"default_sfixed64,omitempty"`
	DefaultFloat    *float32 `protobuf:"fixed32,251,opt,name=default_float,json=defaultFloat,def=9e+09" json:"default_float,omitempty"`
	DefaultDouble   *float64 `protobuf:"fixed64,252,opt,name=default_double,json=defaultDouble,def=7e+22" json:"default_double,omitempty"`
	DefaultBool     *bool    `protobuf:"varint,253,opt,name=default_bool,json=defaultBool,def=1" json:"default_bool,omitempty"`
	DefaultString   *string  `protobuf:"bytes,254,opt,name=default_string,json=defaultString,def=Rosebud" json:"default_string,omitempty"`
	DefaultBytes    []byte   `protobuf:"bytes,255,opt,name=default_bytes,json=defaultBytes,def=joshua" json:"default_bytes,omitempty"`
	// Test field-name-to-JSON-name convention.
	// (protobuf says names can be any valid C/C++ identifier.)
	Fieldname1    *int32 `protobuf:"varint,401,opt,name=fieldname1" json:"fieldname1,omitempty"`
	FieldName2    *int32 `protobuf:"varint,402,opt,name=field_name2,json=fieldName2" json:"field_name2,omitempty"`
	XFieldName3   *int32 `protobuf:"varint,403,opt,name=_field_name3,json=FieldName3" json:"_field_name3,omitempty"`
	Field_Name4_  *int32 `protobuf:"varint,404,opt,name=field__name4_,json=fieldName4" json:"field__name4_,omitempty"`
	Field0Name5   *int32 `protobuf:"varint,405,opt,name=field0name5" json:"field0name5,omitempty"`
	Field_0Name6  *int32 `protobuf:"varint,406,opt,name=field_0_name6,json=field0Name6" json:"field_0_name6,omitempty"`
	FieldName7    *int32 `protobuf:"varint,407,opt,name=fieldName7" json:"fieldName7,omitempty"`
	FieldName8    *int32 `protobuf:"varint,408,opt,name=FieldName8" json:"FieldName8,omitempty"`
	Field_Name9   *int32 `protobuf:"varint,409,opt,name=field_Name9,json=fieldName9" json:"field_Name9,omitempty"`
	Field_Name10  *int32 `protobuf:"varint,410,opt,name=Field_Name10,json=FieldName10" json:"Field_Name10,omitempty"`
	FIELD_NAME11  *int32 `protobuf:"varint,411,opt,name=FIELD_NAME11,json=FIELDNAME11" json:"FIELD_NAME11,omitempty"`
	FIELDName12   *int32 `protobuf:"varint,412,opt,name=FIELD_name12,json=FIELDName12" json:"FIELD_name12,omitempty"`
	XFieldName13  *int32 `protobuf:"varint,413,opt,name=__field_name13,json=FieldName13" json:"__field_name13,omitempty"`
	X_FieldName14 *int32 `protobuf:"varint,414,opt,name=__Field_name14,json=FieldName14" json:"__Field_name14,omitempty"`
	Field_Name15  *int32 `protobuf:"varint,415,opt,name=field__name15,json=fieldName15" json:"field__name15,omitempty"`
	Field__Name16 *int32 `protobuf:"varint,416,opt,name=field__Name16,json=fieldName16" json:"field__Name16,omitempty"`
	FieldName17__ *int32 `protobuf:"varint,417,opt,name=field_name17__,json=fieldName17" json:"field_name17__,omitempty"`
	FieldName18__ *int32 `protobuf:"varint,418,opt,name=Field_name18__,json=FieldName18" json:"Field_name18__,omitempty"`
}

// Default values for TestAllTypesProto2 fields.
const (
	Default_TestAllTypesProto2_DefaultInt32    = int32(-123456789)
	Default_TestAllTypesProto2_DefaultInt64    = int64(-9123456789123456789)
	Default_TestAllTypesProto2_DefaultUint32   = uint32(2123456789)
	Default_TestAllTypesProto2_DefaultUint64   = uint64(10123456789123456789)
	Default_TestAllTypesProto2_DefaultSint32   = int32(-123456789)
	Default_TestAllTypesProto2_DefaultSint64   = int64(-9123456789123456789)
	Default_TestAllTypesProto2_DefaultFixed32  = uint32(2123456789)
	Default_TestAllTypesProto2_DefaultFixed64  = uint64(10123456789123456789)
	Default_TestAllTypesProto2_DefaultSfixed32 = int32(-123456789)
	Default_TestAllTypesProto2_DefaultSfixed64 = int64(-9123456789123456789)
	Default_TestAllTypesProto2_DefaultFloat    = float32(8.999999488e+09)
	Default_TestAllTypesProto2_DefaultDouble   = float64(7e+22)
	Default_TestAllTypesProto2_DefaultBool     = bool(true)
	Default_TestAllTypesProto2_DefaultString   = string("Rosebud")
)

// Default values for TestAllTypesProto2 fields.
var (
	Default_TestAllTypesProto2_DefaultBytes = []byte("joshua")
)

func (x *TestAllTypesProto2) Reset() {
	*x = TestAllTypesProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2) ProtoMessage() {}

func (x *TestAllTypesProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypesProto2) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypesProto2) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *TestAllTypesProto2) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *TestAllTypesProto2) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypesProto2) GetOptionalNestedMessage() *TestAllTypesProto2_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetOptionalForeignMessage() *ForeignMessageProto2 {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetOptionalNestedEnum() TestAllTypesProto2_NestedEnum {
	if x != nil && x.OptionalNestedEnum != nil {
		return *x.OptionalNestedEnum
	}
	return TestAllTypesProto2_FOO
}

func (x *TestAllTypesProto2) GetOptionalForeignEnum() ForeignEnumProto2 {
	if x != nil && x.OptionalForeignEnum != nil {
		return *x.OptionalForeignEnum
	}
	return ForeignEnumProto2_FOREIGN_FOO
}

func (x *TestAllTypesProto2) GetOptionalStringPiece() string {
	if x != nil && x.OptionalStringPiece != nil {
		return *x.OptionalStringPiece
	}
	return ""
}

func (x *TestAllTypesProto2) GetOptionalCord() string {
	if x != nil && x.OptionalCord != nil {
		return *x.OptionalCord
	}
	return ""
}

func (x *TestAllTypesProto2) GetRecursiveMessage() *TestAllTypesProto2 {
	if x != nil {
		return x.RecursiveMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedNestedMessage() []*TestAllTypesProto2_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedForeignMessage() []*ForeignMessageProto2 {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedNestedEnum() []TestAllTypesProto2_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedForeignEnum() []ForeignEnumProto2 {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedStringPiece() []string {
	if x != nil {
		return x.RepeatedStringPiece
	}
	return nil
}

func (x *TestAllTypesProto2) GetRepeatedCord() []string {
	if x != nil {
		return x.RepeatedCord
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedInt32() []int32 {
	if x != nil {
		return x.PackedInt32
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedInt64() []int64 {
	if x != nil {
		return x.PackedInt64
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedUint32() []uint32 {
	if x != nil {
		return x.PackedUint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedUint64() []uint64 {
	if x != nil {
		return x.PackedUint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedSint32() []int32 {
	if x != nil {
		return x.PackedSint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedSint64() []int64 {
	if x != nil {
		return x.PackedSint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedFixed32() []uint32 {
	if x != nil {
		return x.PackedFixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedFixed64() []uint64 {
	if x != nil {
		return x.PackedFixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedSfixed32() []int32 {
	if x != nil {
		return x.PackedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedSfixed64() []int64 {
	if x != nil {
		return x.PackedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedFloat() []float32 {
	if x != nil {
		return x.PackedFloat
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedDouble() []float64 {
	if x != nil {
		return x.PackedDouble
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedBool() []bool {
	if x != nil {
		return x.PackedBool
	}
	return nil
}

func (x *TestAllTypesProto2) GetPackedNestedEnum() []TestAllTypesProto2_NestedEnum {
	if x != nil {
		return x.PackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedInt32() []int32 {
	if x != nil {
		return x.UnpackedInt32
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedInt64() []int64 {
	if x != nil {
		return x.UnpackedInt64
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedUint32() []uint32 {
	if x != nil {
		return x.UnpackedUint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedUint64() []uint64 {
	if x != nil {
		return x.UnpackedUint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedSint32() []int32 {
	if x != nil {
		return x.UnpackedSint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedSint64() []int64 {
	if x != nil {
		return x.UnpackedSint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedFixed32() []uint32 {
	if x != nil {
		return x.UnpackedFixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedFixed64() []uint64 {
	if x != nil {
		return x.UnpackedFixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedSfixed32() []int32 {
	if x != nil {
		return x.UnpackedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedSfixed64() []int64 {
	if x != nil {
		return x.UnpackedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedFloat() []float32 {
	if x != nil {
		return x.UnpackedFloat
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedDouble() []float64 {
	if x != nil {
		return x.UnpackedDouble
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedBool() []bool {
	if x != nil {
		return x.UnpackedBool
	}
	return nil
}

func (x *TestAllTypesProto2) GetUnpackedNestedEnum() []TestAllTypesProto2_NestedEnum {
	if x != nil {
		return x.UnpackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringNestedMessage() map[string]*TestAllTypesProto2_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringForeignMessage() map[string]*ForeignMessageProto2 {
	if x != nil {
		return x.MapStringForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringNestedEnum() map[string]TestAllTypesProto2_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto2) GetMapStringForeignEnum() map[string]ForeignEnumProto2 {
	if x != nil {
		return x.MapStringForeignEnum
	}
	return nil
}

func (m *TestAllTypesProto2) GetOneofField() isTestAllTypesProto2_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypesProto2) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypesProto2) GetOneofNestedMessage() *TestAllTypesProto2_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto2) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypesProto2) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypesProto2) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypesProto2) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypesProto2) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypesProto2) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypesProto2) GetOneofEnum() TestAllTypesProto2_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypesProto2_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypesProto2_FOO
}

func (x *TestAllTypesProto2) GetData() *TestAllTypesProto2_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TestAllTypesProto2) GetMultiwordgroupfield() *TestAllTypesProto2_MultiWordGroupField {
	if x != nil {
		return x.Multiwordgroupfield
	}
	return nil
}

func (x *TestAllTypesProto2) GetDefaultInt32() int32 {
	if x != nil && x.DefaultInt32 != nil {
		return *x.DefaultInt32
	}
	return Default_TestAllTypesProto2_DefaultInt32
}

func (x *TestAllTypesProto2) GetDefaultInt64() int64 {
	if x != nil && x.DefaultInt64 != nil {
		return *x.DefaultInt64
	}
	return Default_TestAllTypesProto2_DefaultInt64
}

func (x *TestAllTypesProto2) GetDefaultUint32() uint32 {
	if x != nil && x.DefaultUint32 != nil {
		return *x.DefaultUint32
	}
	return Default_TestAllTypesProto2_DefaultUint32
}

func (x *TestAllTypesProto2) GetDefaultUint64() uint64 {
	if x != nil && x.DefaultUint64 != nil {
		return *x.DefaultUint64
	}
	return Default_TestAllTypesProto2_DefaultUint64
}

func (x *TestAllTypesProto2) GetDefaultSint32() int32 {
	if x != nil && x.DefaultSint32 != nil {
		return *x.DefaultSint32
	}
	return Default_TestAllTypesProto2_DefaultSint32
}

func (x *TestAllTypesProto2) GetDefaultSint64() int64 {
	if x != nil && x.DefaultSint64 != nil {
		return *x.DefaultSint64
	}
	return Default_TestAllTypesProto2_DefaultSint64
}

func (x *TestAllTypesProto2) GetDefaultFixed32() uint32 {
	if x != nil && x.DefaultFixed32 != nil {
		return *x.DefaultFixed32
	}
	return Default_TestAllTypesProto2_DefaultFixed32
}

func (x *TestAllTypesProto2) GetDefaultFixed64() uint64 {
	if x != nil && x.DefaultFixed64 != nil {
		return *x.DefaultFixed64
	}
	return Default_TestAllTypesProto2_DefaultFixed64
}

func (x *TestAllTypesProto2) GetDefaultSfixed32() int32 {
	if x != nil && x.DefaultSfixed32 != nil {
		return *x.DefaultSfixed32
	}
	return Default_TestAllTypesProto2_DefaultSfixed32
}

func (x *TestAllTypesProto2) GetDefaultSfixed64() int64 {
	if x != nil && x.DefaultSfixed64 != nil {
		return *x.DefaultSfixed64
	}
	return Default_TestAllTypesProto2_DefaultSfixed64
}

func (x *TestAllTypesProto2) GetDefaultFloat() float32 {
	if x != nil && x.DefaultFloat != nil {
		return *x.DefaultFloat
	}
	return Default_TestAllTypesProto2_DefaultFloat
}

func (x *TestAllTypesProto2) GetDefaultDouble() float64 {
	if x != nil && x.DefaultDouble != nil {
		return *x.DefaultDouble
	}
	return Default_TestAllTypesProto2_DefaultDouble
}

func (x *TestAllTypesProto2) GetDefaultBool() bool {
	if x != nil && x.DefaultBool != nil {
		return *x.DefaultBool
	}
	return Default_TestAllTypesProto2_DefaultBool
}

func (x *TestAllTypesProto2) GetDefaultString() string {
	if x != nil && x.DefaultString != nil {
		return *x.DefaultString
	}
	return Default_TestAllTypesProto2_DefaultString
}

func (x *TestAllTypesProto2) GetDefaultBytes() []byte {
	if x != nil && x.DefaultBytes != nil {
		return x.DefaultBytes
	}
	return append([]byte(nil), Default_TestAllTypesProto2_DefaultBytes...)
}

func (x *TestAllTypesProto2) GetFieldname1() int32 {
	if x != nil && x.Fieldname1 != nil {
		return *x.Fieldname1
	}
	return 0
}

func (x *TestAllTypesProto2) GetFieldName2() int32 {
	if x != nil && x.FieldName2 != nil {
		return *x.FieldName2
	}
	return 0
}

func (x *TestAllTypesProto2) GetXFieldName3() int32 {
	if x != nil && x.XFieldName3 != nil {
		return *x.XFieldName3
	}
	return 0
}

func (x *TestAllTypesProto2) GetField_Name4_() int32 {
	if x != nil && x.Field_Name4_ != nil {
		return *x.Field_Name4_
	}
	return 0
}

func (x *TestAllTypesProto2) GetField0Name5() int32 {
	if x != nil && x.Field0Name5 != nil {
		return *x.Field0Name5
	}
	return 0
}

func (x *TestAllTypesProto2) GetField_0Name6() int32 {
	if x != nil && x.Field_0Name6 != nil {
		return *x.Field_0Name6
	}
	return 0
}

func (x *TestAllTypesProto2) GetFieldName7() int32 {
	if x != nil && x.FieldName7 != nil {
		return *x.FieldName7
	}
	return 0
}

func (x *TestAllTypesProto2) GetFieldName8() int32 {
	if x != nil && x.FieldName8 != nil {
		return *x.FieldName8
	}
	return 0
}

func (x *TestAllTypesProto2) GetField_Name9() int32 {
	if x != nil && x.Field_Name9 != nil {
		return *x.Field_Name9
	}
	return 0
}

func (x *TestAllTypesProto2) GetField_Name10() int32 {
	if x != nil && x.Field_Name10 != nil {
		return *x.Field_Name10
	}
	return 0
}

func (x *TestAllTypesProto2) GetFIELD_NAME11() int32 {
	if x != nil && x.FIELD_NAME11 != nil {
		return *x.FIELD_NAME11
	}
	return 0
}

func (x *TestAllTypesProto2) GetFIELDName12() int32 {
	if x != nil && x.FIELDName12 != nil {
		return *x.FIELDName12
	}
	return 0
}

func (x *TestAllTypesProto2) GetXFieldName13() int32 {
	if x != nil && x.XFieldName13 != nil {
		return *x.XFieldName13
	}
	return 0
}

func (x *TestAllTypesProto2) GetX_FieldName14() int32 {
	if x != nil && x.X_FieldName14 != nil {
		return *x.X_FieldName14
	}
	return 0
}

func (x *TestAllTypesProto2) GetField_Name15() int32 {
	if x != nil && x.Field_Name15 != nil {
		return *x.Field_Name15
	}
	return 0
}

func (x *TestAllTypesProto2) GetField__Name16() int32 {
	if x != nil && x.Field__Name16 != nil {
		return *x.Field__Name16
	}
	return 0
}

func (x *TestAllTypesProto2) GetFieldName17__() int32 {
	if x != nil && x.FieldName17__ != nil {
		return *x.FieldName17__
	}
	return 0
}

func (x *TestAllTypesProto2) GetFieldName18__() int32 {
	if x != nil && x.FieldName18__ != nil {
		return *x.FieldName18__
	}
	return 0
}

type isTestAllTypesProto2_OneofField interface {
	isTestAllTypesProto2_OneofField()
}

type TestAllTypesProto2_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type TestAllTypesProto2_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypesProto2_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,oneof"`
}

type TestAllTypesProto2_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,oneof"`
}

type TestAllTypesProto2_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type TestAllTypesProto2_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type TestAllTypesProto2_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type TestAllTypesProto2_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type TestAllTypesProto2_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type TestAllTypesProto2_OneofEnum struct {
	OneofEnum TestAllTypesProto2_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,enum=protobuf_test_messages.editions.proto2.TestAllTypesProto2_NestedEnum,oneof"`
}

func (*TestAllTypesProto2_OneofUint32) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofNestedMessage) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofString) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofBytes) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofBool) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofUint64) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofFloat) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofDouble) isTestAllTypesProto2_OneofField() {}

func (*TestAllTypesProto2_OneofEnum) isTestAllTypesProto2_OneofField() {}

type ForeignMessageProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
}

func (x *ForeignMessageProto2) Reset() {
	*x = ForeignMessageProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessageProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessageProto2) ProtoMessage() {}

func (x *ForeignMessageProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessageProto2.ProtoReflect.Descriptor instead.
func (*ForeignMessageProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{1}
}

func (x *ForeignMessageProto2) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

type GroupField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInt32  *int32  `protobuf:"varint,122,opt,name=group_int32,json=groupInt32" json:"group_int32,omitempty"`
	GroupUint32 *uint32 `protobuf:"varint,123,opt,name=group_uint32,json=groupUint32" json:"group_uint32,omitempty"`
}

func (x *GroupField) Reset() {
	*x = GroupField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupField) ProtoMessage() {}

func (x *GroupField) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupField.ProtoReflect.Descriptor instead.
func (*GroupField) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{2}
}

func (x *GroupField) GetGroupInt32() int32 {
	if x != nil && x.GroupInt32 != nil {
		return *x.GroupInt32
	}
	return 0
}

func (x *GroupField) GetGroupUint32() uint32 {
	if x != nil && x.GroupUint32 != nil {
		return *x.GroupUint32
	}
	return 0
}

type UnknownToTestAllTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionalInt32  *int32                               `protobuf:"varint,1001,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalString *string                              `protobuf:"bytes,1002,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	NestedMessage  *ForeignMessageProto2                `protobuf:"bytes,1003,opt,name=nested_message,json=nestedMessage" json:"nested_message,omitempty"`
	Optionalgroup  *UnknownToTestAllTypes_OptionalGroup `protobuf:"group,1004,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	OptionalBool   *bool                                `protobuf:"varint,1006,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	RepeatedInt32  []int32                              `protobuf:"varint,1011,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
}

func (x *UnknownToTestAllTypes) Reset() {
	*x = UnknownToTestAllTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnknownToTestAllTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnknownToTestAllTypes) ProtoMessage() {}

func (x *UnknownToTestAllTypes) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnknownToTestAllTypes.ProtoReflect.Descriptor instead.
func (*UnknownToTestAllTypes) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{3}
}

func (x *UnknownToTestAllTypes) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *UnknownToTestAllTypes) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *UnknownToTestAllTypes) GetNestedMessage() *ForeignMessageProto2 {
	if x != nil {
		return x.NestedMessage
	}
	return nil
}

func (x *UnknownToTestAllTypes) GetOptionalgroup() *UnknownToTestAllTypes_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *UnknownToTestAllTypes) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *UnknownToTestAllTypes) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

type NullHypothesisProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NullHypothesisProto2) Reset() {
	*x = NullHypothesisProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NullHypothesisProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NullHypothesisProto2) ProtoMessage() {}

func (x *NullHypothesisProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NullHypothesisProto2.ProtoReflect.Descriptor instead.
func (*NullHypothesisProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{4}
}

type EnumOnlyProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnumOnlyProto2) Reset() {
	*x = EnumOnlyProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumOnlyProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumOnlyProto2) ProtoMessage() {}

func (x *EnumOnlyProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumOnlyProto2.ProtoReflect.Descriptor instead.
func (*EnumOnlyProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{5}
}

type OneStringProto2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *string `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
}

func (x *OneStringProto2) Reset() {
	*x = OneStringProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OneStringProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OneStringProto2) ProtoMessage() {}

func (x *OneStringProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OneStringProto2.ProtoReflect.Descriptor instead.
func (*OneStringProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{6}
}

func (x *OneStringProto2) GetData() string {
	if x != nil && x.Data != nil {
		return *x.Data
	}
	return ""
}

type ProtoWithKeywords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Inline   *int32   `protobuf:"varint,1,opt,name=inline" json:"inline,omitempty"`
	Concept  *string  `protobuf:"bytes,2,opt,name=concept" json:"concept,omitempty"`
	Requires []string `protobuf:"bytes,3,rep,name=requires" json:"requires,omitempty"`
}

func (x *ProtoWithKeywords) Reset() {
	*x = ProtoWithKeywords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProtoWithKeywords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtoWithKeywords) ProtoMessage() {}

func (x *ProtoWithKeywords) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtoWithKeywords.ProtoReflect.Descriptor instead.
func (*ProtoWithKeywords) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{7}
}

func (x *ProtoWithKeywords) GetInline() int32 {
	if x != nil && x.Inline != nil {
		return *x.Inline
	}
	return 0
}

func (x *ProtoWithKeywords) GetConcept() string {
	if x != nil && x.Concept != nil {
		return *x.Concept
	}
	return ""
}

func (x *ProtoWithKeywords) GetRequires() []string {
	if x != nil {
		return x.Requires
	}
	return nil
}

type TestAllRequiredTypesProto2 struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Singular
	RequiredInt32            *int32                                    `protobuf:"varint,1,req,name=required_int32,json=requiredInt32" json:"required_int32,omitempty"`
	RequiredInt64            *int64                                    `protobuf:"varint,2,req,name=required_int64,json=requiredInt64" json:"required_int64,omitempty"`
	RequiredUint32           *uint32                                   `protobuf:"varint,3,req,name=required_uint32,json=requiredUint32" json:"required_uint32,omitempty"`
	RequiredUint64           *uint64                                   `protobuf:"varint,4,req,name=required_uint64,json=requiredUint64" json:"required_uint64,omitempty"`
	RequiredSint32           *int32                                    `protobuf:"zigzag32,5,req,name=required_sint32,json=requiredSint32" json:"required_sint32,omitempty"`
	RequiredSint64           *int64                                    `protobuf:"zigzag64,6,req,name=required_sint64,json=requiredSint64" json:"required_sint64,omitempty"`
	RequiredFixed32          *uint32                                   `protobuf:"fixed32,7,req,name=required_fixed32,json=requiredFixed32" json:"required_fixed32,omitempty"`
	RequiredFixed64          *uint64                                   `protobuf:"fixed64,8,req,name=required_fixed64,json=requiredFixed64" json:"required_fixed64,omitempty"`
	RequiredSfixed32         *int32                                    `protobuf:"fixed32,9,req,name=required_sfixed32,json=requiredSfixed32" json:"required_sfixed32,omitempty"`
	RequiredSfixed64         *int64                                    `protobuf:"fixed64,10,req,name=required_sfixed64,json=requiredSfixed64" json:"required_sfixed64,omitempty"`
	RequiredFloat            *float32                                  `protobuf:"fixed32,11,req,name=required_float,json=requiredFloat" json:"required_float,omitempty"`
	RequiredDouble           *float64                                  `protobuf:"fixed64,12,req,name=required_double,json=requiredDouble" json:"required_double,omitempty"`
	RequiredBool             *bool                                     `protobuf:"varint,13,req,name=required_bool,json=requiredBool" json:"required_bool,omitempty"`
	RequiredString           *string                                   `protobuf:"bytes,14,req,name=required_string,json=requiredString" json:"required_string,omitempty"`
	RequiredBytes            []byte                                    `protobuf:"bytes,15,req,name=required_bytes,json=requiredBytes" json:"required_bytes,omitempty"`
	RequiredNestedMessage    *TestAllRequiredTypesProto2_NestedMessage `protobuf:"bytes,18,req,name=required_nested_message,json=requiredNestedMessage" json:"required_nested_message,omitempty"`
	RequiredForeignMessage   *ForeignMessageProto2                     `protobuf:"bytes,19,req,name=required_foreign_message,json=requiredForeignMessage" json:"required_foreign_message,omitempty"`
	RequiredNestedEnum       *TestAllRequiredTypesProto2_NestedEnum    `protobuf:"varint,21,req,name=required_nested_enum,json=requiredNestedEnum,enum=protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2_NestedEnum" json:"required_nested_enum,omitempty"`
	RequiredForeignEnum      *ForeignEnumProto2                        `protobuf:"varint,22,req,name=required_foreign_enum,json=requiredForeignEnum,enum=protobuf_test_messages.editions.proto2.ForeignEnumProto2" json:"required_foreign_enum,omitempty"`
	RequiredStringPiece      *string                                   `protobuf:"bytes,24,req,name=required_string_piece,json=requiredStringPiece" json:"required_string_piece,omitempty"`
	RequiredCord             *string                                   `protobuf:"bytes,25,req,name=required_cord,json=requiredCord" json:"required_cord,omitempty"`
	RecursiveMessage         *TestAllRequiredTypesProto2               `protobuf:"bytes,27,req,name=recursive_message,json=recursiveMessage" json:"recursive_message,omitempty"`
	OptionalRecursiveMessage *TestAllRequiredTypesProto2               `protobuf:"bytes,28,opt,name=optional_recursive_message,json=optionalRecursiveMessage" json:"optional_recursive_message,omitempty"`
	Data                     *TestAllRequiredTypesProto2_Data          `protobuf:"group,201,req,name=Data,json=data" json:"data,omitempty"`
	// default values
	DefaultInt32    *int32   `protobuf:"varint,241,req,name=default_int32,json=defaultInt32,def=-123456789" json:"default_int32,omitempty"`
	DefaultInt64    *int64   `protobuf:"varint,242,req,name=default_int64,json=defaultInt64,def=-9123456789123456789" json:"default_int64,omitempty"`
	DefaultUint32   *uint32  `protobuf:"varint,243,req,name=default_uint32,json=defaultUint32,def=2123456789" json:"default_uint32,omitempty"`
	DefaultUint64   *uint64  `protobuf:"varint,244,req,name=default_uint64,json=defaultUint64,def=10123456789123456789" json:"default_uint64,omitempty"`
	DefaultSint32   *int32   `protobuf:"zigzag32,245,req,name=default_sint32,json=defaultSint32,def=-123456789" json:"default_sint32,omitempty"`
	DefaultSint64   *int64   `protobuf:"zigzag64,246,req,name=default_sint64,json=defaultSint64,def=-9123456789123456789" json:"default_sint64,omitempty"`
	DefaultFixed32  *uint32  `protobuf:"fixed32,247,req,name=default_fixed32,json=defaultFixed32,def=2123456789" json:"default_fixed32,omitempty"`
	DefaultFixed64  *uint64  `protobuf:"fixed64,248,req,name=default_fixed64,json=defaultFixed64,def=10123456789123456789" json:"default_fixed64,omitempty"`
	DefaultSfixed32 *int32   `protobuf:"fixed32,249,req,name=default_sfixed32,json=defaultSfixed32,def=-123456789" json:"default_sfixed32,omitempty"`
	DefaultSfixed64 *int64   `protobuf:"fixed64,250,req,name=default_sfixed64,json=defaultSfixed64,def=-9123456789123456789" json:"default_sfixed64,omitempty"`
	DefaultFloat    *float32 `protobuf:"fixed32,251,req,name=default_float,json=defaultFloat,def=9e+09" json:"default_float,omitempty"`
	DefaultDouble   *float64 `protobuf:"fixed64,252,req,name=default_double,json=defaultDouble,def=7e+22" json:"default_double,omitempty"`
	DefaultBool     *bool    `protobuf:"varint,253,req,name=default_bool,json=defaultBool,def=1" json:"default_bool,omitempty"`
	DefaultString   *string  `protobuf:"bytes,254,req,name=default_string,json=defaultString,def=Rosebud" json:"default_string,omitempty"`
	DefaultBytes    []byte   `protobuf:"bytes,255,req,name=default_bytes,json=defaultBytes,def=joshua" json:"default_bytes,omitempty"`
}

// Default values for TestAllRequiredTypesProto2 fields.
const (
	Default_TestAllRequiredTypesProto2_DefaultInt32    = int32(-123456789)
	Default_TestAllRequiredTypesProto2_DefaultInt64    = int64(-9123456789123456789)
	Default_TestAllRequiredTypesProto2_DefaultUint32   = uint32(2123456789)
	Default_TestAllRequiredTypesProto2_DefaultUint64   = uint64(10123456789123456789)
	Default_TestAllRequiredTypesProto2_DefaultSint32   = int32(-123456789)
	Default_TestAllRequiredTypesProto2_DefaultSint64   = int64(-9123456789123456789)
	Default_TestAllRequiredTypesProto2_DefaultFixed32  = uint32(2123456789)
	Default_TestAllRequiredTypesProto2_DefaultFixed64  = uint64(10123456789123456789)
	Default_TestAllRequiredTypesProto2_DefaultSfixed32 = int32(-123456789)
	Default_TestAllRequiredTypesProto2_DefaultSfixed64 = int64(-9123456789123456789)
	Default_TestAllRequiredTypesProto2_DefaultFloat    = float32(8.999999488e+09)
	Default_TestAllRequiredTypesProto2_DefaultDouble   = float64(7e+22)
	Default_TestAllRequiredTypesProto2_DefaultBool     = bool(true)
	Default_TestAllRequiredTypesProto2_DefaultString   = string("Rosebud")
)

// Default values for TestAllRequiredTypesProto2 fields.
var (
	Default_TestAllRequiredTypesProto2_DefaultBytes = []byte("joshua")
)

func (x *TestAllRequiredTypesProto2) Reset() {
	*x = TestAllRequiredTypesProto2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8}
}

func (x *TestAllRequiredTypesProto2) GetRequiredInt32() int32 {
	if x != nil && x.RequiredInt32 != nil {
		return *x.RequiredInt32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredInt64() int64 {
	if x != nil && x.RequiredInt64 != nil {
		return *x.RequiredInt64
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredUint32() uint32 {
	if x != nil && x.RequiredUint32 != nil {
		return *x.RequiredUint32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredUint64() uint64 {
	if x != nil && x.RequiredUint64 != nil {
		return *x.RequiredUint64
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredSint32() int32 {
	if x != nil && x.RequiredSint32 != nil {
		return *x.RequiredSint32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredSint64() int64 {
	if x != nil && x.RequiredSint64 != nil {
		return *x.RequiredSint64
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredFixed32() uint32 {
	if x != nil && x.RequiredFixed32 != nil {
		return *x.RequiredFixed32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredFixed64() uint64 {
	if x != nil && x.RequiredFixed64 != nil {
		return *x.RequiredFixed64
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredSfixed32() int32 {
	if x != nil && x.RequiredSfixed32 != nil {
		return *x.RequiredSfixed32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredSfixed64() int64 {
	if x != nil && x.RequiredSfixed64 != nil {
		return *x.RequiredSfixed64
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredFloat() float32 {
	if x != nil && x.RequiredFloat != nil {
		return *x.RequiredFloat
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredDouble() float64 {
	if x != nil && x.RequiredDouble != nil {
		return *x.RequiredDouble
	}
	return 0
}

func (x *TestAllRequiredTypesProto2) GetRequiredBool() bool {
	if x != nil && x.RequiredBool != nil {
		return *x.RequiredBool
	}
	return false
}

func (x *TestAllRequiredTypesProto2) GetRequiredString() string {
	if x != nil && x.RequiredString != nil {
		return *x.RequiredString
	}
	return ""
}

func (x *TestAllRequiredTypesProto2) GetRequiredBytes() []byte {
	if x != nil {
		return x.RequiredBytes
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetRequiredNestedMessage() *TestAllRequiredTypesProto2_NestedMessage {
	if x != nil {
		return x.RequiredNestedMessage
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetRequiredForeignMessage() *ForeignMessageProto2 {
	if x != nil {
		return x.RequiredForeignMessage
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetRequiredNestedEnum() TestAllRequiredTypesProto2_NestedEnum {
	if x != nil && x.RequiredNestedEnum != nil {
		return *x.RequiredNestedEnum
	}
	return TestAllRequiredTypesProto2_FOO
}

func (x *TestAllRequiredTypesProto2) GetRequiredForeignEnum() ForeignEnumProto2 {
	if x != nil && x.RequiredForeignEnum != nil {
		return *x.RequiredForeignEnum
	}
	return ForeignEnumProto2_FOREIGN_FOO
}

func (x *TestAllRequiredTypesProto2) GetRequiredStringPiece() string {
	if x != nil && x.RequiredStringPiece != nil {
		return *x.RequiredStringPiece
	}
	return ""
}

func (x *TestAllRequiredTypesProto2) GetRequiredCord() string {
	if x != nil && x.RequiredCord != nil {
		return *x.RequiredCord
	}
	return ""
}

func (x *TestAllRequiredTypesProto2) GetRecursiveMessage() *TestAllRequiredTypesProto2 {
	if x != nil {
		return x.RecursiveMessage
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetOptionalRecursiveMessage() *TestAllRequiredTypesProto2 {
	if x != nil {
		return x.OptionalRecursiveMessage
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetData() *TestAllRequiredTypesProto2_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TestAllRequiredTypesProto2) GetDefaultInt32() int32 {
	if x != nil && x.DefaultInt32 != nil {
		return *x.DefaultInt32
	}
	return Default_TestAllRequiredTypesProto2_DefaultInt32
}

func (x *TestAllRequiredTypesProto2) GetDefaultInt64() int64 {
	if x != nil && x.DefaultInt64 != nil {
		return *x.DefaultInt64
	}
	return Default_TestAllRequiredTypesProto2_DefaultInt64
}

func (x *TestAllRequiredTypesProto2) GetDefaultUint32() uint32 {
	if x != nil && x.DefaultUint32 != nil {
		return *x.DefaultUint32
	}
	return Default_TestAllRequiredTypesProto2_DefaultUint32
}

func (x *TestAllRequiredTypesProto2) GetDefaultUint64() uint64 {
	if x != nil && x.DefaultUint64 != nil {
		return *x.DefaultUint64
	}
	return Default_TestAllRequiredTypesProto2_DefaultUint64
}

func (x *TestAllRequiredTypesProto2) GetDefaultSint32() int32 {
	if x != nil && x.DefaultSint32 != nil {
		return *x.DefaultSint32
	}
	return Default_TestAllRequiredTypesProto2_DefaultSint32
}

func (x *TestAllRequiredTypesProto2) GetDefaultSint64() int64 {
	if x != nil && x.DefaultSint64 != nil {
		return *x.DefaultSint64
	}
	return Default_TestAllRequiredTypesProto2_DefaultSint64
}

func (x *TestAllRequiredTypesProto2) GetDefaultFixed32() uint32 {
	if x != nil && x.DefaultFixed32 != nil {
		return *x.DefaultFixed32
	}
	return Default_TestAllRequiredTypesProto2_DefaultFixed32
}

func (x *TestAllRequiredTypesProto2) GetDefaultFixed64() uint64 {
	if x != nil && x.DefaultFixed64 != nil {
		return *x.DefaultFixed64
	}
	return Default_TestAllRequiredTypesProto2_DefaultFixed64
}

func (x *TestAllRequiredTypesProto2) GetDefaultSfixed32() int32 {
	if x != nil && x.DefaultSfixed32 != nil {
		return *x.DefaultSfixed32
	}
	return Default_TestAllRequiredTypesProto2_DefaultSfixed32
}

func (x *TestAllRequiredTypesProto2) GetDefaultSfixed64() int64 {
	if x != nil && x.DefaultSfixed64 != nil {
		return *x.DefaultSfixed64
	}
	return Default_TestAllRequiredTypesProto2_DefaultSfixed64
}

func (x *TestAllRequiredTypesProto2) GetDefaultFloat() float32 {
	if x != nil && x.DefaultFloat != nil {
		return *x.DefaultFloat
	}
	return Default_TestAllRequiredTypesProto2_DefaultFloat
}

func (x *TestAllRequiredTypesProto2) GetDefaultDouble() float64 {
	if x != nil && x.DefaultDouble != nil {
		return *x.DefaultDouble
	}
	return Default_TestAllRequiredTypesProto2_DefaultDouble
}

func (x *TestAllRequiredTypesProto2) GetDefaultBool() bool {
	if x != nil && x.DefaultBool != nil {
		return *x.DefaultBool
	}
	return Default_TestAllRequiredTypesProto2_DefaultBool
}

func (x *TestAllRequiredTypesProto2) GetDefaultString() string {
	if x != nil && x.DefaultString != nil {
		return *x.DefaultString
	}
	return Default_TestAllRequiredTypesProto2_DefaultString
}

func (x *TestAllRequiredTypesProto2) GetDefaultBytes() []byte {
	if x != nil && x.DefaultBytes != nil {
		return x.DefaultBytes
	}
	return append([]byte(nil), Default_TestAllRequiredTypesProto2_DefaultBytes...)
}

type TestAllTypesProto2_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32              `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllTypesProto2 `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllTypesProto2_NestedMessage) Reset() {
	*x = TestAllTypesProto2_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_NestedMessage) ProtoMessage() {}

func (x *TestAllTypesProto2_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_NestedMessage) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypesProto2_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypesProto2_NestedMessage) GetCorecursive() *TestAllTypesProto2 {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

// groups
type TestAllTypesProto2_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInt32  *int32  `protobuf:"varint,202,opt,name=group_int32,json=groupInt32" json:"group_int32,omitempty"`
	GroupUint32 *uint32 `protobuf:"varint,203,opt,name=group_uint32,json=groupUint32" json:"group_uint32,omitempty"`
}

func (x *TestAllTypesProto2_Data) Reset() {
	*x = TestAllTypesProto2_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_Data) ProtoMessage() {}

func (x *TestAllTypesProto2_Data) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_Data.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_Data) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 20}
}

func (x *TestAllTypesProto2_Data) GetGroupInt32() int32 {
	if x != nil && x.GroupInt32 != nil {
		return *x.GroupInt32
	}
	return 0
}

func (x *TestAllTypesProto2_Data) GetGroupUint32() uint32 {
	if x != nil && x.GroupUint32 != nil {
		return *x.GroupUint32
	}
	return 0
}

type TestAllTypesProto2_MultiWordGroupField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInt32  *int32  `protobuf:"varint,205,opt,name=group_int32,json=groupInt32" json:"group_int32,omitempty"`
	GroupUint32 *uint32 `protobuf:"varint,206,opt,name=group_uint32,json=groupUint32" json:"group_uint32,omitempty"`
}

func (x *TestAllTypesProto2_MultiWordGroupField) Reset() {
	*x = TestAllTypesProto2_MultiWordGroupField{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_MultiWordGroupField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_MultiWordGroupField) ProtoMessage() {}

func (x *TestAllTypesProto2_MultiWordGroupField) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_MultiWordGroupField.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_MultiWordGroupField) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 21}
}

func (x *TestAllTypesProto2_MultiWordGroupField) GetGroupInt32() int32 {
	if x != nil && x.GroupInt32 != nil {
		return *x.GroupInt32
	}
	return 0
}

func (x *TestAllTypesProto2_MultiWordGroupField) GetGroupUint32() uint32 {
	if x != nil && x.GroupUint32 != nil {
		return *x.GroupUint32
	}
	return 0
}

// message_set test case.
type TestAllTypesProto2_MessageSetCorrect struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestAllTypesProto2_MessageSetCorrect) Reset() {
	*x = TestAllTypesProto2_MessageSetCorrect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_MessageSetCorrect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_MessageSetCorrect) ProtoMessage() {}

func (x *TestAllTypesProto2_MessageSetCorrect) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_MessageSetCorrect.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_MessageSetCorrect) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 22}
}

type TestAllTypesProto2_MessageSetCorrectExtension1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Str *string `protobuf:"bytes,25,opt,name=str" json:"str,omitempty"`
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension1) Reset() {
	*x = TestAllTypesProto2_MessageSetCorrectExtension1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_MessageSetCorrectExtension1) ProtoMessage() {}

func (x *TestAllTypesProto2_MessageSetCorrectExtension1) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_MessageSetCorrectExtension1.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_MessageSetCorrectExtension1) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 23}
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension1) GetStr() string {
	if x != nil && x.Str != nil {
		return *x.Str
	}
	return ""
}

type TestAllTypesProto2_MessageSetCorrectExtension2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	I *int32 `protobuf:"varint,9,opt,name=i" json:"i,omitempty"`
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension2) Reset() {
	*x = TestAllTypesProto2_MessageSetCorrectExtension2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto2_MessageSetCorrectExtension2) ProtoMessage() {}

func (x *TestAllTypesProto2_MessageSetCorrectExtension2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto2_MessageSetCorrectExtension2.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto2_MessageSetCorrectExtension2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{0, 24}
}

func (x *TestAllTypesProto2_MessageSetCorrectExtension2) GetI() int32 {
	if x != nil && x.I != nil {
		return *x.I
	}
	return 0
}

type UnknownToTestAllTypes_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A *int32 `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
}

func (x *UnknownToTestAllTypes_OptionalGroup) Reset() {
	*x = UnknownToTestAllTypes_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnknownToTestAllTypes_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnknownToTestAllTypes_OptionalGroup) ProtoMessage() {}

func (x *UnknownToTestAllTypes_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnknownToTestAllTypes_OptionalGroup.ProtoReflect.Descriptor instead.
func (*UnknownToTestAllTypes_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{3, 0}
}

func (x *UnknownToTestAllTypes_OptionalGroup) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

type TestAllRequiredTypesProto2_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A                   *int32                      `protobuf:"varint,1,req,name=a" json:"a,omitempty"`
	Corecursive         *TestAllRequiredTypesProto2 `protobuf:"bytes,2,req,name=corecursive" json:"corecursive,omitempty"`
	OptionalCorecursive *TestAllRequiredTypesProto2 `protobuf:"bytes,3,opt,name=optional_corecursive,json=optionalCorecursive" json:"optional_corecursive,omitempty"`
}

func (x *TestAllRequiredTypesProto2_NestedMessage) Reset() {
	*x = TestAllRequiredTypesProto2_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2_NestedMessage) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2_NestedMessage) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 0}
}

func (x *TestAllRequiredTypesProto2_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllRequiredTypesProto2_NestedMessage) GetCorecursive() *TestAllRequiredTypesProto2 {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

func (x *TestAllRequiredTypesProto2_NestedMessage) GetOptionalCorecursive() *TestAllRequiredTypesProto2 {
	if x != nil {
		return x.OptionalCorecursive
	}
	return nil
}

// groups
type TestAllRequiredTypesProto2_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInt32  *int32  `protobuf:"varint,202,req,name=group_int32,json=groupInt32" json:"group_int32,omitempty"`
	GroupUint32 *uint32 `protobuf:"varint,203,req,name=group_uint32,json=groupUint32" json:"group_uint32,omitempty"`
}

func (x *TestAllRequiredTypesProto2_Data) Reset() {
	*x = TestAllRequiredTypesProto2_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2_Data) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2_Data) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_Data.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2_Data) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 1}
}

func (x *TestAllRequiredTypesProto2_Data) GetGroupInt32() int32 {
	if x != nil && x.GroupInt32 != nil {
		return *x.GroupInt32
	}
	return 0
}

func (x *TestAllRequiredTypesProto2_Data) GetGroupUint32() uint32 {
	if x != nil && x.GroupUint32 != nil {
		return *x.GroupUint32
	}
	return 0
}

// message_set test case.
type TestAllRequiredTypesProto2_MessageSetCorrect struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrect) Reset() {
	*x = TestAllRequiredTypesProto2_MessageSetCorrect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2_MessageSetCorrect) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2_MessageSetCorrect) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_MessageSetCorrect.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2_MessageSetCorrect) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 2}
}

type TestAllRequiredTypesProto2_MessageSetCorrectExtension1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Str *string `protobuf:"bytes,25,req,name=str" json:"str,omitempty"`
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension1) Reset() {
	*x = TestAllRequiredTypesProto2_MessageSetCorrectExtension1{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2_MessageSetCorrectExtension1) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension1) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_MessageSetCorrectExtension1.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2_MessageSetCorrectExtension1) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 3}
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension1) GetStr() string {
	if x != nil && x.Str != nil {
		return *x.Str
	}
	return ""
}

type TestAllRequiredTypesProto2_MessageSetCorrectExtension2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	I *int32 `protobuf:"varint,9,req,name=i" json:"i,omitempty"`
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension2) Reset() {
	*x = TestAllRequiredTypesProto2_MessageSetCorrectExtension2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllRequiredTypesProto2_MessageSetCorrectExtension2) ProtoMessage() {}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension2) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto2_editions_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllRequiredTypesProto2_MessageSetCorrectExtension2.ProtoReflect.Descriptor instead.
func (*TestAllRequiredTypesProto2_MessageSetCorrectExtension2) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP(), []int{8, 4}
}

func (x *TestAllRequiredTypesProto2_MessageSetCorrectExtension2) GetI() int32 {
	if x != nil && x.I != nil {
		return *x.I
	}
	return 0
}

var file_editions_golden_test_messages_proto2_editions_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TestAllTypesProto2)(nil),
		ExtensionType: (*int32)(nil),
		Field:         120,
		Name:          "protobuf_test_messages.editions.proto2.extension_int32",
		Tag:           "varint,120,opt,name=extension_int32",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
	{
		ExtendedType:  (*TestAllTypesProto2)(nil),
		ExtensionType: (*GroupField)(nil),
		Field:         121,
		Name:          "protobuf_test_messages.editions.proto2.groupfield",
		Tag:           "bytes,121,opt,name=groupfield",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
	{
		ExtendedType:  (*TestAllTypesProto2_MessageSetCorrect)(nil),
		ExtensionType: (*TestAllTypesProto2_MessageSetCorrectExtension1)(nil),
		Field:         1547769,
		Name:          "protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1.message_set_extension",
		Tag:           "bytes,1547769,opt,name=message_set_extension",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
	{
		ExtendedType:  (*TestAllTypesProto2_MessageSetCorrect)(nil),
		ExtensionType: (*TestAllTypesProto2_MessageSetCorrectExtension2)(nil),
		Field:         4135312,
		Name:          "protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2.message_set_extension",
		Tag:           "bytes,4135312,opt,name=message_set_extension",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
	{
		ExtendedType:  (*TestAllRequiredTypesProto2_MessageSetCorrect)(nil),
		ExtensionType: (*TestAllRequiredTypesProto2_MessageSetCorrectExtension1)(nil),
		Field:         1547769,
		Name:          "protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1.message_set_extension",
		Tag:           "bytes,1547769,opt,name=message_set_extension",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
	{
		ExtendedType:  (*TestAllRequiredTypesProto2_MessageSetCorrect)(nil),
		ExtensionType: (*TestAllRequiredTypesProto2_MessageSetCorrectExtension2)(nil),
		Field:         4135312,
		Name:          "protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2.message_set_extension",
		Tag:           "bytes,4135312,opt,name=message_set_extension",
		Filename:      "editions/golden/test_messages_proto2_editions.proto",
	},
}

// Extension fields to TestAllTypesProto2.
var (
	// optional int32 extension_int32 = 120;
	E_ExtensionInt32 = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[0]
	// optional protobuf_test_messages.editions.proto2.GroupField groupfield = 121;
	E_Groupfield = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[1]
)

// Extension fields to TestAllTypesProto2_MessageSetCorrect.
var (
	// optional protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1 message_set_extension = 1547769;
	E_TestAllTypesProto2_MessageSetCorrectExtension1_MessageSetExtension = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[2]
	// optional protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2 message_set_extension = 4135312;
	E_TestAllTypesProto2_MessageSetCorrectExtension2_MessageSetExtension = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[3]
)

// Extension fields to TestAllRequiredTypesProto2_MessageSetCorrect.
var (
	// optional protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1 message_set_extension = 1547769;
	E_TestAllRequiredTypesProto2_MessageSetCorrectExtension1_MessageSetExtension = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[4]
	// optional protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2 message_set_extension = 4135312;
	E_TestAllRequiredTypesProto2_MessageSetCorrectExtension2_MessageSetExtension = &file_editions_golden_test_messages_proto2_editions_proto_extTypes[5]
)

var File_editions_golden_test_messages_proto2_editions_proto protoreflect.FileDescriptor

var file_editions_golden_test_messages_proto2_editions_proto_rawDesc = []byte{
	0x0a, 0x33, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x65,
	0x6e, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x22, 0x9d, 0x56,
	0x0a, 0x12, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07,
	0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x17,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x76,
	0x0a, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x16,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x77, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x6d, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x36,
	0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08,
	0x02, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08,
	0x01, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x72, 0x64, 0x12,
	0x67, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x21, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x23, 0x20, 0x03, 0x28,
	0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x25,
	0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x26, 0x20, 0x03, 0x28, 0x06, 0x52,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a,
	0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x29, 0x20, 0x03,
	0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x2b, 0x20, 0x03, 0x28,
	0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0c,
	0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12,
	0x80, 0x01, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x76, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x31,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x77, 0x0a, 0x14, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x6d, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x34, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x13, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x36, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65, 0x18, 0x36, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x08, 0x02, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x37, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x6f, 0x72, 0x64, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x4b, 0x20, 0x03, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x4c, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x4d, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x4e, 0x20, 0x03, 0x28, 0x04, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x4f, 0x20, 0x03, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x50, 0x20, 0x03,
	0x28, 0x12, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x51, 0x20, 0x03, 0x28, 0x07, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x52, 0x20, 0x03, 0x28, 0x06, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x53, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x54, 0x20, 0x03, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x55, 0x20, 0x03, 0x28, 0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2a, 0x0a,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x56,
	0x20, 0x03, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x57, 0x20, 0x03, 0x28, 0x08, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f,
	0x6c, 0x12, 0x7a, 0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x58, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x45, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x10, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x25, 0x0a,
	0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x59, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x75, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5b,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a,
	0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x5d, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x12, 0x52,
	0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x29, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x75, 0x6e,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x60,
	0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x61, 0x20, 0x03, 0x28, 0x0f,
	0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x62, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12,
	0x25, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x18, 0x63, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x64, 0x20, 0x03, 0x28, 0x01, 0x52,
	0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x18, 0x65, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x77, 0x0a, 0x14, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x66, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x75, 0x0a,
	0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x75, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61,
	0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x7b, 0x0a, 0x11, 0x6d,
	0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x3a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x81, 0x01, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x3e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x81, 0x01, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x3f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61,
	0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x40, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61,
	0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x41, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61,
	0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x75, 0x0a, 0x0f, 0x6d,
	0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x42,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x78, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61,
	0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x6f, 0x0a, 0x0d,
	0x6d, 0x61, 0x70, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x44, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x7b, 0x0a,
	0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x45, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x78, 0x0a, 0x10, 0x6d, 0x61,
	0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x46,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x47, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x16, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x48, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x88, 0x01, 0x0a, 0x16, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x49, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x6d,
	0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x4a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x14, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00,
	0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x7c, 0x0a,
	0x14, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x72, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0x73, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x74, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00,
	0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23, 0x0a, 0x0c,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x12, 0x66, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x77, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x5b, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0xc9, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x88, 0x01, 0x0a, 0x13, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x77, 0x6f, 0x72, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0xcc,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x57, 0x6f, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x13, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x77, 0x6f, 0x72, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x30, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0xf1, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33, 0x34,
	0x35, 0x36, 0x37, 0x38, 0x39, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x3a, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0xf2, 0x01, 0x20, 0x01, 0x28, 0x03, 0x3a, 0x14, 0x2d, 0x39, 0x31,
	0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
	0x39, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x32, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0xf3, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x0a, 0x32, 0x31, 0x32, 0x33, 0x34, 0x35,
	0x36, 0x37, 0x38, 0x39, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x3c, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf4, 0x01, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x14, 0x31, 0x30,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
	0x38, 0x39, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x32, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0xf5, 0x01, 0x20, 0x01, 0x28, 0x11, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33,
	0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x3c, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf6, 0x01, 0x20, 0x01, 0x28, 0x12, 0x3a, 0x14,
	0x2d, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35,
	0x36, 0x37, 0x38, 0x39, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x34, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xf7, 0x01, 0x20, 0x01, 0x28, 0x07, 0x3a, 0x0a, 0x32,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x3e, 0x0a, 0x0f, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0xf8, 0x01, 0x20,
	0x01, 0x28, 0x06, 0x3a, 0x14, 0x31, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x36, 0x0a, 0x10, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xf9, 0x01,
	0x20, 0x01, 0x28, 0x0f, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
	0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x40, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0xfa, 0x01, 0x20, 0x01, 0x28, 0x10, 0x3a, 0x14, 0x2d, 0x39,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
	0x38, 0x39, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66,
	0x6c, 0x6f, 0x61, 0x74, 0x18, 0xfb, 0x01, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x05, 0x39, 0x65, 0x2b,
	0x30, 0x39, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x2d, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x18, 0xfc, 0x01, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x05, 0x37, 0x65, 0x2b, 0x32, 0x32,
	0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x28, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18,
	0xfd, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x0b, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x0e, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0xfe, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x3a, 0x07, 0x52, 0x6f, 0x73, 0x65, 0x62, 0x75, 0x64, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xff, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x3a, 0x06, 0x6a, 0x6f, 0x73, 0x68, 0x75, 0x61, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x18, 0x91, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0x18, 0x92, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x33, 0x18, 0x93, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x33, 0x12, 0x22,
	0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x34, 0x5f, 0x18,
	0x94, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x30, 0x6e, 0x61, 0x6d, 0x65,
	0x35, 0x18, 0x95, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x30,
	0x6e, 0x61, 0x6d, 0x65, 0x35, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x30,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x36, 0x18, 0x96, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x30, 0x4e, 0x61, 0x6d, 0x65, 0x36, 0x12, 0x1f, 0x0a, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x37, 0x18, 0x97, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x37, 0x12, 0x1f, 0x0a, 0x0a, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x38, 0x18, 0x98, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x38, 0x12, 0x20, 0x0a, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x39, 0x18, 0x99, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x39, 0x12, 0x22,
	0x0a, 0x0c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x30, 0x18, 0x9a,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65,
	0x31, 0x30, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x31, 0x31, 0x18, 0x9b, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x4e, 0x41, 0x4d, 0x45, 0x31, 0x31, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x31, 0x32, 0x18, 0x9c, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x32, 0x12, 0x24, 0x0a, 0x0e, 0x5f, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x33, 0x18, 0x9d, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x33,
	0x12, 0x24, 0x0a, 0x0e, 0x5f, 0x5f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x31, 0x34, 0x18, 0x9e, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x31, 0x34, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x35, 0x18, 0x9f, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x35, 0x12, 0x23, 0x0a, 0x0d, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x36, 0x18, 0xa0, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x36,
	0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x37,
	0x5f, 0x5f, 0x18, 0xa1, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x31, 0x37, 0x12, 0x24, 0x0a, 0x0e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x31, 0x38, 0x5f, 0x5f, 0x18, 0xa2, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x38, 0x1a, 0x7b, 0x0a, 0x0d,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a,
	0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x5c, 0x0a, 0x0b, 0x63,
	0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x0b, 0x63, 0x6f,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d,
	0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a,
	0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x12, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a,
	0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x07, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x07, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0f, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d,
	0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e,
	0x0a, 0x10, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42,
	0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x93, 0x01, 0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x88, 0x01, 0x0a, 0x1c,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x52,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8d, 0x01, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x82, 0x01, 0x0a, 0x19, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4c, 0x0a, 0x04, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0xca, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xcb, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x1a, 0x5b, 0x0a, 0x13, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x57, 0x6f, 0x72, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0xcd, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0xce, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x1a, 0x21, 0x0a, 0x11, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x2a, 0x08, 0x08, 0x04, 0x10,
	0xff, 0xff, 0xff, 0xff, 0x07, 0x3a, 0x02, 0x08, 0x01, 0x1a, 0x8c, 0x02, 0x0a, 0x1b, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x74, 0x72,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x74, 0x72, 0x32, 0xda, 0x01, 0x0a, 0x15,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x18, 0xf9, 0xbb, 0x5e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x31, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x89, 0x02, 0x0a, 0x1b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x12, 0x0c, 0x0a, 0x01, 0x69, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x01, 0x69, 0x32, 0xdb, 0x01, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x4c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x18, 0x90,
	0xb3, 0xfc, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x52,
	0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42,
	0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x2a,
	0x05, 0x08, 0x78, 0x10, 0xc9, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x4a, 0x06, 0x08, 0xe8, 0x07, 0x10, 0x90, 0x4e, 0x22, 0x24, 0x0a,
	0x14, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x01, 0x63, 0x22, 0x50, 0x0a, 0x0a, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x7a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x7b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x22, 0xb7, 0x03, 0x0a, 0x15, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x54, 0x6f, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x26, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x64, 0x0a, 0x0e, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x0d, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x79, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xec, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x54, 0x6f, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0xee, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xf3, 0x07, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x1a, 0x1d, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x22,
	0x16, 0x0a, 0x14, 0x4e, 0x75, 0x6c, 0x6c, 0x48, 0x79, 0x70, 0x6f, 0x74, 0x68, 0x65, 0x73, 0x69,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x22, 0x2f, 0x0a, 0x0e, 0x45, 0x6e, 0x75, 0x6d, 0x4f,
	0x6e, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x22, 0x1d, 0x0a, 0x04, 0x42, 0x6f, 0x6f,
	0x6c, 0x12, 0x0a, 0x0a, 0x06, 0x6b, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x6b, 0x54, 0x72, 0x75, 0x65, 0x10, 0x01, 0x22, 0x25, 0x0a, 0x0f, 0x4f, 0x6e, 0x65, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x61, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x77,
	0x6f, 0x72, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x69, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x63, 0x65, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x63, 0x65, 0x70, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x73, 0x22, 0xf6, 0x1c, 0x0a, 0x1a, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a,
	0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x30, 0x0a,
	0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x30, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x32, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x32, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42,
	0x6f, 0x6f, 0x6c, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x8f, 0x01, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x15, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x7d, 0x0a, 0x18, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x16, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x14, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x74, 0x0a, 0x15, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x13, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x3b, 0x0a, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0x08, 0x02, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x2c,
	0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x72, 0x64, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x08, 0x01, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x72, 0x64, 0x12, 0x76, 0x0a, 0x11,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x1a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x52, 0x18, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x65, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0xc9, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x42,
	0x07, 0xaa, 0x01, 0x04, 0x08, 0x03, 0x28, 0x02, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x37,
	0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0xf1, 0x01, 0x20, 0x01, 0x28, 0x05, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37,
	0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x41, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf2, 0x01, 0x20, 0x01, 0x28, 0x03, 0x3a,
	0x14, 0x2d, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34,
	0x35, 0x36, 0x37, 0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x39, 0x0a, 0x0e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xf3, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x3a, 0x0a, 0x32, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x43, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf4, 0x01, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x14,
	0x31, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35,
	0x36, 0x37, 0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x39, 0x0a, 0x0e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xf5, 0x01, 0x20,
	0x01, 0x28, 0x11, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x43, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf6, 0x01, 0x20, 0x01, 0x28, 0x12, 0x3a, 0x14,
	0x2d, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35,
	0x36, 0x37, 0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xf7, 0x01,
	0x20, 0x01, 0x28, 0x07, 0x3a, 0x0a, 0x32, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x45, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0xf8, 0x01, 0x20, 0x01, 0x28,
	0x06, 0x3a, 0x14, 0x31, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32,
	0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x3d,
	0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0xf9, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x3a, 0x0a, 0x2d, 0x31, 0x32, 0x33, 0x34,
	0x35, 0x36, 0x37, 0x38, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0f, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x47, 0x0a,
	0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0xfa, 0x01, 0x20, 0x01, 0x28, 0x10, 0x3a, 0x14, 0x2d, 0x39, 0x31, 0x32, 0x33, 0x34,
	0x35, 0x36, 0x37, 0x38, 0x39, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x32, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0xfb, 0x01, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x05,
	0x39, 0x65, 0x2b, 0x30, 0x39, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x34, 0x0a, 0x0e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0xfc, 0x01, 0x20,
	0x01, 0x28, 0x01, 0x3a, 0x05, 0x37, 0x65, 0x2b, 0x32, 0x32, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08,
	0x03, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x12, 0x2f, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x18, 0xfd, 0x01, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x08, 0x03, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f,
	0x6c, 0x12, 0x36, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0xfe, 0x01, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x07, 0x52, 0x6f, 0x73, 0x65,
	0x62, 0x75, 0x64, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x33, 0x0a, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xff, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x3a, 0x06, 0x6a, 0x6f, 0x73, 0x68, 0x75, 0x61, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x79, 0x74, 0x65, 0x73, 0x1a, 0x88,
	0x02, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x13, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x01, 0x61, 0x12, 0x6b, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69,
	0x76, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x1a, 0x5a, 0x0a, 0x04, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x27, 0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0xca, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0a,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0c, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xcb, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x1a, 0x21, 0x0a, 0x11, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x2a, 0x08, 0x08, 0x04, 0x10, 0xff,
	0xff, 0xff, 0xff, 0x07, 0x3a, 0x02, 0x08, 0x01, 0x1a, 0xa3, 0x02, 0x0a, 0x1b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x31, 0x12, 0x17, 0x0a, 0x03, 0x73, 0x74, 0x72, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x03, 0x73, 0x74,
	0x72, 0x32, 0xea, 0x01, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x54, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63,
	0x74, 0x18, 0xf9, 0xbb, 0x5e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x31, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xa0,
	0x02, 0x0a, 0x1b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72,
	0x72, 0x65, 0x63, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x12, 0x13,
	0x0a, 0x01, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03,
	0x52, 0x01, 0x69, 0x32, 0xeb, 0x01, 0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x54, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x18, 0x90, 0xb3, 0xfc, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x72, 0x72, 0x65,
	0x63, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0x52, 0x13, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x52, 0x10,
	0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x45,
	0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x2a, 0x05, 0x08, 0x78,
	0x10, 0xc9, 0x01, 0x4a, 0x06, 0x08, 0xe8, 0x07, 0x10, 0x90, 0x4e, 0x2a, 0x46, 0x0a, 0x11, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x10,
	0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52,
	0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41,
	0x5a, 0x10, 0x02, 0x3a, 0x63, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x18, 0x78, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x95, 0x01, 0x0a, 0x0a, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0x18, 0x79, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x28, 0x02, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x42, 0x4f, 0x0a, 0x31, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x48, 0x01, 0xa2, 0x02, 0x0e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x92, 0x03, 0x06, 0x10, 0x02, 0x18, 0x02, 0x20,
	0x03, 0x62, 0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_editions_golden_test_messages_proto2_editions_proto_rawDescOnce sync.Once
	file_editions_golden_test_messages_proto2_editions_proto_rawDescData = file_editions_golden_test_messages_proto2_editions_proto_rawDesc
)

func file_editions_golden_test_messages_proto2_editions_proto_rawDescGZIP() []byte {
	file_editions_golden_test_messages_proto2_editions_proto_rawDescOnce.Do(func() {
		file_editions_golden_test_messages_proto2_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_editions_golden_test_messages_proto2_editions_proto_rawDescData)
	})
	return file_editions_golden_test_messages_proto2_editions_proto_rawDescData
}

var file_editions_golden_test_messages_proto2_editions_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_editions_golden_test_messages_proto2_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_editions_golden_test_messages_proto2_editions_proto_goTypes = []interface{}{
	(ForeignEnumProto2)(0),                     // 0: protobuf_test_messages.editions.proto2.ForeignEnumProto2
	(TestAllTypesProto2_NestedEnum)(0),         // 1: protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	(EnumOnlyProto2_Bool)(0),                   // 2: protobuf_test_messages.editions.proto2.EnumOnlyProto2.Bool
	(TestAllRequiredTypesProto2_NestedEnum)(0), // 3: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedEnum
	(*TestAllTypesProto2)(nil),                 // 4: protobuf_test_messages.editions.proto2.TestAllTypesProto2
	(*ForeignMessageProto2)(nil),               // 5: protobuf_test_messages.editions.proto2.ForeignMessageProto2
	(*GroupField)(nil),                         // 6: protobuf_test_messages.editions.proto2.GroupField
	(*UnknownToTestAllTypes)(nil),              // 7: protobuf_test_messages.editions.proto2.UnknownToTestAllTypes
	(*NullHypothesisProto2)(nil),               // 8: protobuf_test_messages.editions.proto2.NullHypothesisProto2
	(*EnumOnlyProto2)(nil),                     // 9: protobuf_test_messages.editions.proto2.EnumOnlyProto2
	(*OneStringProto2)(nil),                    // 10: protobuf_test_messages.editions.proto2.OneStringProto2
	(*ProtoWithKeywords)(nil),                  // 11: protobuf_test_messages.editions.proto2.ProtoWithKeywords
	(*TestAllRequiredTypesProto2)(nil),         // 12: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2
	(*TestAllTypesProto2_NestedMessage)(nil),   // 13: protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage
	nil,                                        // 14: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32Int32Entry
	nil,                                        // 15: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt64Int64Entry
	nil,                                        // 16: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapUint32Uint32Entry
	nil,                                        // 17: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapUint64Uint64Entry
	nil,                                        // 18: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSint32Sint32Entry
	nil,                                        // 19: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSint64Sint64Entry
	nil,                                        // 20: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapFixed32Fixed32Entry
	nil,                                        // 21: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapFixed64Fixed64Entry
	nil,                                        // 22: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSfixed32Sfixed32Entry
	nil,                                        // 23: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSfixed64Sfixed64Entry
	nil,                                        // 24: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32FloatEntry
	nil,                                        // 25: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32DoubleEntry
	nil,                                        // 26: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapBoolBoolEntry
	nil,                                        // 27: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringStringEntry
	nil,                                        // 28: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringBytesEntry
	nil,                                        // 29: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedMessageEntry
	nil,                                        // 30: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignMessageEntry
	nil,                                        // 31: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedEnumEntry
	nil,                                        // 32: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignEnumEntry
	(*TestAllTypesProto2_Data)(nil),            // 33: protobuf_test_messages.editions.proto2.TestAllTypesProto2.Data
	(*TestAllTypesProto2_MultiWordGroupField)(nil),                 // 34: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MultiWordGroupField
	(*TestAllTypesProto2_MessageSetCorrect)(nil),                   // 35: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrect
	(*TestAllTypesProto2_MessageSetCorrectExtension1)(nil),         // 36: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1
	(*TestAllTypesProto2_MessageSetCorrectExtension2)(nil),         // 37: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2
	(*UnknownToTestAllTypes_OptionalGroup)(nil),                    // 38: protobuf_test_messages.editions.proto2.UnknownToTestAllTypes.OptionalGroup
	(*TestAllRequiredTypesProto2_NestedMessage)(nil),               // 39: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedMessage
	(*TestAllRequiredTypesProto2_Data)(nil),                        // 40: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.Data
	(*TestAllRequiredTypesProto2_MessageSetCorrect)(nil),           // 41: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrect
	(*TestAllRequiredTypesProto2_MessageSetCorrectExtension1)(nil), // 42: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1
	(*TestAllRequiredTypesProto2_MessageSetCorrectExtension2)(nil), // 43: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2
}
var file_editions_golden_test_messages_proto2_editions_proto_depIdxs = []int32{
	13, // 0: protobuf_test_messages.editions.proto2.TestAllTypesProto2.optional_nested_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage
	5,  // 1: protobuf_test_messages.editions.proto2.TestAllTypesProto2.optional_foreign_message:type_name -> protobuf_test_messages.editions.proto2.ForeignMessageProto2
	1,  // 2: protobuf_test_messages.editions.proto2.TestAllTypesProto2.optional_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	0,  // 3: protobuf_test_messages.editions.proto2.TestAllTypesProto2.optional_foreign_enum:type_name -> protobuf_test_messages.editions.proto2.ForeignEnumProto2
	4,  // 4: protobuf_test_messages.editions.proto2.TestAllTypesProto2.recursive_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2
	13, // 5: protobuf_test_messages.editions.proto2.TestAllTypesProto2.repeated_nested_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage
	5,  // 6: protobuf_test_messages.editions.proto2.TestAllTypesProto2.repeated_foreign_message:type_name -> protobuf_test_messages.editions.proto2.ForeignMessageProto2
	1,  // 7: protobuf_test_messages.editions.proto2.TestAllTypesProto2.repeated_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	0,  // 8: protobuf_test_messages.editions.proto2.TestAllTypesProto2.repeated_foreign_enum:type_name -> protobuf_test_messages.editions.proto2.ForeignEnumProto2
	1,  // 9: protobuf_test_messages.editions.proto2.TestAllTypesProto2.packed_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	1,  // 10: protobuf_test_messages.editions.proto2.TestAllTypesProto2.unpacked_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	14, // 11: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_int32_int32:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32Int32Entry
	15, // 12: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_int64_int64:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt64Int64Entry
	16, // 13: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_uint32_uint32:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapUint32Uint32Entry
	17, // 14: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_uint64_uint64:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapUint64Uint64Entry
	18, // 15: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_sint32_sint32:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSint32Sint32Entry
	19, // 16: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_sint64_sint64:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSint64Sint64Entry
	20, // 17: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_fixed32_fixed32:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapFixed32Fixed32Entry
	21, // 18: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_fixed64_fixed64:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapFixed64Fixed64Entry
	22, // 19: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_sfixed32_sfixed32:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSfixed32Sfixed32Entry
	23, // 20: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_sfixed64_sfixed64:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapSfixed64Sfixed64Entry
	24, // 21: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_int32_float:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32FloatEntry
	25, // 22: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_int32_double:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapInt32DoubleEntry
	26, // 23: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_bool_bool:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapBoolBoolEntry
	27, // 24: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_string:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringStringEntry
	28, // 25: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_bytes:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringBytesEntry
	29, // 26: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_nested_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedMessageEntry
	30, // 27: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_foreign_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignMessageEntry
	31, // 28: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedEnumEntry
	32, // 29: protobuf_test_messages.editions.proto2.TestAllTypesProto2.map_string_foreign_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignEnumEntry
	13, // 30: protobuf_test_messages.editions.proto2.TestAllTypesProto2.oneof_nested_message:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage
	1,  // 31: protobuf_test_messages.editions.proto2.TestAllTypesProto2.oneof_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	33, // 32: protobuf_test_messages.editions.proto2.TestAllTypesProto2.data:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.Data
	34, // 33: protobuf_test_messages.editions.proto2.TestAllTypesProto2.multiwordgroupfield:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MultiWordGroupField
	5,  // 34: protobuf_test_messages.editions.proto2.UnknownToTestAllTypes.nested_message:type_name -> protobuf_test_messages.editions.proto2.ForeignMessageProto2
	38, // 35: protobuf_test_messages.editions.proto2.UnknownToTestAllTypes.optionalgroup:type_name -> protobuf_test_messages.editions.proto2.UnknownToTestAllTypes.OptionalGroup
	39, // 36: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.required_nested_message:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedMessage
	5,  // 37: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.required_foreign_message:type_name -> protobuf_test_messages.editions.proto2.ForeignMessageProto2
	3,  // 38: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.required_nested_enum:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedEnum
	0,  // 39: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.required_foreign_enum:type_name -> protobuf_test_messages.editions.proto2.ForeignEnumProto2
	12, // 40: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.recursive_message:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2
	12, // 41: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.optional_recursive_message:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2
	40, // 42: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.data:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.Data
	4,  // 43: protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage.corecursive:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2
	13, // 44: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedMessageEntry.value:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedMessage
	5,  // 45: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignMessageEntry.value:type_name -> protobuf_test_messages.editions.proto2.ForeignMessageProto2
	1,  // 46: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringNestedEnumEntry.value:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.NestedEnum
	0,  // 47: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MapStringForeignEnumEntry.value:type_name -> protobuf_test_messages.editions.proto2.ForeignEnumProto2
	12, // 48: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedMessage.corecursive:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2
	12, // 49: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.NestedMessage.optional_corecursive:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2
	4,  // 50: protobuf_test_messages.editions.proto2.extension_int32:extendee -> protobuf_test_messages.editions.proto2.TestAllTypesProto2
	4,  // 51: protobuf_test_messages.editions.proto2.groupfield:extendee -> protobuf_test_messages.editions.proto2.TestAllTypesProto2
	35, // 52: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1.message_set_extension:extendee -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrect
	35, // 53: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2.message_set_extension:extendee -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrect
	41, // 54: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1.message_set_extension:extendee -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrect
	41, // 55: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2.message_set_extension:extendee -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrect
	6,  // 56: protobuf_test_messages.editions.proto2.groupfield:type_name -> protobuf_test_messages.editions.proto2.GroupField
	36, // 57: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1.message_set_extension:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension1
	37, // 58: protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2.message_set_extension:type_name -> protobuf_test_messages.editions.proto2.TestAllTypesProto2.MessageSetCorrectExtension2
	42, // 59: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1.message_set_extension:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension1
	43, // 60: protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2.message_set_extension:type_name -> protobuf_test_messages.editions.proto2.TestAllRequiredTypesProto2.MessageSetCorrectExtension2
	61, // [61:61] is the sub-list for method output_type
	61, // [61:61] is the sub-list for method input_type
	56, // [56:61] is the sub-list for extension type_name
	50, // [50:56] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_editions_golden_test_messages_proto2_editions_proto_init() }
func file_editions_golden_test_messages_proto2_editions_proto_init() {
	if File_editions_golden_test_messages_proto2_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessageProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnknownToTestAllTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NullHypothesisProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnumOnlyProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OneStringProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProtoWithKeywords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_MultiWordGroupField); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_MessageSetCorrect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_MessageSetCorrectExtension1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto2_MessageSetCorrectExtension2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnknownToTestAllTypes_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2_MessageSetCorrect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2_MessageSetCorrectExtension1); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto2_editions_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllRequiredTypesProto2_MessageSetCorrectExtension2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_editions_golden_test_messages_proto2_editions_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypesProto2_OneofUint32)(nil),
		(*TestAllTypesProto2_OneofNestedMessage)(nil),
		(*TestAllTypesProto2_OneofString)(nil),
		(*TestAllTypesProto2_OneofBytes)(nil),
		(*TestAllTypesProto2_OneofBool)(nil),
		(*TestAllTypesProto2_OneofUint64)(nil),
		(*TestAllTypesProto2_OneofFloat)(nil),
		(*TestAllTypesProto2_OneofDouble)(nil),
		(*TestAllTypesProto2_OneofEnum)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_editions_golden_test_messages_proto2_editions_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   40,
			NumExtensions: 6,
			NumServices:   0,
		},
		GoTypes:           file_editions_golden_test_messages_proto2_editions_proto_goTypes,
		DependencyIndexes: file_editions_golden_test_messages_proto2_editions_proto_depIdxs,
		EnumInfos:         file_editions_golden_test_messages_proto2_editions_proto_enumTypes,
		MessageInfos:      file_editions_golden_test_messages_proto2_editions_proto_msgTypes,
		ExtensionInfos:    file_editions_golden_test_messages_proto2_editions_proto_extTypes,
	}.Build()
	File_editions_golden_test_messages_proto2_editions_proto = out.File
	file_editions_golden_test_messages_proto2_editions_proto_rawDesc = nil
	file_editions_golden_test_messages_proto2_editions_proto_goTypes = nil
	file_editions_golden_test_messages_proto2_editions_proto_depIdxs = nil
}
