// Package api 基于官方SDK的用户相关API接口
// 提供钉钉用户相关的API调用，包括用户信息获取、登录验证等
package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"fdd622server/internal/pkg/logger"
)

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// UserAPI 基于官方SDK的用户相关API接口
type UserAPI struct {
	sdkClient *SDKClient
}

// UserInfoByCodeResponse 通过code获取用户信息响应结构
type UserInfoByCodeResponse struct {
	UserID string `json:"userid"`
	Name   string `json:"name"`
}

// UserDetailResponse 用户详细信息响应结构
type UserDetailResponse struct {
	UserID     string `json:"userid"`
	Name       string `json:"name"`
	Mobile     string `json:"mobile"`
	Email      string `json:"email"`
	DeptIDList []int  `json:"dept_id_list"`
	MainDeptID int    `json:"main_dept_id"`  // 🔧 添加主部门ID字段
	Avatar     string `json:"avatar"`
	JobNumber  string `json:"job_number"`
	Title      string `json:"title"`
	IsActive   bool   `json:"active"`
	IsAdmin    bool   `json:"is_admin"`
	IsBoss     bool   `json:"is_boss"`
	IsLeader   bool   `json:"is_leader"`
}

// NewUserAPI 创建新的基于官方SDK的用户API实例
func NewUserAPI() (*UserAPI, error) {
	sdkClient, err := NewSDKClient(nil)
	if err != nil {
		return nil, fmt.Errorf("创建SDK客户端失败: %w", err)
	}

	return &UserAPI{
		sdkClient: sdkClient,
	}, nil
}

// GetUserInfoByCode 通过免登授权码获取用户信息
// 使用企业访问令牌 + 免登授权码的方式（企业内部应用免登）
func (u *UserAPI) GetUserInfoByCode(code, accessToken string) (*UserInfoByCodeResponse, error) {
	logger.Info("=== 开始通过免登授权码获取用户信息（企业内部应用） ===")
	logger.Info("API调用参数详情",
		logger.String("code", code),
		logger.String("access_token", accessToken[:min(20, len(accessToken))]+"..."),
		logger.Int("code_length", len(code)),
		logger.Int("access_token_length", len(accessToken)))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用免登API获取用户信息
	// 这里我们需要使用HTTP客户端调用免登API，因为官方SDK可能没有直接支持
	return u.getUserInfoByCodeHTTP(ctx, code, accessToken)
}

// getUserInfoByCodeHTTP 通过HTTP调用钉钉免登API获取用户信息
func (u *UserAPI) getUserInfoByCodeHTTP(ctx context.Context, code, accessToken string) (*UserInfoByCodeResponse, error) {
	// 使用新版免登API
	apiURL := "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo"

	logger.Info("准备调用钉钉免登API",
		logger.String("api_url", apiURL),
		logger.String("method", "POST"),
		logger.String("code", code),
		logger.String("access_token", accessToken[:min(20, len(accessToken))]+"..."))

	// 构造请求参数
	requestData := map[string]interface{}{
		"code": code,
	}

	// 将参数转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logger.Error("序列化请求参数失败", logger.Error2(err))
		return nil, fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 构造完整的URL（包含access_token）
	fullURL := fmt.Sprintf("%s?access_token=%s", apiURL, accessToken)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("创建HTTP请求失败", logger.Error2(err))
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "FDD622Server/1.0")

	logger.Info("发送HTTP请求到钉钉免登API",
		logger.String("url", fullURL),
		logger.String("request_body", string(jsonData)))

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		logger.Error("HTTP请求失败", logger.Error2(err))
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应失败", logger.Error2(err))
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	logger.Info("收到钉钉免登API响应",
		logger.Int("status_code", resp.StatusCode),
		logger.String("response_body", string(respBody)))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.Error("HTTP请求返回错误状态码",
			logger.Int("status_code", resp.StatusCode),
			logger.String("response", string(respBody)))
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应（只解析登录需要的字段）
	var apiResponse struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
		Result  struct {
			UserID string `json:"userid"`
			Name   string `json:"name"`
		} `json:"result"`
	}

	err = json.Unmarshal(respBody, &apiResponse)
	if err != nil {
		logger.Error("解析响应JSON失败", logger.Error2(err))
		return nil, fmt.Errorf("解析响应JSON失败: %w", err)
	}

	// 检查API错误码
	if apiResponse.ErrCode != 0 {
		logger.Error("钉钉API返回错误",
			logger.Int("errcode", apiResponse.ErrCode),
			logger.String("errmsg", apiResponse.ErrMsg))
		return nil, fmt.Errorf("钉钉API错误: %d - %s", apiResponse.ErrCode, apiResponse.ErrMsg)
	}

	// 构造返回结果（只返回登录必需的信息）
	result := &UserInfoByCodeResponse{
		UserID: apiResponse.Result.UserID,
		Name:   apiResponse.Result.Name,
	}

	logger.Info("通过免登授权码获取用户信息成功（仅登录信息）",
		logger.String("user_id", result.UserID),
		logger.String("name", result.Name))

	return result, nil
}

// GetUserDetail 获取用户详细信息
// 使用钉钉官方HTTP API v2版本
func (u *UserAPI) GetUserDetail(accessToken, userID string) (*UserDetailResponse, error) {
	logger.Info("开始获取用户详细信息",
		logger.String("user_id", userID))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用钉钉API v2版本
	apiURL := "https://oapi.dingtalk.com/topapi/v2/user/get"

	// 构造请求参数
	requestData := map[string]interface{}{
		"userid": userID,
	}

	response, err := u.callDingTalkAPI(ctx, apiURL, accessToken, requestData)
	if err != nil {
		return nil, err
	}

	var apiResponse struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
		Result  struct {
			UserID     string `json:"userid"`
			Name       string `json:"name"`
			Mobile     string `json:"mobile"`
			Email      string `json:"email"`
			DeptIDList []int  `json:"dept_id_list"`
			Avatar     string `json:"avatar"`
			JobNumber  string `json:"job_number"`
			Title      string `json:"title"`
			Active     bool   `json:"active"`
			IsAdmin    bool   `json:"is_admin"`
			IsBoss     bool   `json:"is_boss"`
			IsLeader   bool   `json:"is_leader"`
		} `json:"result"`
	}

	if err := json.Unmarshal(response, &apiResponse); err != nil {
		logger.Error("解析用户详细信息响应失败", logger.Error2(err))
		return nil, fmt.Errorf("解析用户详细信息响应失败: %w", err)
	}

	if apiResponse.ErrCode != 0 {
		logger.Error("获取用户详细信息API返回错误",
			logger.Int("errcode", apiResponse.ErrCode),
			logger.String("errmsg", apiResponse.ErrMsg))
		return nil, fmt.Errorf("钉钉API错误: %d - %s", apiResponse.ErrCode, apiResponse.ErrMsg)
	}

	// 转换为标准响应格式
	result := &UserDetailResponse{
		UserID:     apiResponse.Result.UserID,
		Name:       apiResponse.Result.Name,
		Mobile:     apiResponse.Result.Mobile,
		Email:      apiResponse.Result.Email,
		DeptIDList: apiResponse.Result.DeptIDList,
		Avatar:     apiResponse.Result.Avatar,
		JobNumber:  apiResponse.Result.JobNumber,
		Title:      apiResponse.Result.Title,
		IsActive:   apiResponse.Result.Active,
		IsAdmin:    apiResponse.Result.IsAdmin,
		IsBoss:     apiResponse.Result.IsBoss,
		IsLeader:   apiResponse.Result.IsLeader,
	}

	logger.Info("获取用户详细信息成功",
		logger.String("user_id", result.UserID),
		logger.String("name", result.Name),
		logger.Bool("is_active", result.IsActive))

	return result, nil
}

// callDingTalkAPI 调用钉钉API的通用方法（如果不存在的话需要添加）
func (u *UserAPI) callDingTalkAPI(ctx context.Context, apiURL string, accessToken string, requestData map[string]interface{}) ([]byte, error) {
	// 将参数转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logger.Error("序列化请求参数失败", logger.Error2(err))
		return nil, fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 构造完整的URL（包含access_token）
	fullURL := fmt.Sprintf("%s?access_token=%s", apiURL, accessToken)

	logger.Debug("调用钉钉API",
		logger.String("url", apiURL),
		logger.String("request", string(jsonData)))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", fullURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("创建HTTP请求失败", logger.Error2(err))
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "FDD622Server/1.0")

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		logger.Error("HTTP请求失败", logger.Error2(err))
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("读取响应失败", logger.Error2(err))
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	logger.Debug("收到钉钉API响应",
		logger.Int("status_code", resp.StatusCode),
		logger.String("response", string(respBody)))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logger.Error("HTTP请求返回错误状态码",
			logger.Int("status_code", resp.StatusCode),
			logger.String("response", string(respBody)))
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

// ListUsers 获取部门用户列表
// 使用官方SDK替换原有的HTTP客户端实现
func (u *UserAPI) ListUsers(accessToken string, deptID int64, cursor int64, size int64) (*ListUsersResponse, error) {
	logger.Info("开始获取部门用户列表（官方SDK）",
		logger.Int("dept_id", int(deptID)),
		logger.Int("cursor", int(cursor)),
		logger.Int("size", int(size)))

	// TODO: 官方SDK的contact_1_0包中没有ListUsersHeaders、ListUsersRequest等结构体
	// 需要根据实际需求选择合适的API
	logger.Warn("ListUsers方法暂未实现，官方SDK中没有对应的接口")

	// 返回空结果
	result := &ListUsersResponse{
		HasMore:    false,
		NextCursor: 0,
		Users:      []*UserDetailResponse{},
	}

	logger.Info("获取部门用户列表（暂未实现）",
		logger.Int("dept_id", int(deptID)),
		logger.Int("user_count", 0),
		logger.Bool("has_more", false))

	return result, nil
}

// ListUsersResponse 用户列表响应结构
type ListUsersResponse struct {
	Users      []*UserDetailResponse `json:"users"`
	HasMore    bool                  `json:"has_more"`
	NextCursor int64                 `json:"next_cursor"`
}

// Close 关闭用户API客户端
func (u *UserAPI) Close() {
	if u.sdkClient != nil {
		u.sdkClient.Close()
	}
}

