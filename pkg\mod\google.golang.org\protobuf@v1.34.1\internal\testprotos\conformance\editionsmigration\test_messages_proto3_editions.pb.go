// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd
//
// Test schema for proto3 messages.  This test schema is used by:
//
// - benchmarks
// - fuzz tests
// - conformance tests

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: editions/golden/test_messages_proto3_editions.proto

package editions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

type ForeignEnum int32

const (
	ForeignEnum_FOREIGN_FOO ForeignEnum = 0
	ForeignEnum_FOREIGN_BAR ForeignEnum = 1
	ForeignEnum_FOREIGN_BAZ ForeignEnum = 2
)

// Enum value maps for ForeignEnum.
var (
	ForeignEnum_name = map[int32]string{
		0: "FOREIGN_FOO",
		1: "FOREIGN_BAR",
		2: "FOREIGN_BAZ",
	}
	ForeignEnum_value = map[string]int32{
		"FOREIGN_FOO": 0,
		"FOREIGN_BAR": 1,
		"FOREIGN_BAZ": 2,
	}
)

func (x ForeignEnum) Enum() *ForeignEnum {
	p := new(ForeignEnum)
	*p = x
	return p
}

func (x ForeignEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto3_editions_proto_enumTypes[0].Descriptor()
}

func (ForeignEnum) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto3_editions_proto_enumTypes[0]
}

func (x ForeignEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForeignEnum.Descriptor instead.
func (ForeignEnum) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{0}
}

type TestAllTypesProto3_NestedEnum int32

const (
	TestAllTypesProto3_FOO TestAllTypesProto3_NestedEnum = 0
	TestAllTypesProto3_BAR TestAllTypesProto3_NestedEnum = 1
	TestAllTypesProto3_BAZ TestAllTypesProto3_NestedEnum = 2
	TestAllTypesProto3_NEG TestAllTypesProto3_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypesProto3_NestedEnum.
var (
	TestAllTypesProto3_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypesProto3_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypesProto3_NestedEnum) Enum() *TestAllTypesProto3_NestedEnum {
	p := new(TestAllTypesProto3_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypesProto3_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypesProto3_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto3_editions_proto_enumTypes[1].Descriptor()
}

func (TestAllTypesProto3_NestedEnum) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto3_editions_proto_enumTypes[1]
}

func (x TestAllTypesProto3_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypesProto3_NestedEnum.Descriptor instead.
func (TestAllTypesProto3_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{0, 0}
}

type TestAllTypesProto3_AliasedEnum int32

const (
	TestAllTypesProto3_ALIAS_FOO TestAllTypesProto3_AliasedEnum = 0
	TestAllTypesProto3_ALIAS_BAR TestAllTypesProto3_AliasedEnum = 1
	TestAllTypesProto3_ALIAS_BAZ TestAllTypesProto3_AliasedEnum = 2
	TestAllTypesProto3_MOO       TestAllTypesProto3_AliasedEnum = 2
	TestAllTypesProto3_moo       TestAllTypesProto3_AliasedEnum = 2
	TestAllTypesProto3_bAz       TestAllTypesProto3_AliasedEnum = 2
)

// Enum value maps for TestAllTypesProto3_AliasedEnum.
var (
	TestAllTypesProto3_AliasedEnum_name = map[int32]string{
		0: "ALIAS_FOO",
		1: "ALIAS_BAR",
		2: "ALIAS_BAZ",
		// Duplicate value: 2: "MOO",
		// Duplicate value: 2: "moo",
		// Duplicate value: 2: "bAz",
	}
	TestAllTypesProto3_AliasedEnum_value = map[string]int32{
		"ALIAS_FOO": 0,
		"ALIAS_BAR": 1,
		"ALIAS_BAZ": 2,
		"MOO":       2,
		"moo":       2,
		"bAz":       2,
	}
)

func (x TestAllTypesProto3_AliasedEnum) Enum() *TestAllTypesProto3_AliasedEnum {
	p := new(TestAllTypesProto3_AliasedEnum)
	*p = x
	return p
}

func (x TestAllTypesProto3_AliasedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypesProto3_AliasedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto3_editions_proto_enumTypes[2].Descriptor()
}

func (TestAllTypesProto3_AliasedEnum) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto3_editions_proto_enumTypes[2]
}

func (x TestAllTypesProto3_AliasedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypesProto3_AliasedEnum.Descriptor instead.
func (TestAllTypesProto3_AliasedEnum) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{0, 1}
}

type EnumOnlyProto3_Bool int32

const (
	EnumOnlyProto3_kFalse EnumOnlyProto3_Bool = 0
	EnumOnlyProto3_kTrue  EnumOnlyProto3_Bool = 1
)

// Enum value maps for EnumOnlyProto3_Bool.
var (
	EnumOnlyProto3_Bool_name = map[int32]string{
		0: "kFalse",
		1: "kTrue",
	}
	EnumOnlyProto3_Bool_value = map[string]int32{
		"kFalse": 0,
		"kTrue":  1,
	}
)

func (x EnumOnlyProto3_Bool) Enum() *EnumOnlyProto3_Bool {
	p := new(EnumOnlyProto3_Bool)
	*p = x
	return p
}

func (x EnumOnlyProto3_Bool) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumOnlyProto3_Bool) Descriptor() protoreflect.EnumDescriptor {
	return file_editions_golden_test_messages_proto3_editions_proto_enumTypes[3].Descriptor()
}

func (EnumOnlyProto3_Bool) Type() protoreflect.EnumType {
	return &file_editions_golden_test_messages_proto3_editions_proto_enumTypes[3]
}

func (x EnumOnlyProto3_Bool) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumOnlyProto3_Bool.Descriptor instead.
func (EnumOnlyProto3_Bool) EnumDescriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{3, 0}
}

// This proto includes every type of field in both singular and repeated
// forms.
//
// Also, crucially, all messages and enums in this file are eventually
// submessages of this message.  So for example, a fuzz test of TestAllTypes
// could trigger bugs that occur in any message type in this file.  We verify
// this stays true in a unit test.
type TestAllTypesProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Singular
	OptionalInt32          int32                             `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalInt64          int64                             `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalUint32         uint32                            `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalUint64         uint64                            `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSint32         int32                             `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalSint64         int64                             `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalFixed32        uint32                            `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFixed64        uint64                            `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       int32                             `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       int64                             `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFloat          float32                           `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalDouble         float64                           `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalBool           bool                              `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalString         string                            `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                            `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	OptionalNestedMessage  *TestAllTypesProto3_NestedMessage `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessage                   `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage" json:"optional_foreign_message,omitempty"`
	OptionalNestedEnum     TestAllTypesProto3_NestedEnum     `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    ForeignEnum                       `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,enum=protobuf_test_messages.editions.proto3.ForeignEnum" json:"optional_foreign_enum,omitempty"`
	OptionalAliasedEnum    TestAllTypesProto3_AliasedEnum    `protobuf:"varint,23,opt,name=optional_aliased_enum,json=optionalAliasedEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_AliasedEnum" json:"optional_aliased_enum,omitempty"`
	OptionalStringPiece    string                            `protobuf:"bytes,24,opt,name=optional_string_piece,json=optionalStringPiece" json:"optional_string_piece,omitempty"`
	OptionalCord           string                            `protobuf:"bytes,25,opt,name=optional_cord,json=optionalCord" json:"optional_cord,omitempty"`
	RecursiveMessage       *TestAllTypesProto3               `protobuf:"bytes,27,opt,name=recursive_message,json=recursiveMessage" json:"recursive_message,omitempty"`
	// Repeated
	RepeatedInt32          []int32                             `protobuf:"varint,31,rep,packed,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                             `protobuf:"varint,32,rep,packed,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                            `protobuf:"varint,33,rep,packed,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                            `protobuf:"varint,34,rep,packed,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                             `protobuf:"zigzag32,35,rep,packed,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                             `protobuf:"zigzag64,36,rep,packed,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                            `protobuf:"fixed32,37,rep,packed,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                            `protobuf:"fixed64,38,rep,packed,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                             `protobuf:"fixed32,39,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                             `protobuf:"fixed64,40,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                           `protobuf:"fixed32,41,rep,packed,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                           `protobuf:"fixed64,42,rep,packed,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                              `protobuf:"varint,43,rep,packed,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedString         []string                            `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                            `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	RepeatedNestedMessage  []*TestAllTypesProto3_NestedMessage `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessage                   `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage" json:"repeated_foreign_message,omitempty"`
	RepeatedNestedEnum     []TestAllTypesProto3_NestedEnum     `protobuf:"varint,51,rep,packed,name=repeated_nested_enum,json=repeatedNestedEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnum                       `protobuf:"varint,52,rep,packed,name=repeated_foreign_enum,json=repeatedForeignEnum,enum=protobuf_test_messages.editions.proto3.ForeignEnum" json:"repeated_foreign_enum,omitempty"`
	RepeatedStringPiece    []string                            `protobuf:"bytes,54,rep,name=repeated_string_piece,json=repeatedStringPiece" json:"repeated_string_piece,omitempty"`
	RepeatedCord           []string                            `protobuf:"bytes,55,rep,name=repeated_cord,json=repeatedCord" json:"repeated_cord,omitempty"`
	// Packed
	PackedInt32      []int32                         `protobuf:"varint,75,rep,packed,name=packed_int32,json=packedInt32" json:"packed_int32,omitempty"`
	PackedInt64      []int64                         `protobuf:"varint,76,rep,packed,name=packed_int64,json=packedInt64" json:"packed_int64,omitempty"`
	PackedUint32     []uint32                        `protobuf:"varint,77,rep,packed,name=packed_uint32,json=packedUint32" json:"packed_uint32,omitempty"`
	PackedUint64     []uint64                        `protobuf:"varint,78,rep,packed,name=packed_uint64,json=packedUint64" json:"packed_uint64,omitempty"`
	PackedSint32     []int32                         `protobuf:"zigzag32,79,rep,packed,name=packed_sint32,json=packedSint32" json:"packed_sint32,omitempty"`
	PackedSint64     []int64                         `protobuf:"zigzag64,80,rep,packed,name=packed_sint64,json=packedSint64" json:"packed_sint64,omitempty"`
	PackedFixed32    []uint32                        `protobuf:"fixed32,81,rep,packed,name=packed_fixed32,json=packedFixed32" json:"packed_fixed32,omitempty"`
	PackedFixed64    []uint64                        `protobuf:"fixed64,82,rep,packed,name=packed_fixed64,json=packedFixed64" json:"packed_fixed64,omitempty"`
	PackedSfixed32   []int32                         `protobuf:"fixed32,83,rep,packed,name=packed_sfixed32,json=packedSfixed32" json:"packed_sfixed32,omitempty"`
	PackedSfixed64   []int64                         `protobuf:"fixed64,84,rep,packed,name=packed_sfixed64,json=packedSfixed64" json:"packed_sfixed64,omitempty"`
	PackedFloat      []float32                       `protobuf:"fixed32,85,rep,packed,name=packed_float,json=packedFloat" json:"packed_float,omitempty"`
	PackedDouble     []float64                       `protobuf:"fixed64,86,rep,packed,name=packed_double,json=packedDouble" json:"packed_double,omitempty"`
	PackedBool       []bool                          `protobuf:"varint,87,rep,packed,name=packed_bool,json=packedBool" json:"packed_bool,omitempty"`
	PackedNestedEnum []TestAllTypesProto3_NestedEnum `protobuf:"varint,88,rep,packed,name=packed_nested_enum,json=packedNestedEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum" json:"packed_nested_enum,omitempty"`
	// Unpacked
	UnpackedInt32      []int32                         `protobuf:"varint,89,rep,name=unpacked_int32,json=unpackedInt32" json:"unpacked_int32,omitempty"`
	UnpackedInt64      []int64                         `protobuf:"varint,90,rep,name=unpacked_int64,json=unpackedInt64" json:"unpacked_int64,omitempty"`
	UnpackedUint32     []uint32                        `protobuf:"varint,91,rep,name=unpacked_uint32,json=unpackedUint32" json:"unpacked_uint32,omitempty"`
	UnpackedUint64     []uint64                        `protobuf:"varint,92,rep,name=unpacked_uint64,json=unpackedUint64" json:"unpacked_uint64,omitempty"`
	UnpackedSint32     []int32                         `protobuf:"zigzag32,93,rep,name=unpacked_sint32,json=unpackedSint32" json:"unpacked_sint32,omitempty"`
	UnpackedSint64     []int64                         `protobuf:"zigzag64,94,rep,name=unpacked_sint64,json=unpackedSint64" json:"unpacked_sint64,omitempty"`
	UnpackedFixed32    []uint32                        `protobuf:"fixed32,95,rep,name=unpacked_fixed32,json=unpackedFixed32" json:"unpacked_fixed32,omitempty"`
	UnpackedFixed64    []uint64                        `protobuf:"fixed64,96,rep,name=unpacked_fixed64,json=unpackedFixed64" json:"unpacked_fixed64,omitempty"`
	UnpackedSfixed32   []int32                         `protobuf:"fixed32,97,rep,name=unpacked_sfixed32,json=unpackedSfixed32" json:"unpacked_sfixed32,omitempty"`
	UnpackedSfixed64   []int64                         `protobuf:"fixed64,98,rep,name=unpacked_sfixed64,json=unpackedSfixed64" json:"unpacked_sfixed64,omitempty"`
	UnpackedFloat      []float32                       `protobuf:"fixed32,99,rep,name=unpacked_float,json=unpackedFloat" json:"unpacked_float,omitempty"`
	UnpackedDouble     []float64                       `protobuf:"fixed64,100,rep,name=unpacked_double,json=unpackedDouble" json:"unpacked_double,omitempty"`
	UnpackedBool       []bool                          `protobuf:"varint,101,rep,name=unpacked_bool,json=unpackedBool" json:"unpacked_bool,omitempty"`
	UnpackedNestedEnum []TestAllTypesProto3_NestedEnum `protobuf:"varint,102,rep,name=unpacked_nested_enum,json=unpackedNestedEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum" json:"unpacked_nested_enum,omitempty"`
	// Map
	MapInt32Int32           map[int32]int32                              `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Int64           map[int64]int64                              `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Uint32         map[uint32]uint32                            `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Uint64         map[uint64]uint64                            `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Sint32         map[int32]int32                              `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapSint64Sint64         map[int64]int64                              `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapFixed32Fixed32       map[uint32]uint32                            `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapFixed64Fixed64       map[uint64]uint64                            `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapSfixed32Sfixed32     map[int32]int32                              `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapSfixed64Sfixed64     map[int64]int64                              `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapInt32Float           map[int32]float32                            `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapInt32Double          map[int32]float64                            `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolBool             map[bool]bool                                `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringString         map[string]string                            `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringBytes          map[string][]byte                            `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedMessage  map[string]*TestAllTypesProto3_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringForeignMessage map[string]*ForeignMessage                   `protobuf:"bytes,72,rep,name=map_string_foreign_message,json=mapStringForeignMessage" json:"map_string_foreign_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedEnum     map[string]TestAllTypesProto3_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum"`
	MapStringForeignEnum    map[string]ForeignEnum                       `protobuf:"bytes,74,rep,name=map_string_foreign_enum,json=mapStringForeignEnum" json:"map_string_foreign_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.proto3.ForeignEnum"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypesProto3_OneofUint32
	//	*TestAllTypesProto3_OneofNestedMessage
	//	*TestAllTypesProto3_OneofString
	//	*TestAllTypesProto3_OneofBytes
	//	*TestAllTypesProto3_OneofBool
	//	*TestAllTypesProto3_OneofUint64
	//	*TestAllTypesProto3_OneofFloat
	//	*TestAllTypesProto3_OneofDouble
	//	*TestAllTypesProto3_OneofEnum
	//	*TestAllTypesProto3_OneofNullValue
	OneofField isTestAllTypesProto3_OneofField `protobuf_oneof:"oneof_field"`
	// Well-known types
	OptionalBoolWrapper   *wrapperspb.BoolValue     `protobuf:"bytes,201,opt,name=optional_bool_wrapper,json=optionalBoolWrapper" json:"optional_bool_wrapper,omitempty"`
	OptionalInt32Wrapper  *wrapperspb.Int32Value    `protobuf:"bytes,202,opt,name=optional_int32_wrapper,json=optionalInt32Wrapper" json:"optional_int32_wrapper,omitempty"`
	OptionalInt64Wrapper  *wrapperspb.Int64Value    `protobuf:"bytes,203,opt,name=optional_int64_wrapper,json=optionalInt64Wrapper" json:"optional_int64_wrapper,omitempty"`
	OptionalUint32Wrapper *wrapperspb.UInt32Value   `protobuf:"bytes,204,opt,name=optional_uint32_wrapper,json=optionalUint32Wrapper" json:"optional_uint32_wrapper,omitempty"`
	OptionalUint64Wrapper *wrapperspb.UInt64Value   `protobuf:"bytes,205,opt,name=optional_uint64_wrapper,json=optionalUint64Wrapper" json:"optional_uint64_wrapper,omitempty"`
	OptionalFloatWrapper  *wrapperspb.FloatValue    `protobuf:"bytes,206,opt,name=optional_float_wrapper,json=optionalFloatWrapper" json:"optional_float_wrapper,omitempty"`
	OptionalDoubleWrapper *wrapperspb.DoubleValue   `protobuf:"bytes,207,opt,name=optional_double_wrapper,json=optionalDoubleWrapper" json:"optional_double_wrapper,omitempty"`
	OptionalStringWrapper *wrapperspb.StringValue   `protobuf:"bytes,208,opt,name=optional_string_wrapper,json=optionalStringWrapper" json:"optional_string_wrapper,omitempty"`
	OptionalBytesWrapper  *wrapperspb.BytesValue    `protobuf:"bytes,209,opt,name=optional_bytes_wrapper,json=optionalBytesWrapper" json:"optional_bytes_wrapper,omitempty"`
	RepeatedBoolWrapper   []*wrapperspb.BoolValue   `protobuf:"bytes,211,rep,name=repeated_bool_wrapper,json=repeatedBoolWrapper" json:"repeated_bool_wrapper,omitempty"`
	RepeatedInt32Wrapper  []*wrapperspb.Int32Value  `protobuf:"bytes,212,rep,name=repeated_int32_wrapper,json=repeatedInt32Wrapper" json:"repeated_int32_wrapper,omitempty"`
	RepeatedInt64Wrapper  []*wrapperspb.Int64Value  `protobuf:"bytes,213,rep,name=repeated_int64_wrapper,json=repeatedInt64Wrapper" json:"repeated_int64_wrapper,omitempty"`
	RepeatedUint32Wrapper []*wrapperspb.UInt32Value `protobuf:"bytes,214,rep,name=repeated_uint32_wrapper,json=repeatedUint32Wrapper" json:"repeated_uint32_wrapper,omitempty"`
	RepeatedUint64Wrapper []*wrapperspb.UInt64Value `protobuf:"bytes,215,rep,name=repeated_uint64_wrapper,json=repeatedUint64Wrapper" json:"repeated_uint64_wrapper,omitempty"`
	RepeatedFloatWrapper  []*wrapperspb.FloatValue  `protobuf:"bytes,216,rep,name=repeated_float_wrapper,json=repeatedFloatWrapper" json:"repeated_float_wrapper,omitempty"`
	RepeatedDoubleWrapper []*wrapperspb.DoubleValue `protobuf:"bytes,217,rep,name=repeated_double_wrapper,json=repeatedDoubleWrapper" json:"repeated_double_wrapper,omitempty"`
	RepeatedStringWrapper []*wrapperspb.StringValue `protobuf:"bytes,218,rep,name=repeated_string_wrapper,json=repeatedStringWrapper" json:"repeated_string_wrapper,omitempty"`
	RepeatedBytesWrapper  []*wrapperspb.BytesValue  `protobuf:"bytes,219,rep,name=repeated_bytes_wrapper,json=repeatedBytesWrapper" json:"repeated_bytes_wrapper,omitempty"`
	OptionalDuration      *durationpb.Duration      `protobuf:"bytes,301,opt,name=optional_duration,json=optionalDuration" json:"optional_duration,omitempty"`
	OptionalTimestamp     *timestamppb.Timestamp    `protobuf:"bytes,302,opt,name=optional_timestamp,json=optionalTimestamp" json:"optional_timestamp,omitempty"`
	OptionalFieldMask     *fieldmaskpb.FieldMask    `protobuf:"bytes,303,opt,name=optional_field_mask,json=optionalFieldMask" json:"optional_field_mask,omitempty"`
	OptionalStruct        *structpb.Struct          `protobuf:"bytes,304,opt,name=optional_struct,json=optionalStruct" json:"optional_struct,omitempty"`
	OptionalAny           *anypb.Any                `protobuf:"bytes,305,opt,name=optional_any,json=optionalAny" json:"optional_any,omitempty"`
	OptionalValue         *structpb.Value           `protobuf:"bytes,306,opt,name=optional_value,json=optionalValue" json:"optional_value,omitempty"`
	OptionalNullValue     structpb.NullValue        `protobuf:"varint,307,opt,name=optional_null_value,json=optionalNullValue,enum=google.protobuf.NullValue" json:"optional_null_value,omitempty"`
	RepeatedDuration      []*durationpb.Duration    `protobuf:"bytes,311,rep,name=repeated_duration,json=repeatedDuration" json:"repeated_duration,omitempty"`
	RepeatedTimestamp     []*timestamppb.Timestamp  `protobuf:"bytes,312,rep,name=repeated_timestamp,json=repeatedTimestamp" json:"repeated_timestamp,omitempty"`
	RepeatedFieldmask     []*fieldmaskpb.FieldMask  `protobuf:"bytes,313,rep,name=repeated_fieldmask,json=repeatedFieldmask" json:"repeated_fieldmask,omitempty"`
	RepeatedStruct        []*structpb.Struct        `protobuf:"bytes,324,rep,name=repeated_struct,json=repeatedStruct" json:"repeated_struct,omitempty"`
	RepeatedAny           []*anypb.Any              `protobuf:"bytes,315,rep,name=repeated_any,json=repeatedAny" json:"repeated_any,omitempty"`
	RepeatedValue         []*structpb.Value         `protobuf:"bytes,316,rep,name=repeated_value,json=repeatedValue" json:"repeated_value,omitempty"`
	RepeatedListValue     []*structpb.ListValue     `protobuf:"bytes,317,rep,name=repeated_list_value,json=repeatedListValue" json:"repeated_list_value,omitempty"`
	// Test field-name-to-JSON-name convention.
	// (protobuf says names can be any valid C/C++ identifier.)
	Fieldname1    int32 `protobuf:"varint,401,opt,name=fieldname1" json:"fieldname1,omitempty"`
	FieldName2    int32 `protobuf:"varint,402,opt,name=field_name2,json=fieldName2" json:"field_name2,omitempty"`
	XFieldName3   int32 `protobuf:"varint,403,opt,name=_field_name3,json=FieldName3" json:"_field_name3,omitempty"`
	Field_Name4_  int32 `protobuf:"varint,404,opt,name=field__name4_,json=fieldName4" json:"field__name4_,omitempty"`
	Field0Name5   int32 `protobuf:"varint,405,opt,name=field0name5" json:"field0name5,omitempty"`
	Field_0Name6  int32 `protobuf:"varint,406,opt,name=field_0_name6,json=field0Name6" json:"field_0_name6,omitempty"`
	FieldName7    int32 `protobuf:"varint,407,opt,name=fieldName7" json:"fieldName7,omitempty"`
	FieldName8    int32 `protobuf:"varint,408,opt,name=FieldName8" json:"FieldName8,omitempty"`
	Field_Name9   int32 `protobuf:"varint,409,opt,name=field_Name9,json=fieldName9" json:"field_Name9,omitempty"`
	Field_Name10  int32 `protobuf:"varint,410,opt,name=Field_Name10,json=FieldName10" json:"Field_Name10,omitempty"`
	FIELD_NAME11  int32 `protobuf:"varint,411,opt,name=FIELD_NAME11,json=FIELDNAME11" json:"FIELD_NAME11,omitempty"`
	FIELDName12   int32 `protobuf:"varint,412,opt,name=FIELD_name12,json=FIELDName12" json:"FIELD_name12,omitempty"`
	XFieldName13  int32 `protobuf:"varint,413,opt,name=__field_name13,json=FieldName13" json:"__field_name13,omitempty"`
	X_FieldName14 int32 `protobuf:"varint,414,opt,name=__Field_name14,json=FieldName14" json:"__Field_name14,omitempty"`
	Field_Name15  int32 `protobuf:"varint,415,opt,name=field__name15,json=fieldName15" json:"field__name15,omitempty"`
	Field__Name16 int32 `protobuf:"varint,416,opt,name=field__Name16,json=fieldName16" json:"field__Name16,omitempty"`
	FieldName17__ int32 `protobuf:"varint,417,opt,name=field_name17__,json=fieldName17" json:"field_name17__,omitempty"`
	FieldName18__ int32 `protobuf:"varint,418,opt,name=Field_name18__,json=FieldName18" json:"Field_name18__,omitempty"`
}

func (x *TestAllTypesProto3) Reset() {
	*x = TestAllTypesProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto3) ProtoMessage() {}

func (x *TestAllTypesProto3) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto3.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto3) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypesProto3) GetOptionalInt32() int32 {
	if x != nil {
		return x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalInt64() int64 {
	if x != nil {
		return x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalUint32() uint32 {
	if x != nil {
		return x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalUint64() uint64 {
	if x != nil {
		return x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalSint32() int32 {
	if x != nil {
		return x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalSint64() int64 {
	if x != nil {
		return x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalFixed32() uint32 {
	if x != nil {
		return x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalFixed64() uint64 {
	if x != nil {
		return x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalSfixed32() int32 {
	if x != nil {
		return x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalSfixed64() int64 {
	if x != nil {
		return x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalFloat() float32 {
	if x != nil {
		return x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalDouble() float64 {
	if x != nil {
		return x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypesProto3) GetOptionalBool() bool {
	if x != nil {
		return x.OptionalBool
	}
	return false
}

func (x *TestAllTypesProto3) GetOptionalString() string {
	if x != nil {
		return x.OptionalString
	}
	return ""
}

func (x *TestAllTypesProto3) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalNestedMessage() *TestAllTypesProto3_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalForeignMessage() *ForeignMessage {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalNestedEnum() TestAllTypesProto3_NestedEnum {
	if x != nil {
		return x.OptionalNestedEnum
	}
	return TestAllTypesProto3_FOO
}

func (x *TestAllTypesProto3) GetOptionalForeignEnum() ForeignEnum {
	if x != nil {
		return x.OptionalForeignEnum
	}
	return ForeignEnum_FOREIGN_FOO
}

func (x *TestAllTypesProto3) GetOptionalAliasedEnum() TestAllTypesProto3_AliasedEnum {
	if x != nil {
		return x.OptionalAliasedEnum
	}
	return TestAllTypesProto3_ALIAS_FOO
}

func (x *TestAllTypesProto3) GetOptionalStringPiece() string {
	if x != nil {
		return x.OptionalStringPiece
	}
	return ""
}

func (x *TestAllTypesProto3) GetOptionalCord() string {
	if x != nil {
		return x.OptionalCord
	}
	return ""
}

func (x *TestAllTypesProto3) GetRecursiveMessage() *TestAllTypesProto3 {
	if x != nil {
		return x.RecursiveMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedNestedMessage() []*TestAllTypesProto3_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedForeignMessage() []*ForeignMessage {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedNestedEnum() []TestAllTypesProto3_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedForeignEnum() []ForeignEnum {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedStringPiece() []string {
	if x != nil {
		return x.RepeatedStringPiece
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedCord() []string {
	if x != nil {
		return x.RepeatedCord
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedInt32() []int32 {
	if x != nil {
		return x.PackedInt32
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedInt64() []int64 {
	if x != nil {
		return x.PackedInt64
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedUint32() []uint32 {
	if x != nil {
		return x.PackedUint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedUint64() []uint64 {
	if x != nil {
		return x.PackedUint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedSint32() []int32 {
	if x != nil {
		return x.PackedSint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedSint64() []int64 {
	if x != nil {
		return x.PackedSint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedFixed32() []uint32 {
	if x != nil {
		return x.PackedFixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedFixed64() []uint64 {
	if x != nil {
		return x.PackedFixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedSfixed32() []int32 {
	if x != nil {
		return x.PackedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedSfixed64() []int64 {
	if x != nil {
		return x.PackedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedFloat() []float32 {
	if x != nil {
		return x.PackedFloat
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedDouble() []float64 {
	if x != nil {
		return x.PackedDouble
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedBool() []bool {
	if x != nil {
		return x.PackedBool
	}
	return nil
}

func (x *TestAllTypesProto3) GetPackedNestedEnum() []TestAllTypesProto3_NestedEnum {
	if x != nil {
		return x.PackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedInt32() []int32 {
	if x != nil {
		return x.UnpackedInt32
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedInt64() []int64 {
	if x != nil {
		return x.UnpackedInt64
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedUint32() []uint32 {
	if x != nil {
		return x.UnpackedUint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedUint64() []uint64 {
	if x != nil {
		return x.UnpackedUint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedSint32() []int32 {
	if x != nil {
		return x.UnpackedSint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedSint64() []int64 {
	if x != nil {
		return x.UnpackedSint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedFixed32() []uint32 {
	if x != nil {
		return x.UnpackedFixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedFixed64() []uint64 {
	if x != nil {
		return x.UnpackedFixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedSfixed32() []int32 {
	if x != nil {
		return x.UnpackedSfixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedSfixed64() []int64 {
	if x != nil {
		return x.UnpackedSfixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedFloat() []float32 {
	if x != nil {
		return x.UnpackedFloat
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedDouble() []float64 {
	if x != nil {
		return x.UnpackedDouble
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedBool() []bool {
	if x != nil {
		return x.UnpackedBool
	}
	return nil
}

func (x *TestAllTypesProto3) GetUnpackedNestedEnum() []TestAllTypesProto3_NestedEnum {
	if x != nil {
		return x.UnpackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringNestedMessage() map[string]*TestAllTypesProto3_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringForeignMessage() map[string]*ForeignMessage {
	if x != nil {
		return x.MapStringForeignMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringNestedEnum() map[string]TestAllTypesProto3_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (x *TestAllTypesProto3) GetMapStringForeignEnum() map[string]ForeignEnum {
	if x != nil {
		return x.MapStringForeignEnum
	}
	return nil
}

func (m *TestAllTypesProto3) GetOneofField() isTestAllTypesProto3_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypesProto3) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypesProto3) GetOneofNestedMessage() *TestAllTypesProto3_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypesProto3) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypesProto3) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypesProto3) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypesProto3) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypesProto3) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypesProto3) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypesProto3) GetOneofEnum() TestAllTypesProto3_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypesProto3_FOO
}

func (x *TestAllTypesProto3) GetOneofNullValue() structpb.NullValue {
	if x, ok := x.GetOneofField().(*TestAllTypesProto3_OneofNullValue); ok {
		return x.OneofNullValue
	}
	return structpb.NullValue(0)
}

func (x *TestAllTypesProto3) GetOptionalBoolWrapper() *wrapperspb.BoolValue {
	if x != nil {
		return x.OptionalBoolWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalInt32Wrapper() *wrapperspb.Int32Value {
	if x != nil {
		return x.OptionalInt32Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalInt64Wrapper() *wrapperspb.Int64Value {
	if x != nil {
		return x.OptionalInt64Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalUint32Wrapper() *wrapperspb.UInt32Value {
	if x != nil {
		return x.OptionalUint32Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalUint64Wrapper() *wrapperspb.UInt64Value {
	if x != nil {
		return x.OptionalUint64Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalFloatWrapper() *wrapperspb.FloatValue {
	if x != nil {
		return x.OptionalFloatWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalDoubleWrapper() *wrapperspb.DoubleValue {
	if x != nil {
		return x.OptionalDoubleWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalStringWrapper() *wrapperspb.StringValue {
	if x != nil {
		return x.OptionalStringWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalBytesWrapper() *wrapperspb.BytesValue {
	if x != nil {
		return x.OptionalBytesWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedBoolWrapper() []*wrapperspb.BoolValue {
	if x != nil {
		return x.RepeatedBoolWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedInt32Wrapper() []*wrapperspb.Int32Value {
	if x != nil {
		return x.RepeatedInt32Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedInt64Wrapper() []*wrapperspb.Int64Value {
	if x != nil {
		return x.RepeatedInt64Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedUint32Wrapper() []*wrapperspb.UInt32Value {
	if x != nil {
		return x.RepeatedUint32Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedUint64Wrapper() []*wrapperspb.UInt64Value {
	if x != nil {
		return x.RepeatedUint64Wrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedFloatWrapper() []*wrapperspb.FloatValue {
	if x != nil {
		return x.RepeatedFloatWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedDoubleWrapper() []*wrapperspb.DoubleValue {
	if x != nil {
		return x.RepeatedDoubleWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedStringWrapper() []*wrapperspb.StringValue {
	if x != nil {
		return x.RepeatedStringWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedBytesWrapper() []*wrapperspb.BytesValue {
	if x != nil {
		return x.RepeatedBytesWrapper
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalDuration() *durationpb.Duration {
	if x != nil {
		return x.OptionalDuration
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OptionalTimestamp
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalFieldMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.OptionalFieldMask
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalStruct() *structpb.Struct {
	if x != nil {
		return x.OptionalStruct
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalAny() *anypb.Any {
	if x != nil {
		return x.OptionalAny
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalValue() *structpb.Value {
	if x != nil {
		return x.OptionalValue
	}
	return nil
}

func (x *TestAllTypesProto3) GetOptionalNullValue() structpb.NullValue {
	if x != nil {
		return x.OptionalNullValue
	}
	return structpb.NullValue(0)
}

func (x *TestAllTypesProto3) GetRepeatedDuration() []*durationpb.Duration {
	if x != nil {
		return x.RepeatedDuration
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedTimestamp() []*timestamppb.Timestamp {
	if x != nil {
		return x.RepeatedTimestamp
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedFieldmask() []*fieldmaskpb.FieldMask {
	if x != nil {
		return x.RepeatedFieldmask
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedStruct() []*structpb.Struct {
	if x != nil {
		return x.RepeatedStruct
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedAny() []*anypb.Any {
	if x != nil {
		return x.RepeatedAny
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedValue() []*structpb.Value {
	if x != nil {
		return x.RepeatedValue
	}
	return nil
}

func (x *TestAllTypesProto3) GetRepeatedListValue() []*structpb.ListValue {
	if x != nil {
		return x.RepeatedListValue
	}
	return nil
}

func (x *TestAllTypesProto3) GetFieldname1() int32 {
	if x != nil {
		return x.Fieldname1
	}
	return 0
}

func (x *TestAllTypesProto3) GetFieldName2() int32 {
	if x != nil {
		return x.FieldName2
	}
	return 0
}

func (x *TestAllTypesProto3) GetXFieldName3() int32 {
	if x != nil {
		return x.XFieldName3
	}
	return 0
}

func (x *TestAllTypesProto3) GetField_Name4_() int32 {
	if x != nil {
		return x.Field_Name4_
	}
	return 0
}

func (x *TestAllTypesProto3) GetField0Name5() int32 {
	if x != nil {
		return x.Field0Name5
	}
	return 0
}

func (x *TestAllTypesProto3) GetField_0Name6() int32 {
	if x != nil {
		return x.Field_0Name6
	}
	return 0
}

func (x *TestAllTypesProto3) GetFieldName7() int32 {
	if x != nil {
		return x.FieldName7
	}
	return 0
}

func (x *TestAllTypesProto3) GetFieldName8() int32 {
	if x != nil {
		return x.FieldName8
	}
	return 0
}

func (x *TestAllTypesProto3) GetField_Name9() int32 {
	if x != nil {
		return x.Field_Name9
	}
	return 0
}

func (x *TestAllTypesProto3) GetField_Name10() int32 {
	if x != nil {
		return x.Field_Name10
	}
	return 0
}

func (x *TestAllTypesProto3) GetFIELD_NAME11() int32 {
	if x != nil {
		return x.FIELD_NAME11
	}
	return 0
}

func (x *TestAllTypesProto3) GetFIELDName12() int32 {
	if x != nil {
		return x.FIELDName12
	}
	return 0
}

func (x *TestAllTypesProto3) GetXFieldName13() int32 {
	if x != nil {
		return x.XFieldName13
	}
	return 0
}

func (x *TestAllTypesProto3) GetX_FieldName14() int32 {
	if x != nil {
		return x.X_FieldName14
	}
	return 0
}

func (x *TestAllTypesProto3) GetField_Name15() int32 {
	if x != nil {
		return x.Field_Name15
	}
	return 0
}

func (x *TestAllTypesProto3) GetField__Name16() int32 {
	if x != nil {
		return x.Field__Name16
	}
	return 0
}

func (x *TestAllTypesProto3) GetFieldName17__() int32 {
	if x != nil {
		return x.FieldName17__
	}
	return 0
}

func (x *TestAllTypesProto3) GetFieldName18__() int32 {
	if x != nil {
		return x.FieldName18__
	}
	return 0
}

type isTestAllTypesProto3_OneofField interface {
	isTestAllTypesProto3_OneofField()
}

type TestAllTypesProto3_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type TestAllTypesProto3_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypesProto3_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,oneof"`
}

type TestAllTypesProto3_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,oneof"`
}

type TestAllTypesProto3_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type TestAllTypesProto3_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type TestAllTypesProto3_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type TestAllTypesProto3_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type TestAllTypesProto3_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type TestAllTypesProto3_OneofEnum struct {
	OneofEnum TestAllTypesProto3_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,enum=protobuf_test_messages.editions.proto3.TestAllTypesProto3_NestedEnum,oneof"`
}

type TestAllTypesProto3_OneofNullValue struct {
	OneofNullValue structpb.NullValue `protobuf:"varint,120,opt,name=oneof_null_value,json=oneofNullValue,enum=google.protobuf.NullValue,oneof"`
}

func (*TestAllTypesProto3_OneofUint32) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofNestedMessage) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofString) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofBytes) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofBool) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofUint64) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofFloat) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofDouble) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofEnum) isTestAllTypesProto3_OneofField() {}

func (*TestAllTypesProto3_OneofNullValue) isTestAllTypesProto3_OneofField() {}

type ForeignMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
}

func (x *ForeignMessage) Reset() {
	*x = ForeignMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessage) ProtoMessage() {}

func (x *ForeignMessage) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessage.ProtoReflect.Descriptor instead.
func (*ForeignMessage) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{1}
}

func (x *ForeignMessage) GetC() int32 {
	if x != nil {
		return x.C
	}
	return 0
}

type NullHypothesisProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NullHypothesisProto3) Reset() {
	*x = NullHypothesisProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NullHypothesisProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NullHypothesisProto3) ProtoMessage() {}

func (x *NullHypothesisProto3) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NullHypothesisProto3.ProtoReflect.Descriptor instead.
func (*NullHypothesisProto3) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{2}
}

type EnumOnlyProto3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EnumOnlyProto3) Reset() {
	*x = EnumOnlyProto3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnumOnlyProto3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnumOnlyProto3) ProtoMessage() {}

func (x *EnumOnlyProto3) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnumOnlyProto3.ProtoReflect.Descriptor instead.
func (*EnumOnlyProto3) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{3}
}

type TestAllTypesProto3_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           int32               `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllTypesProto3 `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllTypesProto3_NestedMessage) Reset() {
	*x = TestAllTypesProto3_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesProto3_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesProto3_NestedMessage) ProtoMessage() {}

func (x *TestAllTypesProto3_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_editions_golden_test_messages_proto3_editions_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesProto3_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypesProto3_NestedMessage) Descriptor() ([]byte, []int) {
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypesProto3_NestedMessage) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *TestAllTypesProto3_NestedMessage) GetCorecursive() *TestAllTypesProto3 {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

var File_editions_golden_test_messages_proto3_editions_proto protoreflect.FileDescriptor

var file_editions_golden_test_messages_proto3_editions_proto_rawDesc = []byte{
	0x0a, 0x33, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x65,
	0x6e, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61,
	0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe9, 0x5d, 0x0a, 0x12, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a,
	0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10,
	0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66,
	0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74,
	0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x70, 0x0a, 0x18, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x77, 0x0a, 0x14,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x67, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x7a,
	0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73,
	0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x46, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x6c, 0x69, 0x61, 0x73, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x36, 0x0a, 0x15, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69,
	0x65, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08, 0x02, 0x52, 0x13, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x65,
	0x63, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x72, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x72, 0x64, 0x12, 0x67, 0x0a, 0x11, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x52, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x20, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x21, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x22,
	0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x23, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07,
	0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x26, 0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x28,
	0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x29, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x2c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x17,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x70,
	0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x31, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x77, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x45,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x67, 0x0a, 0x15, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x34, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x36, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65, 0x18, 0x36, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x08, 0x02, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x37, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x6f, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x4b, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x4c, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x4d, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x4e, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x4f, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x50, 0x20, 0x03, 0x28, 0x12, 0x52,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x25, 0x0a,
	0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x51, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x52, 0x20, 0x03, 0x28, 0x06, 0x52, 0x0d, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x53,
	0x20, 0x03, 0x28, 0x0f, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x54, 0x20, 0x03, 0x28, 0x10, 0x52, 0x0e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x55, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x23, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x18, 0x56, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x57, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x73, 0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x58, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x10, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x0e, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x59, 0x20,
	0x03, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5a, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x0d,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x04,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x11,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x12,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x30, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x5f, 0x20, 0x03, 0x28,
	0x07, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x30, 0x0a, 0x10, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x60, 0x20,
	0x03, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61,
	0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x32, 0x0a, 0x11, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x18, 0x61, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x10, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x32, 0x0a, 0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0x62, 0x20, 0x03, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x02, 0x52, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x12, 0x2c, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x03, 0x28, 0x02, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x18, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x18, 0x64, 0x20, 0x03, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x12, 0x2a, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x18, 0x65, 0x20, 0x03, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52,
	0x0c, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x7e, 0x0a,
	0x14, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x66, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x75, 0x0a,
	0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x75, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61,
	0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x7b, 0x0a, 0x11, 0x6d,
	0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x3a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e,
	0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x7b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x81, 0x01, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x3e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x81, 0x01, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x3f, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61,
	0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x40, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61,
	0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x41, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61,
	0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x75, 0x0a, 0x0f, 0x6d,
	0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x42,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x78, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61,
	0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x6f, 0x0a, 0x0d,
	0x6d, 0x61, 0x70, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x44, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e,
	0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x7b, 0x0a,
	0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x45, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x78, 0x0a, 0x10, 0x6d, 0x61,
	0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x46,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x91, 0x01, 0x0a, 0x19, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x47, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x16, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x94, 0x01, 0x0a, 0x1a, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x48, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x57, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x88, 0x01, 0x0a, 0x16, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x49, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x53, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x8b, 0x01, 0x0a, 0x17, 0x6d,
	0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x4a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x14, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00,
	0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x7c, 0x0a,
	0x14, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x72, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0x73, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x74, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02, 0x48, 0x00,
	0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23, 0x0a, 0x0c,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x12, 0x66, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18,
	0x77, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x46, 0x0a, 0x10, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x78, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x0e, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x4f, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xc9, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xca, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x18, 0xcb, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x17, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x77, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xcc, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x12, 0x55, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xcd, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x18, 0xce, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x17,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f,
	0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xcf, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xd0,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x18, 0xd1, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x4f,
	0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f,
	0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xd3, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12,
	0x52, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xd4, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x57, 0x72, 0x61, 0x70,
	0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xd5, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x72, 0x18, 0xd6, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x55,
	0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xd7, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x55, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x57, 0x72,
	0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18,
	0xd8, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x17, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x18, 0xd9, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x12, 0x55, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x18, 0xda, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x18, 0xdb, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x11, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0xad, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0xae, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x4b, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0xaf, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x11, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x41, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x18, 0xb0, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x12, 0x38, 0x0a, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x6e, 0x79,
	0x18, 0xb1, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x0b, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x6e, 0x79, 0x12, 0x3e, 0x0a, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xb2, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4b, 0x0a, 0x13, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0xb3, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x75,
	0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x47, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0xb7, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x4a, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0xb8, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x4a, 0x0a, 0x12,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0xb9, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x41, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x18, 0xc4, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x12, 0x38, 0x0a, 0x0c, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6e, 0x79, 0x18, 0xbb, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x6e, 0x79, 0x12, 0x3e, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xbc, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x4b, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0xbd, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x31,
	0x18, 0x91, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x6e, 0x61,
	0x6d, 0x65, 0x31, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x32, 0x18, 0x92, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x32, 0x12, 0x21, 0x0a, 0x0c, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x33, 0x18, 0x93, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x33, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x34, 0x5f, 0x18, 0x94, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x34, 0x12, 0x21, 0x0a, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x30, 0x6e, 0x61, 0x6d, 0x65, 0x35, 0x18, 0x95, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x30, 0x6e, 0x61, 0x6d, 0x65, 0x35, 0x12,
	0x23, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x30, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x36,
	0x18, 0x96, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x30, 0x4e,
	0x61, 0x6d, 0x65, 0x36, 0x12, 0x1f, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x37, 0x18, 0x97, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x37, 0x12, 0x1f, 0x0a, 0x0a, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61,
	0x6d, 0x65, 0x38, 0x18, 0x98, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x38, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x4e, 0x61, 0x6d, 0x65, 0x39, 0x18, 0x99, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x39, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x30, 0x18, 0x9a, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x30, 0x12, 0x22, 0x0a, 0x0c,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x31, 0x31, 0x18, 0x9b, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x4e, 0x41, 0x4d, 0x45, 0x31, 0x31,
	0x12, 0x22, 0x0a, 0x0c, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x32,
	0x18, 0x9c, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x4e, 0x61,
	0x6d, 0x65, 0x31, 0x32, 0x12, 0x24, 0x0a, 0x0e, 0x5f, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x31, 0x33, 0x18, 0x9d, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x33, 0x12, 0x24, 0x0a, 0x0e, 0x5f, 0x5f,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x34, 0x18, 0x9e, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x34,
	0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31,
	0x35, 0x18, 0x9f, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e,
	0x61, 0x6d, 0x65, 0x31, 0x35, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x5f,
	0x4e, 0x61, 0x6d, 0x65, 0x31, 0x36, 0x18, 0xa0, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x36, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x37, 0x5f, 0x5f, 0x18, 0xa1, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x31, 0x37,
	0x12, 0x24, 0x0a, 0x0e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x31, 0x38,
	0x5f, 0x5f, 0x18, 0xa2, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x31, 0x38, 0x1a, 0x7b, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x5c, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72,
	0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73,
	0x69, 0x76, 0x65, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d,
	0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x07, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a,
	0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d,
	0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x42,
	0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x93, 0x01, 0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x5e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x82, 0x01, 0x0a, 0x1c, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
	0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8d, 0x01, 0x0a, 0x18, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x7c, 0x0a, 0x19, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x42, 0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x22, 0x59, 0x0a, 0x0b, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x46, 0x4f, 0x4f, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x41, 0x4c, 0x49, 0x41, 0x53, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12,
	0x07, 0x0a, 0x03, 0x4d, 0x4f, 0x4f, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x6d, 0x6f, 0x6f, 0x10,
	0x02, 0x12, 0x07, 0x0a, 0x03, 0x62, 0x41, 0x7a, 0x10, 0x02, 0x1a, 0x02, 0x10, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4a, 0x06, 0x08,
	0xf5, 0x03, 0x10, 0xff, 0x03, 0x22, 0x1e, 0x0a, 0x0e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x63, 0x22, 0x16, 0x0a, 0x14, 0x4e, 0x75, 0x6c, 0x6c, 0x48, 0x79, 0x70,
	0x6f, 0x74, 0x68, 0x65, 0x73, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x22, 0x2f, 0x0a,
	0x0e, 0x45, 0x6e, 0x75, 0x6d, 0x4f, 0x6e, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x22,
	0x1d, 0x0a, 0x04, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x0a, 0x0a, 0x06, 0x6b, 0x46, 0x61, 0x6c, 0x73,
	0x65, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x6b, 0x54, 0x72, 0x75, 0x65, 0x10, 0x01, 0x2a, 0x40,
	0x0a, 0x0b, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0f, 0x0a,
	0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x01, 0x12,
	0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x02,
	0x42, 0x4b, 0x0a, 0x31, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33, 0x48, 0x01, 0xa2, 0x02, 0x0e, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x92, 0x03, 0x02, 0x08, 0x02, 0x62, 0x08, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_editions_golden_test_messages_proto3_editions_proto_rawDescOnce sync.Once
	file_editions_golden_test_messages_proto3_editions_proto_rawDescData = file_editions_golden_test_messages_proto3_editions_proto_rawDesc
)

func file_editions_golden_test_messages_proto3_editions_proto_rawDescGZIP() []byte {
	file_editions_golden_test_messages_proto3_editions_proto_rawDescOnce.Do(func() {
		file_editions_golden_test_messages_proto3_editions_proto_rawDescData = protoimpl.X.CompressGZIP(file_editions_golden_test_messages_proto3_editions_proto_rawDescData)
	})
	return file_editions_golden_test_messages_proto3_editions_proto_rawDescData
}

var file_editions_golden_test_messages_proto3_editions_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_editions_golden_test_messages_proto3_editions_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_editions_golden_test_messages_proto3_editions_proto_goTypes = []interface{}{
	(ForeignEnum)(0),                         // 0: protobuf_test_messages.editions.proto3.ForeignEnum
	(TestAllTypesProto3_NestedEnum)(0),       // 1: protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	(TestAllTypesProto3_AliasedEnum)(0),      // 2: protobuf_test_messages.editions.proto3.TestAllTypesProto3.AliasedEnum
	(EnumOnlyProto3_Bool)(0),                 // 3: protobuf_test_messages.editions.proto3.EnumOnlyProto3.Bool
	(*TestAllTypesProto3)(nil),               // 4: protobuf_test_messages.editions.proto3.TestAllTypesProto3
	(*ForeignMessage)(nil),                   // 5: protobuf_test_messages.editions.proto3.ForeignMessage
	(*NullHypothesisProto3)(nil),             // 6: protobuf_test_messages.editions.proto3.NullHypothesisProto3
	(*EnumOnlyProto3)(nil),                   // 7: protobuf_test_messages.editions.proto3.EnumOnlyProto3
	(*TestAllTypesProto3_NestedMessage)(nil), // 8: protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage
	nil,                                      // 9: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32Int32Entry
	nil,                                      // 10: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt64Int64Entry
	nil,                                      // 11: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapUint32Uint32Entry
	nil,                                      // 12: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapUint64Uint64Entry
	nil,                                      // 13: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSint32Sint32Entry
	nil,                                      // 14: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSint64Sint64Entry
	nil,                                      // 15: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapFixed32Fixed32Entry
	nil,                                      // 16: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapFixed64Fixed64Entry
	nil,                                      // 17: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSfixed32Sfixed32Entry
	nil,                                      // 18: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSfixed64Sfixed64Entry
	nil,                                      // 19: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32FloatEntry
	nil,                                      // 20: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32DoubleEntry
	nil,                                      // 21: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapBoolBoolEntry
	nil,                                      // 22: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringStringEntry
	nil,                                      // 23: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringBytesEntry
	nil,                                      // 24: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedMessageEntry
	nil,                                      // 25: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignMessageEntry
	nil,                                      // 26: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedEnumEntry
	nil,                                      // 27: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignEnumEntry
	(structpb.NullValue)(0),                  // 28: google.protobuf.NullValue
	(*wrapperspb.BoolValue)(nil),             // 29: google.protobuf.BoolValue
	(*wrapperspb.Int32Value)(nil),            // 30: google.protobuf.Int32Value
	(*wrapperspb.Int64Value)(nil),            // 31: google.protobuf.Int64Value
	(*wrapperspb.UInt32Value)(nil),           // 32: google.protobuf.UInt32Value
	(*wrapperspb.UInt64Value)(nil),           // 33: google.protobuf.UInt64Value
	(*wrapperspb.FloatValue)(nil),            // 34: google.protobuf.FloatValue
	(*wrapperspb.DoubleValue)(nil),           // 35: google.protobuf.DoubleValue
	(*wrapperspb.StringValue)(nil),           // 36: google.protobuf.StringValue
	(*wrapperspb.BytesValue)(nil),            // 37: google.protobuf.BytesValue
	(*durationpb.Duration)(nil),              // 38: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),            // 39: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),            // 40: google.protobuf.FieldMask
	(*structpb.Struct)(nil),                  // 41: google.protobuf.Struct
	(*anypb.Any)(nil),                        // 42: google.protobuf.Any
	(*structpb.Value)(nil),                   // 43: google.protobuf.Value
	(*structpb.ListValue)(nil),               // 44: google.protobuf.ListValue
}
var file_editions_golden_test_messages_proto3_editions_proto_depIdxs = []int32{
	8,  // 0: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_nested_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage
	5,  // 1: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_foreign_message:type_name -> protobuf_test_messages.editions.proto3.ForeignMessage
	1,  // 2: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_nested_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	0,  // 3: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_foreign_enum:type_name -> protobuf_test_messages.editions.proto3.ForeignEnum
	2,  // 4: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_aliased_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.AliasedEnum
	4,  // 5: protobuf_test_messages.editions.proto3.TestAllTypesProto3.recursive_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3
	8,  // 6: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_nested_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage
	5,  // 7: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_foreign_message:type_name -> protobuf_test_messages.editions.proto3.ForeignMessage
	1,  // 8: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_nested_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	0,  // 9: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_foreign_enum:type_name -> protobuf_test_messages.editions.proto3.ForeignEnum
	1,  // 10: protobuf_test_messages.editions.proto3.TestAllTypesProto3.packed_nested_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	1,  // 11: protobuf_test_messages.editions.proto3.TestAllTypesProto3.unpacked_nested_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	9,  // 12: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_int32_int32:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32Int32Entry
	10, // 13: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_int64_int64:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt64Int64Entry
	11, // 14: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_uint32_uint32:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapUint32Uint32Entry
	12, // 15: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_uint64_uint64:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapUint64Uint64Entry
	13, // 16: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_sint32_sint32:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSint32Sint32Entry
	14, // 17: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_sint64_sint64:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSint64Sint64Entry
	15, // 18: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_fixed32_fixed32:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapFixed32Fixed32Entry
	16, // 19: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_fixed64_fixed64:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapFixed64Fixed64Entry
	17, // 20: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_sfixed32_sfixed32:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSfixed32Sfixed32Entry
	18, // 21: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_sfixed64_sfixed64:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapSfixed64Sfixed64Entry
	19, // 22: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_int32_float:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32FloatEntry
	20, // 23: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_int32_double:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapInt32DoubleEntry
	21, // 24: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_bool_bool:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapBoolBoolEntry
	22, // 25: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_string:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringStringEntry
	23, // 26: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_bytes:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringBytesEntry
	24, // 27: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_nested_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedMessageEntry
	25, // 28: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_foreign_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignMessageEntry
	26, // 29: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_nested_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedEnumEntry
	27, // 30: protobuf_test_messages.editions.proto3.TestAllTypesProto3.map_string_foreign_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignEnumEntry
	8,  // 31: protobuf_test_messages.editions.proto3.TestAllTypesProto3.oneof_nested_message:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage
	1,  // 32: protobuf_test_messages.editions.proto3.TestAllTypesProto3.oneof_enum:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	28, // 33: protobuf_test_messages.editions.proto3.TestAllTypesProto3.oneof_null_value:type_name -> google.protobuf.NullValue
	29, // 34: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_bool_wrapper:type_name -> google.protobuf.BoolValue
	30, // 35: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_int32_wrapper:type_name -> google.protobuf.Int32Value
	31, // 36: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_int64_wrapper:type_name -> google.protobuf.Int64Value
	32, // 37: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_uint32_wrapper:type_name -> google.protobuf.UInt32Value
	33, // 38: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_uint64_wrapper:type_name -> google.protobuf.UInt64Value
	34, // 39: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_float_wrapper:type_name -> google.protobuf.FloatValue
	35, // 40: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_double_wrapper:type_name -> google.protobuf.DoubleValue
	36, // 41: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_string_wrapper:type_name -> google.protobuf.StringValue
	37, // 42: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_bytes_wrapper:type_name -> google.protobuf.BytesValue
	29, // 43: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_bool_wrapper:type_name -> google.protobuf.BoolValue
	30, // 44: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_int32_wrapper:type_name -> google.protobuf.Int32Value
	31, // 45: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_int64_wrapper:type_name -> google.protobuf.Int64Value
	32, // 46: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_uint32_wrapper:type_name -> google.protobuf.UInt32Value
	33, // 47: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_uint64_wrapper:type_name -> google.protobuf.UInt64Value
	34, // 48: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_float_wrapper:type_name -> google.protobuf.FloatValue
	35, // 49: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_double_wrapper:type_name -> google.protobuf.DoubleValue
	36, // 50: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_string_wrapper:type_name -> google.protobuf.StringValue
	37, // 51: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_bytes_wrapper:type_name -> google.protobuf.BytesValue
	38, // 52: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_duration:type_name -> google.protobuf.Duration
	39, // 53: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_timestamp:type_name -> google.protobuf.Timestamp
	40, // 54: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_field_mask:type_name -> google.protobuf.FieldMask
	41, // 55: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_struct:type_name -> google.protobuf.Struct
	42, // 56: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_any:type_name -> google.protobuf.Any
	43, // 57: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_value:type_name -> google.protobuf.Value
	28, // 58: protobuf_test_messages.editions.proto3.TestAllTypesProto3.optional_null_value:type_name -> google.protobuf.NullValue
	38, // 59: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_duration:type_name -> google.protobuf.Duration
	39, // 60: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_timestamp:type_name -> google.protobuf.Timestamp
	40, // 61: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_fieldmask:type_name -> google.protobuf.FieldMask
	41, // 62: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_struct:type_name -> google.protobuf.Struct
	42, // 63: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_any:type_name -> google.protobuf.Any
	43, // 64: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_value:type_name -> google.protobuf.Value
	44, // 65: protobuf_test_messages.editions.proto3.TestAllTypesProto3.repeated_list_value:type_name -> google.protobuf.ListValue
	4,  // 66: protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage.corecursive:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3
	8,  // 67: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedMessageEntry.value:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedMessage
	5,  // 68: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignMessageEntry.value:type_name -> protobuf_test_messages.editions.proto3.ForeignMessage
	1,  // 69: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringNestedEnumEntry.value:type_name -> protobuf_test_messages.editions.proto3.TestAllTypesProto3.NestedEnum
	0,  // 70: protobuf_test_messages.editions.proto3.TestAllTypesProto3.MapStringForeignEnumEntry.value:type_name -> protobuf_test_messages.editions.proto3.ForeignEnum
	71, // [71:71] is the sub-list for method output_type
	71, // [71:71] is the sub-list for method input_type
	71, // [71:71] is the sub-list for extension type_name
	71, // [71:71] is the sub-list for extension extendee
	0,  // [0:71] is the sub-list for field type_name
}

func init() { file_editions_golden_test_messages_proto3_editions_proto_init() }
func file_editions_golden_test_messages_proto3_editions_proto_init() {
	if File_editions_golden_test_messages_proto3_editions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_editions_golden_test_messages_proto3_editions_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto3_editions_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto3_editions_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NullHypothesisProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto3_editions_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnumOnlyProto3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_editions_golden_test_messages_proto3_editions_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesProto3_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_editions_golden_test_messages_proto3_editions_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypesProto3_OneofUint32)(nil),
		(*TestAllTypesProto3_OneofNestedMessage)(nil),
		(*TestAllTypesProto3_OneofString)(nil),
		(*TestAllTypesProto3_OneofBytes)(nil),
		(*TestAllTypesProto3_OneofBool)(nil),
		(*TestAllTypesProto3_OneofUint64)(nil),
		(*TestAllTypesProto3_OneofFloat)(nil),
		(*TestAllTypesProto3_OneofDouble)(nil),
		(*TestAllTypesProto3_OneofEnum)(nil),
		(*TestAllTypesProto3_OneofNullValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_editions_golden_test_messages_proto3_editions_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_editions_golden_test_messages_proto3_editions_proto_goTypes,
		DependencyIndexes: file_editions_golden_test_messages_proto3_editions_proto_depIdxs,
		EnumInfos:         file_editions_golden_test_messages_proto3_editions_proto_enumTypes,
		MessageInfos:      file_editions_golden_test_messages_proto3_editions_proto_msgTypes,
	}.Build()
	File_editions_golden_test_messages_proto3_editions_proto = out.File
	file_editions_golden_test_messages_proto3_editions_proto_rawDesc = nil
	file_editions_golden_test_messages_proto3_editions_proto_goTypes = nil
	file_editions_golden_test_messages_proto3_editions_proto_depIdxs = nil
}
