package model

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	basemodel "git.eykj.cn/base/core/model"
	"git.eykj.cn/base/core/pkg/logger"
)

// User 用户模型 - 支持分表
type User struct {
	basemodel.BaseModel // 继承基础模型

	UserID               string         `json:"user_id" gorm:"type:char(50);default:'';comment:用户架构唯一标识"`
	UnionID              string         `json:"union_id" gorm:"type:char(50);default:'';comment:用户唯一标识unionid"`
	Username             string         `json:"username" gorm:"type:varchar(255);default:'';comment:用户名"`
	Name                 string         `json:"name" gorm:"type:varchar(255);default:'';comment:姓名"`
	Avatar               string         `json:"avatar" gorm:"type:varchar(255);default:'';comment:头像"`
	Password             string         `json:"-" gorm:"type:varchar(100);not null;comment:密码(第三方同步用户无需密码)"`
	Nickname             string         `json:"nickname" gorm:"type:varchar(50);default:'';comment:昵称"`
	Status               *int64         `json:"status" gorm:"type:bigint;default:1;comment:状态"`
	StateCode            string         `json:"state_code" gorm:"type:varchar(255);default:'';comment:国际电话区号"`
	ManagerUserID        string         `json:"manager_user_id" gorm:"-"`
	Mobile               *int64         `json:"mobile" gorm:"type:bigint;comment:手机号"`
	HideMobile           int8           `json:"hide_mobile" gorm:"type:tinyint;default:0;comment:是否号码隐藏 0 否 1 隐藏"`
	Telephone            string         `json:"telephone" gorm:"type:varchar(255);default:'';comment:分机号"`
	JobNumber            string         `json:"job_number" gorm:"type:varchar(255);default:'';comment:员工工号"`
	Title                string         `json:"title" gorm:"type:varchar(255);default:'';comment:职位"`
	Email                string         `json:"email" gorm:"type:varchar(255);default:'';comment:员工邮箱"`
	OrgEmail             string         `json:"org_email" gorm:"type:varchar(255);default:'';comment:员工的企业邮箱"`
	WorkPlace            string         `json:"work_place" gorm:"type:varchar(255);default:'';comment:办公地点"`
	Remark               string         `json:"remark" gorm:"type:varchar(255);default:'';comment:备注信息"`
	DepartmentName       string         `json:"department_name" gorm:"type:varchar(255);default:'';comment:部门名称"`
	DeptIDList           sql.NullString `json:"dept_id_list" gorm:"type:json;comment:所属部门ID列表"`
	DeptOrderList        sql.NullString `json:"dept_order_list" gorm:"type:json;comment:员工在对应的部门中的排序"`
	Extension            sql.NullString `json:"extension" gorm:"type:json;comment:扩展属性"`
	HiredDate            *int64         `json:"hired_date" gorm:"comment:入职时间，Unix时间戳"`
	Active               int8           `json:"active" gorm:"type:tinyint;default:0;comment:是否已激活 0 否 1 是"`
	Admin                int8           `json:"admin" gorm:"type:tinyint;default:0;comment:是否为管理员 0 否 1 是"`
	Boss                 int8           `json:"boss" gorm:"type:tinyint;default:0;comment:是否为企业的老板 0 否 1 是"`
	Leader               int8           `json:"leader" gorm:"type:tinyint;default:0;comment:是否为主管 0 否 1 是"`
	ExclusiveAccount     int8           `json:"exclusive_account" gorm:"type:tinyint;default:0;comment:是否专属帐号 0 否 1 是"`
	LoginID              string         `json:"login_id" gorm:"type:varchar(255);default:'';comment:专属帐号登录名"`
	ExclusiveAccountType string         `json:"exclusive_account_type" gorm:"type:char(10);default:'';comment:专属帐号类型sso：企业自建专属帐号dingtalk：钉钉自建专属帐号"`
	LeaderInDept         sql.NullString `json:"leader_in_dept" gorm:"type:json;comment:员工在对应的部门中是否领导"`
	RoleList             sql.NullString `json:"role_list" gorm:"type:json;comment:角色信息"`
	UnionEmpExt          sql.NullString `json:"union_emp_ext" gorm:"type:json;comment:当用户来自于关联组织时的关联信息"`
	IsLeave              int8           `json:"is_leave" gorm:"type:tinyint;default:0;comment:是否离职 0 否 1 是"`
	IsLeaveTime          *time.Time     `json:"is_leave_time" gorm:"type:datetime(3);comment:离职时间"`
	DeptID               *int           `json:"dept_id" gorm:"type:bigint;comment:主部门id"`
	IDCard               string         `json:"id_card" gorm:"type:char(20);default:'';comment:身份证号"`
	Area                 sql.NullString `json:"area" gorm:"type:json;comment:地区信息"`
	Type                 int8           `json:"type" gorm:"type:tinyint;not null;default:0;comment:部门类型 0-内部 1-外部"`
}

// TableName 实现 Tabler 接口（关键！）
// 根据企业ID动态生成表名
func (u User) TableName() string {
	// 如果模型中有企业ID，使用分表逻辑
	return basemodel.GetShardingTableName("user", u.CorpID)
}

// UserDynamicModel 用户动态模型（兼容旧代码）
// 使用小写字段名以兼容现有代码
type UserDynamicModel struct {
	basemodel.BaseModel // 继承基础模型

	Userid               string         `json:"user_id" gorm:"column:user_id;type:char(50);default:'';comment:用户架构唯一标识"`
	Unionid              string         `json:"union_id" gorm:"column:union_id;type:char(50);default:'';comment:用户唯一标识unionid"`
	Username             string         `json:"username" gorm:"type:varchar(255);default:'';comment:用户名"`
	Name                 string         `json:"name" gorm:"type:varchar(255);default:'';comment:姓名"`
	Avatar               string         `json:"avatar" gorm:"type:varchar(255);default:'';comment:头像"`
	Password             string         `json:"-" gorm:"type:varchar(100);not null;comment:密码(第三方同步用户无需密码)"`
	Nickname             string         `json:"nickname" gorm:"type:varchar(50);default:'';comment:昵称"`
	Status               *int64         `json:"status" gorm:"type:bigint;default:1;comment:状态"`
	StateCode            string         `json:"state_code" gorm:"type:varchar(255);default:'';comment:国际电话区号"`
	ManagerUserID        string         `json:"manager_user_id" gorm:"-"`
	Mobile               *int64         `json:"mobile" gorm:"type:bigint;comment:手机号"`
	HideMobile           int8           `json:"hide_mobile" gorm:"type:tinyint;default:0;comment:是否号码隐藏 0 否 1 隐藏"`
	JobNumber            string         `json:"job_number" gorm:"type:varchar(255);default:'';comment:工号"`
	Title                string         `json:"title" gorm:"type:varchar(255);default:'';comment:职位"`
	Email                string         `json:"email" gorm:"type:varchar(255);default:'';comment:员工邮箱"`
	OrgEmail             string         `json:"org_email" gorm:"type:varchar(255);default:'';comment:员工的企业邮箱"`
	WorkPlace            string         `json:"work_place" gorm:"type:varchar(255);default:'';comment:办公地点"`
	Remark               string         `json:"remark" gorm:"type:varchar(255);default:'';comment:备注信息"`
	DepartmentName       string         `json:"department_name" gorm:"type:varchar(255);default:'';comment:部门名称"`
	DeptIdList           sql.NullString `json:"dept_id_list" gorm:"column:dept_id_list;type:json;comment:所属部门ID列表"`
	DeptOrderList        sql.NullString `json:"dept_order_list" gorm:"column:dept_order_list;type:json;comment:员工在对应的部门中的排序"`
	Active               int8           `json:"active" gorm:"type:tinyint;default:1;comment:是否激活 0 否 1 是"`
	Admin                int8           `json:"admin" gorm:"type:tinyint;default:0;comment:是否管理员 0 否 1 是"`
	Boss                 int8           `json:"boss" gorm:"type:tinyint;default:0;comment:是否老板 0 否 1 是"`
	Leader               int8           `json:"leader" gorm:"type:tinyint;default:0;comment:是否领导 0 否 1 是"`
	ExclusiveAccount     int8           `json:"exclusive_account" gorm:"type:tinyint;default:0;comment:是否专属帐号 0 否 1 是"`
	HiredDate            *int64         `json:"hired_date" gorm:"type:bigint;comment:入职时间，Unix时间戳"`
	Telephone            string         `json:"telephone" gorm:"type:varchar(255);default:'';comment:分机号"`
	LoginID              string         `json:"login_id" gorm:"type:varchar(255);default:'';comment:专属帐号登录名"`
	ExclusiveAccountType string         `json:"exclusive_account_type" gorm:"type:char(10);default:'';comment:专属帐号类型sso：企业自建专属帐号dingtalk：钉钉自建专属帐号"`
	LeaderInDept         sql.NullString `json:"leader_in_dept" gorm:"type:json;comment:员工在对应的部门中是否领导"`
	RoleList             sql.NullString `json:"role_list" gorm:"type:json;comment:角色信息"`
	UnionEmpExt          sql.NullString `json:"union_emp_ext" gorm:"type:json;comment:当用户来自于关联组织时的关联信息"`
	Isleave              int8           `json:"is_leave" gorm:"column:is_leave;type:tinyint;default:0;comment:是否离职 0 否 1 是"`
	IsleaveTime          *time.Time     `json:"is_leave_time" gorm:"column:is_leave_time;type:datetime(3);comment:离职时间"`
	DeptID               *int64         `json:"dept_id" gorm:"type:bigint;comment:主部门id"`  // 🔧 修改为*int64匹配数据库bigint类型
	IDCard               string         `json:"id_card" gorm:"type:char(20);default:'';comment:身份证号"`
	Area                 sql.NullString `json:"area" gorm:"type:json;comment:地区信息"`
	Extension            sql.NullString `json:"extension" gorm:"type:json;comment:扩展信息"`
	Type                 int8           `json:"type" gorm:"type:tinyint;not null;default:0;comment:部门类型 0-内部 1-外部"`
}

// TableName 实现 Tabler 接口（关键！）
// 根据企业ID动态生成表名
func (u UserDynamicModel) TableName() string {
	// 如果模型中有企业ID，使用分表逻辑
	return basemodel.GetShardingTableName("user", u.CorpID)
}

// 注意：基础的 CRUD 操作现在通过 BaseModel 的泛型函数提供
// GetListModel[User], GetInfoModel[User], AddModel[User], ModifyModel[User], DeleteModel[User]
// 分表功能通过 User.TableName() 方法自动实现
// 用户表迁移功能已移至 db.go 文件中统一管理
// 只保留有特殊业务逻辑的函数

// GetUserByUseridAndCorpid 根据用户ID和企业ID获取用户（兼容旧代码）
func GetUserByUseridAndCorpid(userid, corpid string) (*UserDynamicModel, error) {
	if userid == "" || corpid == "" {
		return nil, nil
	}

	// 创建带有企业ID的UserDynamicModel实例，TableName()方法会自动计算分表
	var user UserDynamicModel
	user.CorpID = corpid

	// 获取正确的表名
	tableName := user.TableName()
	fmt.Printf("🔍 [DEBUG] GetUserByUseridAndCorpid 使用表名: %s\n", tableName)

	// 直接指定表名进行查询
	err := basemodel.DB.Table(tableName).Where("user_id = ? AND corp_id = ?", userid, corpid).First(&user).Error
	if err != nil {
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, err
	}

	return &user, nil
}

// CreateUser 创建用户（兼容旧代码）
func CreateUser(user *UserDynamicModel) error {
	// 获取正确的表名
	tableName := user.TableName()
	fmt.Printf("🔍 [DEBUG] CreateUser 使用表名: %s\n", tableName)

	// 直接指定表名进行创建
	return basemodel.DB.Table(tableName).Create(user).Error
}

// UpdateUser 更新用户（兼容旧代码）
func UpdateUser(user *UserDynamicModel) error {
	return basemodel.DB.Save(user).Error
}

// UpdateUserByCorpIDAndUserID 根据企业ID和用户ID部分更新用户信息
func UpdateUserByCorpIDAndUserID(corpID, userID string, updates map[string]interface{}) (int64, error) {
	if corpID == "" || userID == "" {
		return 0, errors.New("企业ID和用户ID不能为空")
	}
	// 使用基础模型的标准方法进行部分更新
	queryFields := map[string]interface{}{
		"corp_id": corpID,
		"user_id": userID,
	}

	rowsAffected, err := basemodel.ModifyModel[UserDynamicModel](queryFields, updates)

	logger.Info("📊 执行SQL更新",
		logger.String("corp_id", corpID),
		logger.String("user_id", userID),
		logger.String("rows_affected", fmt.Sprintf("%d", rowsAffected)),
		logger.String("error", fmt.Sprintf("%v", err)))

	return rowsAffected, err
}

// GetUserByIDAndCorpid 根据ID和企业ID获取用户（兼容旧代码）
func GetUserByIDAndCorpid(id uint64, corpid string) (*UserDynamicModel, error) {
	if id == 0 || corpid == "" {
		return nil, nil
	}

	// 创建带有企业ID的UserDynamicModel实例，TableName()方法会自动计算分表
	var user UserDynamicModel
	user.CorpID = corpid

	// 使用GORM查询，会自动使用UserDynamicModel.TableName()返回的分表
	err := basemodel.DB.Where("id = ? AND corp_id = ?", id, corpid).First(&user).Error
	if err != nil {
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, err
	}

	return &user, nil
}

// GetUserByID 根据ID获取用户（兼容旧代码）
func GetUserByID(id uint) (*User, error) {
	var user User
	err := basemodel.DB.First(&user, id).Error
	if err != nil {
		if err.Error() == "record not found" {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

