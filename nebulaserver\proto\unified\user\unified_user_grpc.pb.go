// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.33.0
// source: unified_user.proto

package user

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UnifiedUserService_GetUsers_FullMethodName = "/unified.user.UnifiedUserService/GetUsers"
)

// UnifiedUserServiceClient is the client API for UnifiedUserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// UnifiedUserService 统一用户管理服务
// 其他业务系统通过此服务访问用户信息，本服务负责授权验证和路由到对应平台
type UnifiedUserServiceClient interface {
	// GetUsers 统一用户查询接口（整合三种查询场景）
	GetUsers(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersResponse, error)
}

type unifiedUserServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUnifiedUserServiceClient(cc grpc.ClientConnInterface) UnifiedUserServiceClient {
	return &unifiedUserServiceClient{cc}
}

func (c *unifiedUserServiceClient) GetUsers(ctx context.Context, in *GetUsersRequest, opts ...grpc.CallOption) (*GetUsersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUsersResponse)
	err := c.cc.Invoke(ctx, UnifiedUserService_GetUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UnifiedUserServiceServer is the server API for UnifiedUserService service.
// All implementations must embed UnimplementedUnifiedUserServiceServer
// for forward compatibility.
//
// UnifiedUserService 统一用户管理服务
// 其他业务系统通过此服务访问用户信息，本服务负责授权验证和路由到对应平台
type UnifiedUserServiceServer interface {
	// GetUsers 统一用户查询接口（整合三种查询场景）
	GetUsers(context.Context, *GetUsersRequest) (*GetUsersResponse, error)
	mustEmbedUnimplementedUnifiedUserServiceServer()
}

// UnimplementedUnifiedUserServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUnifiedUserServiceServer struct{}

func (UnimplementedUnifiedUserServiceServer) GetUsers(context.Context, *GetUsersRequest) (*GetUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsers not implemented")
}
func (UnimplementedUnifiedUserServiceServer) mustEmbedUnimplementedUnifiedUserServiceServer() {}
func (UnimplementedUnifiedUserServiceServer) testEmbeddedByValue()                            {}

// UnsafeUnifiedUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UnifiedUserServiceServer will
// result in compilation errors.
type UnsafeUnifiedUserServiceServer interface {
	mustEmbedUnimplementedUnifiedUserServiceServer()
}

func RegisterUnifiedUserServiceServer(s grpc.ServiceRegistrar, srv UnifiedUserServiceServer) {
	// If the following call pancis, it indicates UnimplementedUnifiedUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UnifiedUserService_ServiceDesc, srv)
}

func _UnifiedUserService_GetUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UnifiedUserServiceServer).GetUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UnifiedUserService_GetUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UnifiedUserServiceServer).GetUsers(ctx, req.(*GetUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UnifiedUserService_ServiceDesc is the grpc.ServiceDesc for UnifiedUserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UnifiedUserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "unified.user.UnifiedUserService",
	HandlerType: (*UnifiedUserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUsers",
			Handler:    _UnifiedUserService_GetUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "unified_user.proto",
}
