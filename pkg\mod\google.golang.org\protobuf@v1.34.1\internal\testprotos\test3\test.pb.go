// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/test3/test.proto

package test3

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type ForeignEnum int32

const (
	ForeignEnum_FOREIGN_ZERO ForeignEnum = 0
	ForeignEnum_FOREIGN_FOO  ForeignEnum = 4
	ForeignEnum_FOREIGN_BAR  ForeignEnum = 5
	ForeignEnum_FOREIGN_BAZ  ForeignEnum = 6
)

// Enum value maps for ForeignEnum.
var (
	ForeignEnum_name = map[int32]string{
		0: "FOREIGN_ZERO",
		4: "FOREIGN_FOO",
		5: "FOREIGN_BAR",
		6: "FOREIGN_BAZ",
	}
	ForeignEnum_value = map[string]int32{
		"FOREIGN_ZERO": 0,
		"FOREIGN_FOO":  4,
		"FOREIGN_BAR":  5,
		"FOREIGN_BAZ":  6,
	}
)

func (x ForeignEnum) Enum() *ForeignEnum {
	p := new(ForeignEnum)
	*p = x
	return p
}

func (x ForeignEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test3_test_proto_enumTypes[0].Descriptor()
}

func (ForeignEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test3_test_proto_enumTypes[0]
}

func (x ForeignEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForeignEnum.Descriptor instead.
func (ForeignEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test3_test_proto_rawDescGZIP(), []int{0}
}

type TestAllTypes_NestedEnum int32

const (
	TestAllTypes_FOO TestAllTypes_NestedEnum = 0
	TestAllTypes_BAR TestAllTypes_NestedEnum = 1
	TestAllTypes_BAZ TestAllTypes_NestedEnum = 2
	TestAllTypes_NEG TestAllTypes_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypes_NestedEnum.
var (
	TestAllTypes_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypes_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypes_NestedEnum) Enum() *TestAllTypes_NestedEnum {
	p := new(TestAllTypes_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypes_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypes_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_testprotos_test3_test_proto_enumTypes[1].Descriptor()
}

func (TestAllTypes_NestedEnum) Type() protoreflect.EnumType {
	return &file_internal_testprotos_test3_test_proto_enumTypes[1]
}

func (x TestAllTypes_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypes_NestedEnum.Descriptor instead.
func (TestAllTypes_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_internal_testprotos_test3_test_proto_rawDescGZIP(), []int{0, 0}
}

type TestAllTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SingularInt32          int32                                  `protobuf:"varint,81,opt,name=singular_int32,json=singularInt32,proto3" json:"singular_int32,omitempty"`
	SingularInt64          int64                                  `protobuf:"varint,82,opt,name=singular_int64,json=singularInt64,proto3" json:"singular_int64,omitempty"`
	SingularUint32         uint32                                 `protobuf:"varint,83,opt,name=singular_uint32,json=singularUint32,proto3" json:"singular_uint32,omitempty"`
	SingularUint64         uint64                                 `protobuf:"varint,84,opt,name=singular_uint64,json=singularUint64,proto3" json:"singular_uint64,omitempty"`
	SingularSint32         int32                                  `protobuf:"zigzag32,85,opt,name=singular_sint32,json=singularSint32,proto3" json:"singular_sint32,omitempty"`
	SingularSint64         int64                                  `protobuf:"zigzag64,86,opt,name=singular_sint64,json=singularSint64,proto3" json:"singular_sint64,omitempty"`
	SingularFixed32        uint32                                 `protobuf:"fixed32,87,opt,name=singular_fixed32,json=singularFixed32,proto3" json:"singular_fixed32,omitempty"`
	SingularFixed64        uint64                                 `protobuf:"fixed64,88,opt,name=singular_fixed64,json=singularFixed64,proto3" json:"singular_fixed64,omitempty"`
	SingularSfixed32       int32                                  `protobuf:"fixed32,89,opt,name=singular_sfixed32,json=singularSfixed32,proto3" json:"singular_sfixed32,omitempty"`
	SingularSfixed64       int64                                  `protobuf:"fixed64,90,opt,name=singular_sfixed64,json=singularSfixed64,proto3" json:"singular_sfixed64,omitempty"`
	SingularFloat          float32                                `protobuf:"fixed32,91,opt,name=singular_float,json=singularFloat,proto3" json:"singular_float,omitempty"`
	SingularDouble         float64                                `protobuf:"fixed64,92,opt,name=singular_double,json=singularDouble,proto3" json:"singular_double,omitempty"`
	SingularBool           bool                                   `protobuf:"varint,93,opt,name=singular_bool,json=singularBool,proto3" json:"singular_bool,omitempty"`
	SingularString         string                                 `protobuf:"bytes,94,opt,name=singular_string,json=singularString,proto3" json:"singular_string,omitempty"`
	SingularBytes          []byte                                 `protobuf:"bytes,95,opt,name=singular_bytes,json=singularBytes,proto3" json:"singular_bytes,omitempty"`
	SingularNestedMessage  *TestAllTypes_NestedMessage            `protobuf:"bytes,98,opt,name=singular_nested_message,json=singularNestedMessage,proto3" json:"singular_nested_message,omitempty"`
	SingularForeignMessage *ForeignMessage                        `protobuf:"bytes,99,opt,name=singular_foreign_message,json=singularForeignMessage,proto3" json:"singular_foreign_message,omitempty"`
	SingularImportMessage  *ImportMessage                         `protobuf:"bytes,100,opt,name=singular_import_message,json=singularImportMessage,proto3" json:"singular_import_message,omitempty"`
	SingularNestedEnum     TestAllTypes_NestedEnum                `protobuf:"varint,101,opt,name=singular_nested_enum,json=singularNestedEnum,proto3,enum=goproto.proto.test3.TestAllTypes_NestedEnum" json:"singular_nested_enum,omitempty"`
	SingularForeignEnum    ForeignEnum                            `protobuf:"varint,102,opt,name=singular_foreign_enum,json=singularForeignEnum,proto3,enum=goproto.proto.test3.ForeignEnum" json:"singular_foreign_enum,omitempty"`
	SingularImportEnum     ImportEnum                             `protobuf:"varint,103,opt,name=singular_import_enum,json=singularImportEnum,proto3,enum=goproto.proto.test3.ImportEnum" json:"singular_import_enum,omitempty"`
	OptionalInt32          *int32                                 `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32,proto3,oneof" json:"optional_int32,omitempty"`
	OptionalInt64          *int64                                 `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64,proto3,oneof" json:"optional_int64,omitempty"`
	OptionalUint32         *uint32                                `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32,proto3,oneof" json:"optional_uint32,omitempty"`
	OptionalUint64         *uint64                                `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64,proto3,oneof" json:"optional_uint64,omitempty"`
	OptionalSint32         *int32                                 `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32,proto3,oneof" json:"optional_sint32,omitempty"`
	OptionalSint64         *int64                                 `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64,proto3,oneof" json:"optional_sint64,omitempty"`
	OptionalFixed32        *uint32                                `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32,proto3,oneof" json:"optional_fixed32,omitempty"`
	OptionalFixed64        *uint64                                `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64,proto3,oneof" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       *int32                                 `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32,proto3,oneof" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       *int64                                 `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64,proto3,oneof" json:"optional_sfixed64,omitempty"`
	OptionalFloat          *float32                               `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat,proto3,oneof" json:"optional_float,omitempty"`
	OptionalDouble         *float64                               `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble,proto3,oneof" json:"optional_double,omitempty"`
	OptionalBool           *bool                                  `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool,proto3,oneof" json:"optional_bool,omitempty"`
	OptionalString         *string                                `protobuf:"bytes,14,opt,name=optional_string,json=optionalString,proto3,oneof" json:"optional_string,omitempty"`
	OptionalBytes          []byte                                 `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes,proto3,oneof" json:"optional_bytes,omitempty"`
	OptionalNestedMessage  *TestAllTypes_NestedMessage            `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage,proto3,oneof" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessage                        `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage,proto3,oneof" json:"optional_foreign_message,omitempty"`
	OptionalImportMessage  *ImportMessage                         `protobuf:"bytes,20,opt,name=optional_import_message,json=optionalImportMessage,proto3,oneof" json:"optional_import_message,omitempty"`
	OptionalNestedEnum     *TestAllTypes_NestedEnum               `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,proto3,enum=goproto.proto.test3.TestAllTypes_NestedEnum,oneof" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    *ForeignEnum                           `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,proto3,enum=goproto.proto.test3.ForeignEnum,oneof" json:"optional_foreign_enum,omitempty"`
	OptionalImportEnum     *ImportEnum                            `protobuf:"varint,23,opt,name=optional_import_enum,json=optionalImportEnum,proto3,enum=goproto.proto.test3.ImportEnum,oneof" json:"optional_import_enum,omitempty"`
	RepeatedInt32          []int32                                `protobuf:"varint,31,rep,packed,name=repeated_int32,json=repeatedInt32,proto3" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                                `protobuf:"varint,32,rep,packed,name=repeated_int64,json=repeatedInt64,proto3" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                               `protobuf:"varint,33,rep,packed,name=repeated_uint32,json=repeatedUint32,proto3" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                               `protobuf:"varint,34,rep,packed,name=repeated_uint64,json=repeatedUint64,proto3" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                                `protobuf:"zigzag32,35,rep,packed,name=repeated_sint32,json=repeatedSint32,proto3" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                                `protobuf:"zigzag64,36,rep,packed,name=repeated_sint64,json=repeatedSint64,proto3" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                               `protobuf:"fixed32,37,rep,packed,name=repeated_fixed32,json=repeatedFixed32,proto3" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                               `protobuf:"fixed64,38,rep,packed,name=repeated_fixed64,json=repeatedFixed64,proto3" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                                `protobuf:"fixed32,39,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32,proto3" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                                `protobuf:"fixed64,40,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64,proto3" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                              `protobuf:"fixed32,41,rep,packed,name=repeated_float,json=repeatedFloat,proto3" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                              `protobuf:"fixed64,42,rep,packed,name=repeated_double,json=repeatedDouble,proto3" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                                 `protobuf:"varint,43,rep,packed,name=repeated_bool,json=repeatedBool,proto3" json:"repeated_bool,omitempty"`
	RepeatedString         []string                               `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString,proto3" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                               `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes,proto3" json:"repeated_bytes,omitempty"`
	RepeatedNestedMessage  []*TestAllTypes_NestedMessage          `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage,proto3" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessage                      `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage,proto3" json:"repeated_foreign_message,omitempty"`
	RepeatedImportmessage  []*ImportMessage                       `protobuf:"bytes,50,rep,name=repeated_importmessage,json=repeatedImportmessage,proto3" json:"repeated_importmessage,omitempty"`
	RepeatedNestedEnum     []TestAllTypes_NestedEnum              `protobuf:"varint,51,rep,packed,name=repeated_nested_enum,json=repeatedNestedEnum,proto3,enum=goproto.proto.test3.TestAllTypes_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnum                          `protobuf:"varint,52,rep,packed,name=repeated_foreign_enum,json=repeatedForeignEnum,proto3,enum=goproto.proto.test3.ForeignEnum" json:"repeated_foreign_enum,omitempty"`
	RepeatedImportenum     []ImportEnum                           `protobuf:"varint,53,rep,packed,name=repeated_importenum,json=repeatedImportenum,proto3,enum=goproto.proto.test3.ImportEnum" json:"repeated_importenum,omitempty"`
	MapInt32Int32          map[int32]int32                        `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32,proto3" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapInt64Int64          map[int64]int64                        `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64,proto3" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint32Uint32        map[uint32]uint32                      `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32,proto3" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint64Uint64        map[uint64]uint64                      `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64,proto3" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapSint32Sint32        map[int32]int32                        `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32,proto3" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key,proto3" protobuf_val:"zigzag32,2,opt,name=value,proto3"`
	MapSint64Sint64        map[int64]int64                        `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64,proto3" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key,proto3" protobuf_val:"zigzag64,2,opt,name=value,proto3"`
	MapFixed32Fixed32      map[uint32]uint32                      `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32,proto3" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapFixed64Fixed64      map[uint64]uint64                      `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64,proto3" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapSfixed32Sfixed32    map[int32]int32                        `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32,proto3" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapSfixed64Sfixed64    map[int64]int64                        `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64,proto3" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapInt32Float          map[int32]float32                      `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float,proto3" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapInt32Double         map[int32]float64                      `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double,proto3" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolBool            map[bool]bool                          `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool,proto3" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapStringString        map[string]string                      `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString,proto3" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapStringBytes         map[string][]byte                      `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes,proto3" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapStringNestedMessage map[string]*TestAllTypes_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage,proto3" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapStringNestedEnum    map[string]TestAllTypes_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum,proto3" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=goproto.proto.test3.TestAllTypes_NestedEnum"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypes_OneofUint32
	//	*TestAllTypes_OneofNestedMessage
	//	*TestAllTypes_OneofString
	//	*TestAllTypes_OneofBytes
	//	*TestAllTypes_OneofBool
	//	*TestAllTypes_OneofUint64
	//	*TestAllTypes_OneofFloat
	//	*TestAllTypes_OneofDouble
	//	*TestAllTypes_OneofEnum
	OneofField isTestAllTypes_OneofField `protobuf_oneof:"oneof_field"`
}

func (x *TestAllTypes) Reset() {
	*x = TestAllTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test3_test_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes) ProtoMessage() {}

func (x *TestAllTypes) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test3_test_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes.ProtoReflect.Descriptor instead.
func (*TestAllTypes) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test3_test_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypes) GetSingularInt32() int32 {
	if x != nil {
		return x.SingularInt32
	}
	return 0
}

func (x *TestAllTypes) GetSingularInt64() int64 {
	if x != nil {
		return x.SingularInt64
	}
	return 0
}

func (x *TestAllTypes) GetSingularUint32() uint32 {
	if x != nil {
		return x.SingularUint32
	}
	return 0
}

func (x *TestAllTypes) GetSingularUint64() uint64 {
	if x != nil {
		return x.SingularUint64
	}
	return 0
}

func (x *TestAllTypes) GetSingularSint32() int32 {
	if x != nil {
		return x.SingularSint32
	}
	return 0
}

func (x *TestAllTypes) GetSingularSint64() int64 {
	if x != nil {
		return x.SingularSint64
	}
	return 0
}

func (x *TestAllTypes) GetSingularFixed32() uint32 {
	if x != nil {
		return x.SingularFixed32
	}
	return 0
}

func (x *TestAllTypes) GetSingularFixed64() uint64 {
	if x != nil {
		return x.SingularFixed64
	}
	return 0
}

func (x *TestAllTypes) GetSingularSfixed32() int32 {
	if x != nil {
		return x.SingularSfixed32
	}
	return 0
}

func (x *TestAllTypes) GetSingularSfixed64() int64 {
	if x != nil {
		return x.SingularSfixed64
	}
	return 0
}

func (x *TestAllTypes) GetSingularFloat() float32 {
	if x != nil {
		return x.SingularFloat
	}
	return 0
}

func (x *TestAllTypes) GetSingularDouble() float64 {
	if x != nil {
		return x.SingularDouble
	}
	return 0
}

func (x *TestAllTypes) GetSingularBool() bool {
	if x != nil {
		return x.SingularBool
	}
	return false
}

func (x *TestAllTypes) GetSingularString() string {
	if x != nil {
		return x.SingularString
	}
	return ""
}

func (x *TestAllTypes) GetSingularBytes() []byte {
	if x != nil {
		return x.SingularBytes
	}
	return nil
}

func (x *TestAllTypes) GetSingularNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.SingularNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetSingularForeignMessage() *ForeignMessage {
	if x != nil {
		return x.SingularForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetSingularImportMessage() *ImportMessage {
	if x != nil {
		return x.SingularImportMessage
	}
	return nil
}

func (x *TestAllTypes) GetSingularNestedEnum() TestAllTypes_NestedEnum {
	if x != nil {
		return x.SingularNestedEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetSingularForeignEnum() ForeignEnum {
	if x != nil {
		return x.SingularForeignEnum
	}
	return ForeignEnum_FOREIGN_ZERO
}

func (x *TestAllTypes) GetSingularImportEnum() ImportEnum {
	if x != nil {
		return x.SingularImportEnum
	}
	return ImportEnum_IMPORT_ZERO
}

func (x *TestAllTypes) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypes) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypes) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypes) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypes) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *TestAllTypes) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *TestAllTypes) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedMessage() *TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalForeignMessage() *ForeignMessage {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalImportMessage() *ImportMessage {
	if x != nil {
		return x.OptionalImportMessage
	}
	return nil
}

func (x *TestAllTypes) GetOptionalNestedEnum() TestAllTypes_NestedEnum {
	if x != nil && x.OptionalNestedEnum != nil {
		return *x.OptionalNestedEnum
	}
	return TestAllTypes_FOO
}

func (x *TestAllTypes) GetOptionalForeignEnum() ForeignEnum {
	if x != nil && x.OptionalForeignEnum != nil {
		return *x.OptionalForeignEnum
	}
	return ForeignEnum_FOREIGN_ZERO
}

func (x *TestAllTypes) GetOptionalImportEnum() ImportEnum {
	if x != nil && x.OptionalImportEnum != nil {
		return *x.OptionalImportEnum
	}
	return ImportEnum_IMPORT_ZERO
}

func (x *TestAllTypes) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedMessage() []*TestAllTypes_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignMessage() []*ForeignMessage {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedImportmessage() []*ImportMessage {
	if x != nil {
		return x.RepeatedImportmessage
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedNestedEnum() []TestAllTypes_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedForeignEnum() []ForeignEnum {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypes) GetRepeatedImportenum() []ImportEnum {
	if x != nil {
		return x.RepeatedImportenum
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypes) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypes) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypes) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypes) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypes) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypes) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypes) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypes) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypes) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypes) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedMessage() map[string]*TestAllTypes_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetMapStringNestedEnum() map[string]TestAllTypes_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (m *TestAllTypes) GetOneofField() isTestAllTypes_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypes) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypes) GetOneofNestedMessage() *TestAllTypes_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypes) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypes) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypes) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypes) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypes) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypes) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypes) GetOneofEnum() TestAllTypes_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypes_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypes_FOO
}

type isTestAllTypes_OneofField interface {
	isTestAllTypes_OneofField()
}

type TestAllTypes_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,proto3,oneof"`
}

type TestAllTypes_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypes_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,proto3,oneof"`
}

type TestAllTypes_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,proto3,oneof"`
}

type TestAllTypes_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,proto3,oneof"`
}

type TestAllTypes_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,proto3,oneof"`
}

type TestAllTypes_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,proto3,oneof"`
}

type TestAllTypes_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,proto3,oneof"`
}

type TestAllTypes_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,proto3,oneof"`
}

type TestAllTypes_OneofEnum struct {
	OneofEnum TestAllTypes_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,proto3,enum=goproto.proto.test3.TestAllTypes_NestedEnum,oneof"`
}

func (*TestAllTypes_OneofUint32) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofNestedMessage) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofString) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBytes) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofBool) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofUint64) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofFloat) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofDouble) isTestAllTypes_OneofField() {}

func (*TestAllTypes_OneofEnum) isTestAllTypes_OneofField() {}

type ForeignMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C int32 `protobuf:"varint,1,opt,name=c,proto3" json:"c,omitempty"`
	D int32 `protobuf:"varint,2,opt,name=d,proto3" json:"d,omitempty"`
}

func (x *ForeignMessage) Reset() {
	*x = ForeignMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test3_test_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessage) ProtoMessage() {}

func (x *ForeignMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test3_test_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessage.ProtoReflect.Descriptor instead.
func (*ForeignMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test3_test_proto_rawDescGZIP(), []int{1}
}

func (x *ForeignMessage) GetC() int32 {
	if x != nil {
		return x.C
	}
	return 0
}

func (x *ForeignMessage) GetD() int32 {
	if x != nil {
		return x.D
	}
	return 0
}

type TestAllTypes_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           int32         `protobuf:"varint,1,opt,name=a,proto3" json:"a,omitempty"`
	Corecursive *TestAllTypes `protobuf:"bytes,2,opt,name=corecursive,proto3" json:"corecursive,omitempty"`
}

func (x *TestAllTypes_NestedMessage) Reset() {
	*x = TestAllTypes_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_test3_test_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypes_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypes_NestedMessage) ProtoMessage() {}

func (x *TestAllTypes_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_test3_test_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypes_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypes_NestedMessage) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_test3_test_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypes_NestedMessage) GetA() int32 {
	if x != nil {
		return x.A
	}
	return 0
}

func (x *TestAllTypes_NestedMessage) GetCorecursive() *TestAllTypes {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

var File_internal_testprotos_test3_test_proto protoreflect.FileDescriptor

var file_internal_testprotos_test3_test_proto_rawDesc = []byte{
	0x0a, 0x24, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x1a, 0x2b, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73,
	0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x90, 0x3c, 0x0a, 0x0c, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x51, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x52, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x53, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x54, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x55, 0x20, 0x01,
	0x28, 0x11, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x56, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x57, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c,
	0x61, 0x72, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x58, 0x20, 0x01, 0x28, 0x06,
	0x52, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x59, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x73, 0x69,
	0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b,
	0x0a, 0x11, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x5a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x5b, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x5d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x42, 0x6f, 0x6f, 0x6c,
	0x12, 0x27, 0x0a, 0x0f, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x5e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x5f, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x67, 0x0a, 0x17, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x62, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x15, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x18, 0x73, 0x69, 0x6e,
	0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x63, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x16, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5a, 0x0a, 0x17, 0x73, 0x69, 0x6e, 0x67,
	0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x73,
	0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x5e, 0x0a, 0x14, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x65, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x12, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x54, 0x0a, 0x15, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72,
	0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x66, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x73, 0x69, 0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x51, 0x0a, 0x14, 0x73, 0x69,
	0x6e, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x67, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x49,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x73, 0x69, 0x6e, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x0a,
	0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x03,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x04, 0x52, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x88, 0x01,
	0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x48, 0x05, 0x52, 0x0e, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x88, 0x01, 0x01, 0x12,
	0x2c, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x48, 0x06, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a,
	0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07, 0x48, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a,
	0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x48, 0x08, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a,
	0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x48, 0x09, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x88, 0x01, 0x01, 0x12,
	0x30, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x48, 0x0a, 0x52, 0x10, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x88, 0x01,
	0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x48, 0x0b, 0x52, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x48, 0x0c, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x0d, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f,
	0x6f, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0e,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x0f, 0x52, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12,
	0x6c, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x48, 0x10, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x62, 0x0a,
	0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x48, 0x11, 0x52, 0x16, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x5f, 0x0a, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x12, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x63, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x13,
	0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x14, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x88,
	0x01, 0x01, 0x12, 0x56, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75,
	0x6d, 0x48, 0x15, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x1f, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x21, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x23, 0x20,
	0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x24, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x26, 0x20, 0x03, 0x28,
	0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x27, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x29,
	0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x2a, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x2b, 0x20,
	0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f,
	0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x2d, 0x20, 0x03,
	0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x67, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x30, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x18, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x31, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x16, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x32, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x5e, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x33, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x54, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x34, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x50, 0x0a, 0x13, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x6e, 0x75,
	0x6d, 0x18, 0x35, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x5c, 0x0a, 0x0f,
	0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x5c, 0x0a, 0x0f, 0x6d, 0x61,
	0x70, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x39, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x62, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x62, 0x0a, 0x11,
	0x6d, 0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x3b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x62, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x62, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x68, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x3e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x11, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x68, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x6e, 0x0a, 0x15,
	0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x40, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x6e, 0x0a, 0x15,
	0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x41, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x5c, 0x0a, 0x0f,
	0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18,
	0x42, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x5f, 0x0a, 0x10, 0x6d, 0x61,
	0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x43,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x56, 0x0a, 0x0d, 0x6d,
	0x61, 0x70, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x44, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f,
	0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42,
	0x6f, 0x6f, 0x6c, 0x12, 0x62, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x45, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x5f, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x46, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x78, 0x0a, 0x19, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x47, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x6d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x6f, 0x0a, 0x16, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x49, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13,
	0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x63, 0x0a, 0x14, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a,
	0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x72, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0x73, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x74, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02,
	0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23,
	0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x18, 0x77, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e,
	0x75, 0x6d, 0x1a, 0x62, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x61, 0x12, 0x43, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61,
	0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42,
	0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61,
	0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x07, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x07, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46,
	0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d,
	0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x7a, 0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x45, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x74,
	0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x33, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42,
	0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x42,
	0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x42, 0x14,
	0x0a, 0x12, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x22, 0x2c, 0x0a, 0x0e, 0x46,
	0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a,
	0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x63, 0x12, 0x0c, 0x0a, 0x01, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x64, 0x2a, 0x52, 0x0a, 0x0b, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4f, 0x52, 0x45,
	0x49, 0x47, 0x4e, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f,
	0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x46,
	0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b,
	0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x06, 0x42, 0x36, 0x5a,
	0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f,
	0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_testprotos_test3_test_proto_rawDescOnce sync.Once
	file_internal_testprotos_test3_test_proto_rawDescData = file_internal_testprotos_test3_test_proto_rawDesc
)

func file_internal_testprotos_test3_test_proto_rawDescGZIP() []byte {
	file_internal_testprotos_test3_test_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_test3_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_test3_test_proto_rawDescData)
	})
	return file_internal_testprotos_test3_test_proto_rawDescData
}

var file_internal_testprotos_test3_test_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_testprotos_test3_test_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_internal_testprotos_test3_test_proto_goTypes = []interface{}{
	(ForeignEnum)(0),                   // 0: goproto.proto.test3.ForeignEnum
	(TestAllTypes_NestedEnum)(0),       // 1: goproto.proto.test3.TestAllTypes.NestedEnum
	(*TestAllTypes)(nil),               // 2: goproto.proto.test3.TestAllTypes
	(*ForeignMessage)(nil),             // 3: goproto.proto.test3.ForeignMessage
	(*TestAllTypes_NestedMessage)(nil), // 4: goproto.proto.test3.TestAllTypes.NestedMessage
	nil,                                // 5: goproto.proto.test3.TestAllTypes.MapInt32Int32Entry
	nil,                                // 6: goproto.proto.test3.TestAllTypes.MapInt64Int64Entry
	nil,                                // 7: goproto.proto.test3.TestAllTypes.MapUint32Uint32Entry
	nil,                                // 8: goproto.proto.test3.TestAllTypes.MapUint64Uint64Entry
	nil,                                // 9: goproto.proto.test3.TestAllTypes.MapSint32Sint32Entry
	nil,                                // 10: goproto.proto.test3.TestAllTypes.MapSint64Sint64Entry
	nil,                                // 11: goproto.proto.test3.TestAllTypes.MapFixed32Fixed32Entry
	nil,                                // 12: goproto.proto.test3.TestAllTypes.MapFixed64Fixed64Entry
	nil,                                // 13: goproto.proto.test3.TestAllTypes.MapSfixed32Sfixed32Entry
	nil,                                // 14: goproto.proto.test3.TestAllTypes.MapSfixed64Sfixed64Entry
	nil,                                // 15: goproto.proto.test3.TestAllTypes.MapInt32FloatEntry
	nil,                                // 16: goproto.proto.test3.TestAllTypes.MapInt32DoubleEntry
	nil,                                // 17: goproto.proto.test3.TestAllTypes.MapBoolBoolEntry
	nil,                                // 18: goproto.proto.test3.TestAllTypes.MapStringStringEntry
	nil,                                // 19: goproto.proto.test3.TestAllTypes.MapStringBytesEntry
	nil,                                // 20: goproto.proto.test3.TestAllTypes.MapStringNestedMessageEntry
	nil,                                // 21: goproto.proto.test3.TestAllTypes.MapStringNestedEnumEntry
	(*ImportMessage)(nil),              // 22: goproto.proto.test3.ImportMessage
	(ImportEnum)(0),                    // 23: goproto.proto.test3.ImportEnum
}
var file_internal_testprotos_test3_test_proto_depIdxs = []int32{
	4,  // 0: goproto.proto.test3.TestAllTypes.singular_nested_message:type_name -> goproto.proto.test3.TestAllTypes.NestedMessage
	3,  // 1: goproto.proto.test3.TestAllTypes.singular_foreign_message:type_name -> goproto.proto.test3.ForeignMessage
	22, // 2: goproto.proto.test3.TestAllTypes.singular_import_message:type_name -> goproto.proto.test3.ImportMessage
	1,  // 3: goproto.proto.test3.TestAllTypes.singular_nested_enum:type_name -> goproto.proto.test3.TestAllTypes.NestedEnum
	0,  // 4: goproto.proto.test3.TestAllTypes.singular_foreign_enum:type_name -> goproto.proto.test3.ForeignEnum
	23, // 5: goproto.proto.test3.TestAllTypes.singular_import_enum:type_name -> goproto.proto.test3.ImportEnum
	4,  // 6: goproto.proto.test3.TestAllTypes.optional_nested_message:type_name -> goproto.proto.test3.TestAllTypes.NestedMessage
	3,  // 7: goproto.proto.test3.TestAllTypes.optional_foreign_message:type_name -> goproto.proto.test3.ForeignMessage
	22, // 8: goproto.proto.test3.TestAllTypes.optional_import_message:type_name -> goproto.proto.test3.ImportMessage
	1,  // 9: goproto.proto.test3.TestAllTypes.optional_nested_enum:type_name -> goproto.proto.test3.TestAllTypes.NestedEnum
	0,  // 10: goproto.proto.test3.TestAllTypes.optional_foreign_enum:type_name -> goproto.proto.test3.ForeignEnum
	23, // 11: goproto.proto.test3.TestAllTypes.optional_import_enum:type_name -> goproto.proto.test3.ImportEnum
	4,  // 12: goproto.proto.test3.TestAllTypes.repeated_nested_message:type_name -> goproto.proto.test3.TestAllTypes.NestedMessage
	3,  // 13: goproto.proto.test3.TestAllTypes.repeated_foreign_message:type_name -> goproto.proto.test3.ForeignMessage
	22, // 14: goproto.proto.test3.TestAllTypes.repeated_importmessage:type_name -> goproto.proto.test3.ImportMessage
	1,  // 15: goproto.proto.test3.TestAllTypes.repeated_nested_enum:type_name -> goproto.proto.test3.TestAllTypes.NestedEnum
	0,  // 16: goproto.proto.test3.TestAllTypes.repeated_foreign_enum:type_name -> goproto.proto.test3.ForeignEnum
	23, // 17: goproto.proto.test3.TestAllTypes.repeated_importenum:type_name -> goproto.proto.test3.ImportEnum
	5,  // 18: goproto.proto.test3.TestAllTypes.map_int32_int32:type_name -> goproto.proto.test3.TestAllTypes.MapInt32Int32Entry
	6,  // 19: goproto.proto.test3.TestAllTypes.map_int64_int64:type_name -> goproto.proto.test3.TestAllTypes.MapInt64Int64Entry
	7,  // 20: goproto.proto.test3.TestAllTypes.map_uint32_uint32:type_name -> goproto.proto.test3.TestAllTypes.MapUint32Uint32Entry
	8,  // 21: goproto.proto.test3.TestAllTypes.map_uint64_uint64:type_name -> goproto.proto.test3.TestAllTypes.MapUint64Uint64Entry
	9,  // 22: goproto.proto.test3.TestAllTypes.map_sint32_sint32:type_name -> goproto.proto.test3.TestAllTypes.MapSint32Sint32Entry
	10, // 23: goproto.proto.test3.TestAllTypes.map_sint64_sint64:type_name -> goproto.proto.test3.TestAllTypes.MapSint64Sint64Entry
	11, // 24: goproto.proto.test3.TestAllTypes.map_fixed32_fixed32:type_name -> goproto.proto.test3.TestAllTypes.MapFixed32Fixed32Entry
	12, // 25: goproto.proto.test3.TestAllTypes.map_fixed64_fixed64:type_name -> goproto.proto.test3.TestAllTypes.MapFixed64Fixed64Entry
	13, // 26: goproto.proto.test3.TestAllTypes.map_sfixed32_sfixed32:type_name -> goproto.proto.test3.TestAllTypes.MapSfixed32Sfixed32Entry
	14, // 27: goproto.proto.test3.TestAllTypes.map_sfixed64_sfixed64:type_name -> goproto.proto.test3.TestAllTypes.MapSfixed64Sfixed64Entry
	15, // 28: goproto.proto.test3.TestAllTypes.map_int32_float:type_name -> goproto.proto.test3.TestAllTypes.MapInt32FloatEntry
	16, // 29: goproto.proto.test3.TestAllTypes.map_int32_double:type_name -> goproto.proto.test3.TestAllTypes.MapInt32DoubleEntry
	17, // 30: goproto.proto.test3.TestAllTypes.map_bool_bool:type_name -> goproto.proto.test3.TestAllTypes.MapBoolBoolEntry
	18, // 31: goproto.proto.test3.TestAllTypes.map_string_string:type_name -> goproto.proto.test3.TestAllTypes.MapStringStringEntry
	19, // 32: goproto.proto.test3.TestAllTypes.map_string_bytes:type_name -> goproto.proto.test3.TestAllTypes.MapStringBytesEntry
	20, // 33: goproto.proto.test3.TestAllTypes.map_string_nested_message:type_name -> goproto.proto.test3.TestAllTypes.MapStringNestedMessageEntry
	21, // 34: goproto.proto.test3.TestAllTypes.map_string_nested_enum:type_name -> goproto.proto.test3.TestAllTypes.MapStringNestedEnumEntry
	4,  // 35: goproto.proto.test3.TestAllTypes.oneof_nested_message:type_name -> goproto.proto.test3.TestAllTypes.NestedMessage
	1,  // 36: goproto.proto.test3.TestAllTypes.oneof_enum:type_name -> goproto.proto.test3.TestAllTypes.NestedEnum
	2,  // 37: goproto.proto.test3.TestAllTypes.NestedMessage.corecursive:type_name -> goproto.proto.test3.TestAllTypes
	4,  // 38: goproto.proto.test3.TestAllTypes.MapStringNestedMessageEntry.value:type_name -> goproto.proto.test3.TestAllTypes.NestedMessage
	1,  // 39: goproto.proto.test3.TestAllTypes.MapStringNestedEnumEntry.value:type_name -> goproto.proto.test3.TestAllTypes.NestedEnum
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_internal_testprotos_test3_test_proto_init() }
func file_internal_testprotos_test3_test_proto_init() {
	if File_internal_testprotos_test3_test_proto != nil {
		return
	}
	file_internal_testprotos_test3_test_import_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_test3_test_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test3_test_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_testprotos_test3_test_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypes_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_testprotos_test3_test_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypes_OneofUint32)(nil),
		(*TestAllTypes_OneofNestedMessage)(nil),
		(*TestAllTypes_OneofString)(nil),
		(*TestAllTypes_OneofBytes)(nil),
		(*TestAllTypes_OneofBool)(nil),
		(*TestAllTypes_OneofUint64)(nil),
		(*TestAllTypes_OneofFloat)(nil),
		(*TestAllTypes_OneofDouble)(nil),
		(*TestAllTypes_OneofEnum)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_test3_test_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_test3_test_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_test3_test_proto_depIdxs,
		EnumInfos:         file_internal_testprotos_test3_test_proto_enumTypes,
		MessageInfos:      file_internal_testprotos_test3_test_proto_msgTypes,
	}.Build()
	File_internal_testprotos_test3_test_proto = out.File
	file_internal_testprotos_test3_test_proto_rawDesc = nil
	file_internal_testprotos_test3_test_proto_goTypes = nil
	file_internal_testprotos_test3_test_proto_depIdxs = nil
}
