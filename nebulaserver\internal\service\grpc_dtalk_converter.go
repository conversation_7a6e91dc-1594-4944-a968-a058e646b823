package service

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"nebulaserver/internal/model"
	"strconv"

	unified_dept "nebulaserver/proto/unified/department"
	unified_user "nebulaserver/proto/unified/user"

	basemodel "git.eykj.cn/base/core/model"
	dtalk_common "git.eykj.cn/base/grpc/dtalk/common"
	dtalk_dept "git.eykj.cn/base/grpc/dtalk/department"
	dtalk_user "git.eykj.cn/base/grpc/dtalk/user"
)

// ========== 部门转换函数 ==========

// convertDepartmentInfoToUnified 从grpc/dtalk的DepartmentDetailInfo转换到unified的DepartmentDetail
func convertDepartmentInfoToUnified(dtalkInfo *dtalk_dept.DepartmentDetailInfo) *unified_dept.DepartmentDetail {
	if dtalkInfo == nil || dtalkInfo.BasicInfo == nil {
		return nil
	}

	basicInfo := dtalkInfo.BasicInfo
	return &unified_dept.DepartmentDetail{
		Id:              uint64(basicInfo.DeptId), // 数据库自增ID，这里使用0
		CorpId:          "",                       // 需要从外部传入
		DeptId:          fmt.Sprintf("%d", basicInfo.DeptId),
		Name:            basicInfo.DeptName,
		ParentId:        fmt.Sprintf("%d", basicInfo.ParentId),
		Order:           basicInfo.Order,
		Status:          convertDepartmentStatus(basicInfo.Status),
		CreateDeptGroup: false, // dtalk proto中没有此字段
		AutoAddUser:     false, // dtalk proto中没有此字段
		MemberCount:     0,     // 需要单独查询
		Brief:           basicInfo.Description,
		CreatedAt:       basicInfo.CreateTime,
		UpdatedAt:       basicInfo.UpdateTime,
	}
}

// convertDepartmentListItemToUnified 从DepartmentListItem转换到unified的DepartmentDetail
func convertDepartmentListItemToUnified(item *dtalk_dept.DepartmentListItem, corpId string) *unified_dept.DepartmentDetail {
	if item == nil || item.DeptInfo == nil {
		return nil
	}

	deptInfo := item.DeptInfo
	return &unified_dept.DepartmentDetail{
		Id:              0, // 数据库自增ID
		CorpId:          corpId,
		DeptId:          fmt.Sprintf("%d", deptInfo.DeptId),
		Name:            deptInfo.DeptName,
		ParentId:        fmt.Sprintf("%d", deptInfo.ParentId),
		Order:           deptInfo.Order,
		Status:          convertDepartmentStatus(deptInfo.Status),
		CreateDeptGroup: false,
		AutoAddUser:     false,
		MemberCount:     item.UserCount,
		Brief:           deptInfo.Description,
		CreatedAt:       deptInfo.CreateTime,
		UpdatedAt:       deptInfo.UpdateTime,
	}
}

// convertDepartmentStatus 转换部门状态
func convertDepartmentStatus(status dtalk_common.DepartmentStatus) int32 {
	switch status {
	case dtalk_common.DepartmentStatus_NORMAL:
		return 1 // 正常
	case dtalk_common.DepartmentStatus_DISABLED_DEPT:
		return 2 // 禁用
	default:
		return 0 // 未知
	}
}

// ========== 用户转换函数 ==========

// convertUserInfoToUnified 从grpc/dtalk的UserFullInfo转换到unified的UserDetail
func convertUserInfoToUnified(dtalkUser *dtalk_user.UserFullInfo, corpId string) *unified_user.UserDetail {
	if dtalkUser == nil || dtalkUser.BasicInfo == nil {
		return nil
	}

	basicInfo := dtalkUser.BasicInfo

	// 转换部门ID列表
	var deptIdList []string
	var deptId int64 // 🔧 使用dept_id字段名
	if basicInfo.DeptList != nil {
		for _, dept := range basicInfo.DeptList {
			deptIdList = append(deptIdList, fmt.Sprintf("%d", dept.DeptId))
		}
		// 🔧 设置主部门ID（取第一个部门作为主部门）
		if len(basicInfo.DeptList) > 0 {
			deptId = basicInfo.DeptList[0].DeptId
		}
	}

	return &unified_user.UserDetail{
		Id:             0, // 需要从数据库获取
		CorpId:         corpId,
		UserId:         basicInfo.UserId,
		UnionId:        basicInfo.UnionId,
		Username:       basicInfo.UserId, // dtalk没有username字段,使用user_id
		Name:           basicInfo.Name,
		Avatar:         basicInfo.Avatar,
		Mobile:         basicInfo.Mobile,
		Telephone:      "", // dtalk BasicInfo中没有此字段
		JobNumber:      basicInfo.JobNumber,
		Title:          basicInfo.Title,
		Email:          basicInfo.Email,
		OrgEmail:       "", // dtalk中没有此字段
		WorkPlace:      basicInfo.WorkPlace,
		Remark:         basicInfo.Remark,
		DepartmentName: "", // 需要单独查询
		DeptIdList:     deptIdList,
		DeptId:         deptId, // 🔧 设置dept_id字段
		HiredDate:      basicInfo.HiredDate,
		Active:         convertUserStatus(basicInfo.Status),
		Admin:          convertBoolToInt32(basicInfo.IsAdmin),
		Boss:           convertBoolToInt32(basicInfo.IsBoss),
		Leader:         convertBoolToInt32(basicInfo.IsLeader),
		IsLeave:        0, // 需要从扩展信息中获取
		IsLeaveTime:    0,
		CreatedAt:      basicInfo.CreateTime,
		UpdatedAt:      basicInfo.UpdateTime,
	}
}

// convertDepartmentUserInfoToUnified 从DepartmentUserInfo转换到unified的UserDetail
func convertDepartmentUserInfoToUnified(dtalkUser *dtalk_dept.DepartmentUserInfo, corpId string) *unified_user.UserDetail {
	if dtalkUser == nil || dtalkUser.UserInfo == nil {
		return nil
	}

	userInfo := dtalkUser.UserInfo

	// 转换部门ID列表
	var deptIdList []string
	var deptId int64 // 🔧 使用dept_id字段名
	if userInfo.DeptList != nil {
		for _, dept := range userInfo.DeptList {
			deptIdList = append(deptIdList, fmt.Sprintf("%d", dept.DeptId))
		}
		// 🔧 设置主部门ID（取第一个部门作为主部门）
		if len(userInfo.DeptList) > 0 {
			deptId = userInfo.DeptList[0].DeptId
		}
	}

	return &unified_user.UserDetail{
		Id:             0,
		CorpId:         corpId,
		UserId:         userInfo.UserId,
		UnionId:        userInfo.UnionId,
		Username:       userInfo.UserId,
		Name:           userInfo.Name,
		Avatar:         userInfo.Avatar,
		Mobile:         userInfo.Mobile,
		Telephone:      "",
		JobNumber:      userInfo.JobNumber,
		Title:          userInfo.Title,
		Email:          userInfo.Email,
		OrgEmail:       "",
		WorkPlace:      userInfo.WorkPlace,
		Remark:         userInfo.Remark,
		DepartmentName: "",
		DeptIdList:     deptIdList,
		DeptId:         deptId, // 🔧 设置dept_id字段
		HiredDate:      userInfo.HiredDate,
		Active:         convertUserStatus(userInfo.Status),
		Admin:          convertBoolToInt32(userInfo.IsAdmin),
		Boss:           convertBoolToInt32(userInfo.IsBoss),
		Leader:         convertBoolToInt32(dtalkUser.IsDeptLeader),
		IsLeave:        0,
		IsLeaveTime:    0,
		CreatedAt:      userInfo.CreateTime,
		UpdatedAt:      userInfo.UpdateTime,
	}
}

// convertUserStatus 转换用户状态
func convertUserStatus(status dtalk_common.UserStatus) int32 {
	switch status {
	case dtalk_common.UserStatus_ACTIVE:
		return 1 // 激活
	case dtalk_common.UserStatus_INACTIVE:
		return 0 // 未激活
	case dtalk_common.UserStatus_DISABLED:
		return 2 // 禁用
	default:
		return 0 // 未知
	}
}

// convertBoolToInt32 转换bool到int32
func convertBoolToInt32(b bool) int32 {
	if b {
		return 1
	}
	return 0
}

// ========== 数据库模型转换函数 ==========

// convertDBUserToUnified 将数据库用户模型转换为统一用户详情
func convertDBUserToUnified(user *model.UserDynamicModel) *unified_user.UserDetail {
	if user == nil {
		return nil
	}

	// 解析部门ID列表
	var deptIdList []string
	if user.DeptIdList.Valid {
		var deptIDs []int64
		if err := json.Unmarshal([]byte(user.DeptIdList.String), &deptIDs); err == nil {
			for _, id := range deptIDs {
				deptIdList = append(deptIdList, fmt.Sprintf("%d", id))
			}
		}
	}

	// 处理手机号
	mobile := ""
	if user.Mobile != nil {
		mobile = fmt.Sprintf("%d", *user.Mobile)
	}

	// 处理入职时间
	hiredDate := int64(0)
	if user.HiredDate != nil {
		hiredDate = *user.HiredDate
	}

	// 处理离职时间
	isLeaveTime := int64(0)
	if user.IsleaveTime != nil {
		isLeaveTime = user.IsleaveTime.Unix()
	}

	// 🔧 处理主部门ID
	deptId := int64(0)
	if user.DeptID != nil {
		deptId = *user.DeptID
	}

	return &unified_user.UserDetail{
		Id:             user.ID,
		CorpId:         user.CorpID,
		UserId:         user.Userid,
		UnionId:        user.Unionid,
		Username:       user.Username,
		Name:           user.Name,
		Avatar:         user.Avatar,
		Mobile:         mobile,
		Telephone:      user.Telephone,
		JobNumber:      user.JobNumber,
		Title:          user.Title,
		Email:          user.Email,
		OrgEmail:       user.OrgEmail,
		WorkPlace:      user.WorkPlace,
		Remark:         user.Remark,
		DepartmentName: user.DepartmentName,
		DeptIdList:     deptIdList,
		DeptId:         deptId, // 🔧 设置dept_id字段
		HiredDate:      hiredDate,
		Active:         int32(user.Active),
		Admin:          int32(user.Admin),
		Boss:           int32(user.Boss),
		Leader:         int32(user.Leader),
		IsLeave:        int32(user.Isleave),
		IsLeaveTime:    isLeaveTime,
		CreatedAt:      user.CreatedAt.Unix(),
		UpdatedAt:      user.UpdatedAt.Unix(),
	}
}

// boolToInt8 将布尔值转换为int8 (0/1)
func boolToInt8(b bool) int8 {
	if b {
		return 1
	}
	return 0
}

// convertPlatformUserToDB 将平台用户信息（UserFullInfo）转换为数据库模型
func convertPlatformUserToDB(userInfo *dtalk_user.UserFullInfo, corpID string) *model.UserDynamicModel {
	if userInfo == nil || userInfo.BasicInfo == nil {
		return nil
	}

	basicInfo := userInfo.BasicInfo

	// 处理手机号
	var mobile *int64
	if basicInfo.Mobile != "" {
		mobileInt, err := strconv.ParseInt(basicInfo.Mobile, 10, 64)
		if err == nil {
			mobile = &mobileInt
		}
	}

	// 处理入职时间
	var hiredDate *int64
	if basicInfo.HiredDate > 0 {
		hiredDate = &basicInfo.HiredDate
	}

	// 构建默认状态值
	var status int64 = 1

	return &model.UserDynamicModel{
		BaseModel: basemodel.BaseModel{
			CorpID: corpID,
		},
		Userid:           basicInfo.UserId,
		Unionid:          basicInfo.UnionId,
		Name:             basicInfo.Name,
		Nickname:         "", // dtalk中无此字段,可从扩展信息获取
		Avatar:           basicInfo.Avatar,
		Password:         "",      // 第三方同步用户无需密码
		Status:           &status, // 默认状态为1
		StateCode:        "",      // dtalk BasicInfo中无此字段
		ManagerUserID:    "",      // 需要单独设置
		Mobile:           mobile,
		HideMobile:       0,  // 默认不隐藏
		Telephone:        "", // dtalk BasicInfo中没有此字段
		JobNumber:        basicInfo.JobNumber,
		Title:            basicInfo.Title,
		Email:            basicInfo.Email,
		OrgEmail:         "", // dtalk中没有此字段
		WorkPlace:        basicInfo.WorkPlace,
		Remark:           basicInfo.Remark,
		DepartmentName:   "", // 需要单独设置
		Active:           int8(convertUserStatus(basicInfo.Status)),
		Admin:            boolToInt8(basicInfo.IsAdmin),
		Boss:             boolToInt8(basicInfo.IsBoss),
		Leader:           boolToInt8(basicInfo.IsLeader),
		ExclusiveAccount: 0, // 默认非专属账号
		HiredDate:        hiredDate,
		Type:             0, // 默认为内部用户
	}
}

// convertUserFullInfoToDB 将平台完整用户信息（UserFullInfo）转换为数据库模型
func convertUserFullInfoToDB(fullInfo *dtalk_user.UserFullInfo, corpID string) *model.UserDynamicModel {
	if fullInfo == nil || fullInfo.BasicInfo == nil {
		return nil
	}

	basicInfo := fullInfo.BasicInfo
	extendedInfo := fullInfo.ExtendedInfo

	// 处理手机号
	var mobile *int64
	if basicInfo.Mobile != "" {
		mobileInt, err := strconv.ParseInt(basicInfo.Mobile, 10, 64)
		if err == nil {
			mobile = &mobileInt
		}
	}

	// 处理入职时间
	var hiredDate *int64
	if basicInfo.HiredDate > 0 {
		hiredDate = &basicInfo.HiredDate
	} else if extendedInfo != nil && extendedInfo.HireDate > 0 {
		hiredDate = &extendedInfo.HireDate
	}

	// 构建默认状态值
	var status int64 = 1

	// 处理部门ID列表（JSON格式）
	var deptIdListJSON sql.NullString
	if basicInfo.DeptList != nil && len(basicInfo.DeptList) > 0 {
		var deptIdList []string
		for _, dept := range basicInfo.DeptList {
			deptIdList = append(deptIdList, fmt.Sprintf("%d", dept.DeptId))
		}
		if jsonBytes, err := json.Marshal(deptIdList); err == nil {
			deptIdListJSON = sql.NullString{String: string(jsonBytes), Valid: true}
		}
	}

	// 处理部门排序列表（JSON格式）
	var deptOrderListJSON sql.NullString
	if basicInfo.DeptList != nil && len(basicInfo.DeptList) > 0 {
		var deptOrderList []int32
		for _, dept := range basicInfo.DeptList {
			deptOrderList = append(deptOrderList, dept.Order)
		}
		if jsonBytes, err := json.Marshal(deptOrderList); err == nil {
			deptOrderListJSON = sql.NullString{String: string(jsonBytes), Valid: true}
		}
	}

	// 处理部门领导信息（JSON格式）
	var leaderInDeptJSON sql.NullString
	if fullInfo.Departments != nil && len(fullInfo.Departments) > 0 {
		var leaderInDept []bool
		for _, deptInfo := range fullInfo.Departments {
			leaderInDept = append(leaderInDept, deptInfo.IsLeader)
		}
		if jsonBytes, err := json.Marshal(leaderInDept); err == nil {
			leaderInDeptJSON = sql.NullString{String: string(jsonBytes), Valid: true}
		}
	}

	// 处理角色列表（JSON格式）
	var roleListJSON sql.NullString
	if fullInfo.PermissionInfo != nil && fullInfo.PermissionInfo.Permissions != nil && len(fullInfo.PermissionInfo.Permissions) > 0 {
		if jsonBytes, err := json.Marshal(fullInfo.PermissionInfo.Permissions); err == nil {
			roleListJSON = sql.NullString{String: string(jsonBytes), Valid: true}
		}
	}

	// 处理扩展属性（JSON格式）
	var extensionJSON sql.NullString
	if extendedInfo != nil {
		extension := make(map[string]interface{})
		if extendedInfo.EmployeeId != "" {
			extension["employee_id"] = extendedInfo.EmployeeId
		}
		if extendedInfo.JobNumber != "" {
			extension["job_number"] = extendedInfo.JobNumber
		}
		if extendedInfo.Position != "" {
			extension["position"] = extendedInfo.Position
		}
		if extendedInfo.Birthday != "" {
			extension["birthday"] = extendedInfo.Birthday
		}
		if extendedInfo.IdCard != "" {
			extension["id_card"] = extendedInfo.IdCard
		}
		if extendedInfo.EmergencyContact != "" {
			extension["emergency_contact"] = extendedInfo.EmergencyContact
		}
		if extendedInfo.EmergencyPhone != "" {
			extension["emergency_phone"] = extendedInfo.EmergencyPhone
		}
		if extendedInfo.Address != "" {
			extension["address"] = extendedInfo.Address
		}
		if extendedInfo.CustomFields != nil {
			extension["custom_fields"] = extendedInfo.CustomFields
		}
		if len(extension) > 0 {
			if jsonBytes, err := json.Marshal(extension); err == nil {
				extensionJSON = sql.NullString{String: string(jsonBytes), Valid: true}
			}
		}
	}

	// 获取主部门ID
	var primaryDeptID *int
	if fullInfo.Departments != nil {
		for _, deptInfo := range fullInfo.Departments {
			if deptInfo.IsPrimary && deptInfo.DeptInfo != nil {
				deptIDInt := int(deptInfo.DeptInfo.DeptId)
				primaryDeptID = &deptIDInt
				break
			}
		}
	}

	// 获取StateCode
	stateCode := ""
	if basicInfo.StateCode != "" {
		stateCode = basicInfo.StateCode
	}

	// 获取企业邮箱（如果有）
	orgEmail := ""
	if extendedInfo != nil && extendedInfo.CustomFields != nil {
		if email, ok := extendedInfo.CustomFields["org_email"]; ok {
			orgEmail = email
		}
	}

	// 获取身份证号
	idCard := ""
	if extendedInfo != nil && extendedInfo.IdCard != "" {
		idCard = extendedInfo.IdCard
	}

	return &model.UserDynamicModel{
		BaseModel: basemodel.BaseModel{
			CorpID: corpID,
		},
		Userid:           basicInfo.UserId,
		Unionid:          basicInfo.UnionId,
		Name:             basicInfo.Name,
		Nickname:         "",
		Avatar:           basicInfo.Avatar,
		Password:         "",
		Status:           &status,
		StateCode:        stateCode,
		ManagerUserID:    "", // 需要单独设置
		Mobile:           mobile,
		HideMobile:       0,
		Telephone:        "", // 钉钉API不提供
		JobNumber:        basicInfo.JobNumber,
		Title:            basicInfo.Title,
		Email:            basicInfo.Email,
		OrgEmail:         orgEmail,
		WorkPlace:        basicInfo.WorkPlace,
		Remark:           basicInfo.Remark,
		DepartmentName:   "",                // 需要单独设置
		DeptIdList:       deptIdListJSON,    // JSON格式
		DeptOrderList:    deptOrderListJSON, // JSON格式
		Extension:        extensionJSON,     // JSON格式
		Active:           int8(convertUserStatus(basicInfo.Status)),
		Admin:            boolToInt8(basicInfo.IsAdmin),
		Boss:             boolToInt8(basicInfo.IsBoss),
		Leader:           boolToInt8(basicInfo.IsLeader),
		ExclusiveAccount: 0,                // 钉钉API不提供
		LoginID:          "",               // 钉钉API不提供
		LeaderInDept:     leaderInDeptJSON, // JSON格式
		RoleList:         roleListJSON,     // JSON格式
		HiredDate:        hiredDate,
		DeptID:           primaryDeptID,
		IDCard:           idCard,
		Type:             0, // 默认为内部用户
	}
}

// convertPlatformUserInfoToDB 将平台简单用户信息（DingTalkUserInfo）转换为数据库模型
func convertPlatformUserInfoToDB(userInfo *dtalk_common.DingTalkUserInfo, corpID string) *model.UserDynamicModel {
	if userInfo == nil {
		return nil
	}

	// 处理手机号
	var mobile *int64
	if userInfo.Mobile != "" {
		mobileInt, err := strconv.ParseInt(userInfo.Mobile, 10, 64)
		if err == nil {
			mobile = &mobileInt
		}
	}

	// 处理入职时间
	var hiredDate *int64
	if userInfo.HiredDate > 0 {
		hiredDate = &userInfo.HiredDate
	}

	// 构建默认状态值
	var status int64 = 1

	return &model.UserDynamicModel{
		BaseModel: basemodel.BaseModel{
			CorpID: corpID,
		},
		Userid:           userInfo.UserId,
		Unionid:          userInfo.UnionId,
		Name:             userInfo.Name,
		Nickname:         "",
		Avatar:           userInfo.Avatar,
		Password:         "",
		Status:           &status,
		StateCode:        "",
		ManagerUserID:    "",
		Mobile:           mobile,
		HideMobile:       0,
		Telephone:        "",
		JobNumber:        userInfo.JobNumber,
		Title:            userInfo.Title,
		Email:            userInfo.Email,
		OrgEmail:         "",
		WorkPlace:        userInfo.WorkPlace,
		Remark:           userInfo.Remark,
		DepartmentName:   "",
		Active:           int8(convertUserStatus(userInfo.Status)),
		Admin:            boolToInt8(userInfo.IsAdmin),
		Boss:             boolToInt8(userInfo.IsBoss),
		Leader:           boolToInt8(userInfo.IsLeader),
		ExclusiveAccount: 0,
		HiredDate:        hiredDate,
		Type:             0,
	}
}
