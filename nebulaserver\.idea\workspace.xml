<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="badc6595-cb8e-4816-a74f-a5874e42db8c" name="Changes" comment="feat(unified_user): 添加统一用户查询功能&#10;&#10;- 新增 QueryType 枚举，支持多种用户查询类型&#10;- 添加 GetUsersRequest 和 GetUsersResponse 消息结构- 实现数据库用户模型与统一用户模型之间的转换函数&#10;- 更新 protobuf 文件并重新生成代码&#10;- 调整消息类型索引以适应新增的查询类型">
      <change beforePath="$PROJECT_DIR$/.env" beforeDir="false" afterPath="$PROJECT_DIR$/.env" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/model/user.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/model/user.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/appmarket_event.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/appmarket_event.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/grpc_adapter.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/grpc_adapter.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/grpc_dtalk_converter.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/grpc_dtalk_converter.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/unified_user_service.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/unified_user_service.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/internal/service/user.go" beforeDir="false" afterPath="$PROJECT_DIR$/internal/service/user.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/unified/user/unified_user.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/unified/user/unified_user.pb.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/unified/user/unified_user.proto" beforeDir="false" afterPath="$PROJECT_DIR$/proto/unified/user/unified_user.proto" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/proto/unified/user/unified_user_grpc.pb.go" beforeDir="false" afterPath="$PROJECT_DIR$/proto/unified/user/unified_user_grpc.pb.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../Work/go" />
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="Master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HttpClientOnboardingState">{
  &quot;isOnboardingCommentShown&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="34lcdjayDmusbBnSjA70w9MSDso" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go Build.go build nebulaserver.executor": "Debug",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "Beta",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/goproject/nebulaserver",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="go build nebulaserver" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="nebulaserver" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="nebulaserver" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build nebulaserver" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-f466f9b0953e-146d08934cbf-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-252.27397.100" />
        <option value="bundled-js-predefined-d6986cc7102b-3aa1da707db6-JavaScript-GO-252.27397.100" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="badc6595-cb8e-4816-a74f-a5874e42db8c" name="Changes" comment="" />
      <created>1761791688152</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1761791688152</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat(user): 更新用户模型字段定义&#10;&#10;- 修改 Password 字段长度为 100 并设置为非空&#10;- 新增 Nickname 字段，类型为 varchar(50)- 新增 Status 字段，类型为 bigint&#10;- 设置 Mobile 字段类型为 bigint&#10;- 设置 IsLeaveTime 字段类型为 datetime(3)&#10;- 设置 DeptID 字段类型为 bigint&#10;- 新增 Type 字段用于区分部门类型- 更新 ManagerUserID 字段列名为 manager_userid- 明确 HiredDate 字段注释为 Unix 时间戳&#10;- 调整部分字段默认值与注释说明" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(user): 更新用户模型字段定义&#10;&#10;- 修改 Password 字段长度为 100 并设置为非空&#10;- 新增 Nickname 字段，类型为 varchar(50)- 新增 Status 字段，类型为 bigint&#10;- 设置 Mobile 字段类型为 bigint&#10;- 设置 IsLeaveTime 字段类型为 datetime(3)&#10;- 设置 DeptID 字段类型为 bigint&#10;- 新增 Type 字段用于区分部门类型- 更新 ManagerUserID 字段列名为 manager_userid- 明确 HiredDate 字段注释为 Unix 时间戳&#10;- 调整部分字段默认值与注释说明" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>