package grpc

import (
	"context"
	"fmt"

	"fdd622server/internal/pkg/logger"
	"fdd622server/internal/service/dtalk/api"
	commonpb "fdd622server/proto/dtalk/common"
	userpb "fdd622server/proto/dtalk/user"
)

// UserServiceServer 用户服务实现
type UserServiceServer struct {
	userpb.UnimplementedUserServiceServer
	userAPI *api.UserAPI
	authAPI *api.AuthAPI
}

// NewUserServiceServer 创建用户服务实例
func NewUserServiceServer() (*UserServiceServer, error) {
	userAPI, err := api.NewUserAPI()
	if err != nil {
		return nil, fmt.Errorf("创建用户API失败: %w", err)
	}

	authAPI, err := api.NewAuthAPI()
	if err != nil {
		return nil, fmt.Errorf("创建认证API失败: %w", err)
	}

	return &UserServiceServer{
		userAPI: userAPI,
		authAPI: authAPI,
	}, nil
}

// GetUserInfo 获取用户详细信息
func (s *UserServiceServer) GetUserInfo(ctx context.Context, req *userpb.GetUserInfoRequest) (*userpb.GetUserInfoResponse, error) {
	logger.Info("收到获取用户信息请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId),
		logger.String("mobile", req.Mobile))

	// 检查服务初始化状态
	if s == nil {
		logger.Error("用户服务实例为nil")
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "服务未初始化",
				ErrorCode:    5000,
				ErrorMessage: "UserServiceServer is nil",
			},
		}, nil
	}

	if s.authAPI == nil {
		logger.Error("认证API未初始化")
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "认证服务未初始化",
				ErrorCode:    5001,
				ErrorMessage: "authAPI is nil",
			},
		}, nil
	}

	if s.userAPI == nil {
		logger.Error("用户API未初始化")
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "用户服务未初始化",
				ErrorCode:    5002,
				ErrorMessage: "userAPI is nil",
			},
		}, nil
	}

	// 检查必填参数
	if req.UserId == "" && req.Mobile == "" {
		logger.Error("user_id和mobile不能同时为空")
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "user_id和mobile不能同时为空",
				ErrorCode:    4001,
				ErrorMessage: "user_id and mobile cannot both be empty",
			},
		}, nil
	}

	// 获取access_token
	accessToken, err := s.authAPI.GetCorpAccessToken(req.CorpId, req.AppId)
	if err != nil {
		logger.Error("获取access_token失败", logger.Error2(err))
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "获取访问令牌失败",
				ErrorCode:    1001,
				ErrorMessage: err.Error(),
			},
		}, nil
	}

	// 调用钉钉API获取用户详情
	// 注意:如果传入的是mobile,需要先通过mobile获取user_id
	userID := req.UserId
	if userID == "" && req.Mobile != "" {
		// TODO: 通过手机号获取用户ID的逻辑
		logger.Warn("暂不支持通过手机号查询用户")
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "暂不支持通过手机号查询用户",
				ErrorCode:    4002,
				ErrorMessage: "Query by mobile is not supported yet",
			},
		}, nil
	}

	userInfo, err := s.userAPI.GetUserDetail(accessToken, userID)
	if err != nil {
		logger.Error("调用钉钉API获取用户详情失败", logger.Error2(err))
		return &userpb.GetUserInfoResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "调用钉钉API失败",
				ErrorCode:    1002,
				ErrorMessage: err.Error(),
			},
		}, nil
	}

	// 转换部门信息列表
	deptList := make([]*commonpb.DingTalkDept, 0, len(userInfo.DeptIDList))
	for _, deptID := range userInfo.DeptIDList {
		deptList = append(deptList, &commonpb.DingTalkDept{
			DeptId: int64(deptID),
			// 其他部门字段留空,需要时可以再调用部门接口填充
		})
	}

	// 转换为proto格式
	userFullInfo := &userpb.UserFullInfo{
		BasicInfo: &commonpb.DingTalkUserInfo{
			UserId:    userInfo.UserID,
			UnionId:   "", // UserDetail中没有unionId
			Name:      userInfo.Name,
			Avatar:    userInfo.Avatar,
			Mobile:    userInfo.Mobile,
			Email:     userInfo.Email,
			JobNumber: userInfo.JobNumber,
			Title:     userInfo.Title,
			DeptList:  deptList,
			IsAdmin:   userInfo.IsAdmin,
			IsBoss:    userInfo.IsBoss,
			IsLeader:  userInfo.IsLeader,
		},
	}

	// 如果请求要求包含扩展信息、部门信息或权限信息
	if req.IncludeExtendedInfo {
		// TODO: 添加扩展信息
		logger.Info("请求包含扩展信息,暂未实现")
	}

	if req.IncludeDepartments {
		// TODO: 添加部门详细信息
		logger.Info("请求包含部门信息,暂未实现")
	}

	if req.IncludePermissions {
		// TODO: 添加权限信息
		logger.Info("请求包含权限信息,暂未实现")
	}

	logger.Info("获取用户详情成功",
		logger.String("user_name", userInfo.Name),
		logger.String("user_id", userInfo.UserID))

	return &userpb.GetUserInfoResponse{
		Base: &commonpb.BaseResponse{
			Success:      true,
			Message:      "获取成功",
			ErrorCode:    0,
			ErrorMessage: "",
		},
		UserInfo: userFullInfo,
	}, nil
}

// GetUserList 获取部门用户列表
func (s *UserServiceServer) GetUserList(ctx context.Context, req *userpb.GetUserListRequest) (*userpb.GetUserListResponse, error) {
	logger.Info("收到获取部门用户列表请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.Int64("dept_id", req.DeptId),
		logger.Bool("include_child_depts", req.IncludeChildDepts))

	// 参数验证
	if req.CorpId == "" || req.AppId == "" || req.DeptId == 0 {
		logger.Error("获取部门用户列表请求参数不完整")
		return &userpb.GetUserListResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "请求参数不完整：corp_id、app_id、dept_id都是必需的",
				ErrorCode:    4001,
				ErrorMessage: "Missing required parameters",
			},
		}, nil
	}

	// 获取access_token
	accessToken, err := s.authAPI.GetCorpAccessToken(req.CorpId, req.AppId)
	if err != nil {
		logger.Error("获取access_token失败", logger.Error2(err))
		return &userpb.GetUserListResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "获取访问令牌失败",
				ErrorCode:    1001,
				ErrorMessage: err.Error(),
			},
		}, nil
	}

	// 创建部门API实例
	deptAPI, err := api.NewDepartmentAPI()
	if err != nil {
		logger.Error("创建部门API失败", logger.Error2(err))
		return &userpb.GetUserListResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "内部服务错误",
				ErrorCode:    5000,
				ErrorMessage: err.Error(),
			},
		}, nil
	}
	defer deptAPI.Close()

	// 设置分页参数
	page := int32(1)
	pageSize := int32(100)
	if req.Pagination != nil {
		if req.Pagination.Page > 0 {
			page = req.Pagination.Page
		}
		if req.Pagination.PageSize > 0 && req.Pagination.PageSize <= 100 {
			pageSize = req.Pagination.PageSize
		}
	}

	// 计算cursor（钉钉API使用cursor分页）
	cursor := int64((page - 1) * pageSize)

	logger.Info("调用钉钉API获取部门用户列表",
		logger.Int64("dept_id", req.DeptId),
		logger.Int64("cursor", cursor),
		logger.Int("size", int(pageSize)))

	// 调用钉钉API获取部门用户列表
	deptUsersResp, err := deptAPI.ListDepartmentUsers(accessToken, req.DeptId, cursor, int64(pageSize))
	if err != nil {
		logger.Error("调用钉钉API获取部门用户失败", logger.Error2(err))
		return &userpb.GetUserListResponse{
			Base: &commonpb.BaseResponse{
				Success:      false,
				Message:      "调用钉钉API失败",
				ErrorCode:    1002,
				ErrorMessage: err.Error(),
			},
		}, nil
	}

	// 转换用户数据
	var users []*commonpb.DingTalkUserInfo
	for _, user := range deptUsersResp.Result.List {
		users = append(users, &commonpb.DingTalkUserInfo{
			UserId: user.UserID,
			Name:   user.Name,
			// 简单用户列表只包含UserID和Name
			// 如需完整信息，需要循环调用GetUserInfo
		})
	}

	logger.Info("获取部门用户列表成功",
		logger.Int64("dept_id", req.DeptId),
		logger.Int("user_count", len(users)),
		logger.Bool("has_more", deptUsersResp.Result.HasMore))

	// 构造响应
	return &userpb.GetUserListResponse{
		Base: &commonpb.BaseResponse{
			Success:      true,
			Message:      "获取成功",
			ErrorCode:    0,
			ErrorMessage: "",
		},
		Users:         users,
		DeptId:        req.DeptId,
		HasChildDepts: req.IncludeChildDepts,
		Pagination: &commonpb.PaginationResponse{
			Page:     page,
			PageSize: pageSize,
			Total:    int64(len(users)), // 钉钉API不返回总数，这里只返回当前页数量
			HasNext:  deptUsersResp.Result.HasMore,
		},
	}, nil
}

// SearchUsers 搜索用户
func (s *UserServiceServer) SearchUsers(ctx context.Context, req *userpb.SearchUsersRequest) (*userpb.SearchUsersResponse, error) {
	logger.Info("收到搜索用户请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("keyword", req.Keyword))

	// TODO: 实现搜索用户逻辑
	return &userpb.SearchUsersResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2003,
			ErrorMessage: "SearchUsers功能暂未实现",
		},
	}, nil
}

// BatchGetUserInfo 批量获取用户信息
func (s *UserServiceServer) BatchGetUserInfo(ctx context.Context, req *userpb.BatchGetUserInfoRequest) (*userpb.BatchGetUserInfoResponse, error) {
	logger.Info("收到批量获取用户信息请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.Int("user_ids_count", len(req.UserIds)),
		logger.Int("mobiles_count", len(req.Mobiles)))

	// TODO: 实现批量获取用户信息逻辑
	return &userpb.BatchGetUserInfoResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2004,
			ErrorMessage: "BatchGetUserInfo功能暂未实现",
		},
	}, nil
}

// GetUserDepartments 获取用户所在部门列表
func (s *UserServiceServer) GetUserDepartments(ctx context.Context, req *userpb.GetUserDepartmentsRequest) (*userpb.GetUserDepartmentsResponse, error) {
	logger.Info("收到获取用户部门请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现获取用户部门逻辑
	return &userpb.GetUserDepartmentsResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2005,
			ErrorMessage: "GetUserDepartments功能暂未实现",
		},
	}, nil
}

// GetUserSubordinates 获取用户的直接下属
func (s *UserServiceServer) GetUserSubordinates(ctx context.Context, req *userpb.GetUserSubordinatesRequest) (*userpb.GetUserSubordinatesResponse, error) {
	logger.Info("收到获取用户下属请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现获取用户下属逻辑
	return &userpb.GetUserSubordinatesResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2006,
			ErrorMessage: "GetUserSubordinates功能暂未实现",
		},
	}, nil
}

// GetUserSuperiors 获取用户的上级领导
func (s *UserServiceServer) GetUserSuperiors(ctx context.Context, req *userpb.GetUserSuperiorsRequest) (*userpb.GetUserSuperiorsResponse, error) {
	logger.Info("收到获取用户上级请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现获取用户上级逻辑
	return &userpb.GetUserSuperiorsResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2007,
			ErrorMessage: "GetUserSuperiors功能暂未实现",
		},
	}, nil
}

// GetUserAttendance 获取用户的考勤信息
func (s *UserServiceServer) GetUserAttendance(ctx context.Context, req *userpb.GetUserAttendanceRequest) (*userpb.GetUserAttendanceResponse, error) {
	logger.Info("收到获取用户考勤请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现获取用户考勤逻辑
	return &userpb.GetUserAttendanceResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2008,
			ErrorMessage: "GetUserAttendance功能暂未实现",
		},
	}, nil
}

// CreateUser 创建用户
func (s *UserServiceServer) CreateUser(ctx context.Context, req *userpb.CreateUserRequest) (*userpb.CreateUserResponse, error) {
	logger.Info("收到创建用户请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("name", req.Name),
		logger.String("mobile", req.Mobile))

	// TODO: 实现创建用户逻辑
	return &userpb.CreateUserResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2009,
			ErrorMessage: "CreateUser功能暂未实现",
		},
	}, nil
}

// UpdateUser 更新用户信息
func (s *UserServiceServer) UpdateUser(ctx context.Context, req *userpb.UpdateUserRequest) (*userpb.UpdateUserResponse, error) {
	logger.Info("收到更新用户请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现更新用户逻辑
	return &userpb.UpdateUserResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2010,
			ErrorMessage: "UpdateUser功能暂未实现",
		},
	}, nil
}

// DeleteUser 删除用户
func (s *UserServiceServer) DeleteUser(ctx context.Context, req *userpb.DeleteUserRequest) (*userpb.DeleteUserResponse, error) {
	logger.Info("收到删除用户请求",
		logger.String("corp_id", req.CorpId),
		logger.String("app_id", req.AppId),
		logger.String("user_id", req.UserId))

	// TODO: 实现删除用户逻辑
	return &userpb.DeleteUserResponse{
		Base: &commonpb.BaseResponse{
			Success:      false,
			Message:      "功能暂未实现",
			ErrorCode:    2011,
			ErrorMessage: "DeleteUser功能暂未实现",
		},
	}, nil
}
