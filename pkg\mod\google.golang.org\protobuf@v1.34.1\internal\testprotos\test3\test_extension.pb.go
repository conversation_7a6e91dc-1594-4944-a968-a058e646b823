// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/test3/test_extension.proto

package test3

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
)

var file_internal_testprotos_test3_test_extension_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1001,
		Name:          "goproto.proto.test3.optional_int32_ext",
		Tag:           "varint,1001,opt,name=optional_int32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1002,
		Name:          "goproto.proto.test3.optional_int64_ext",
		Tag:           "varint,1002,opt,name=optional_int64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1003,
		Name:          "goproto.proto.test3.optional_uint32_ext",
		Tag:           "varint,1003,opt,name=optional_uint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1004,
		Name:          "goproto.proto.test3.optional_uint64_ext",
		Tag:           "varint,1004,opt,name=optional_uint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1005,
		Name:          "goproto.proto.test3.optional_sint32_ext",
		Tag:           "zigzag32,1005,opt,name=optional_sint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1006,
		Name:          "goproto.proto.test3.optional_sint64_ext",
		Tag:           "zigzag64,1006,opt,name=optional_sint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1007,
		Name:          "goproto.proto.test3.optional_fixed32_ext",
		Tag:           "fixed32,1007,opt,name=optional_fixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1008,
		Name:          "goproto.proto.test3.optional_fixed64_ext",
		Tag:           "fixed64,1008,opt,name=optional_fixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1009,
		Name:          "goproto.proto.test3.optional_sfixed32_ext",
		Tag:           "fixed32,1009,opt,name=optional_sfixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1010,
		Name:          "goproto.proto.test3.optional_sfixed64_ext",
		Tag:           "fixed64,1010,opt,name=optional_sfixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         1011,
		Name:          "goproto.proto.test3.optional_float_ext",
		Tag:           "fixed32,1011,opt,name=optional_float_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         1012,
		Name:          "goproto.proto.test3.optional_double_ext",
		Tag:           "fixed64,1012,opt,name=optional_double_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1013,
		Name:          "goproto.proto.test3.optional_bool_ext",
		Tag:           "varint,1013,opt,name=optional_bool_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1014,
		Name:          "goproto.proto.test3.optional_string_ext",
		Tag:           "bytes,1014,opt,name=optional_string_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         1015,
		Name:          "goproto.proto.test3.optional_bytes_ext",
		Tag:           "bytes,1015,opt,name=optional_bytes_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignMessage)(nil),
		Field:         1016,
		Name:          "goproto.proto.test3.optional_foreign_message_ext",
		Tag:           "bytes,1016,opt,name=optional_foreign_message_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignEnum)(nil),
		Field:         1017,
		Name:          "goproto.proto.test3.optional_foreign_enum_ext",
		Tag:           "varint,1017,opt,name=optional_foreign_enum_ext,enum=goproto.proto.test3.ForeignEnum",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2001,
		Name:          "goproto.proto.test3.optional_optional_int32_ext",
		Tag:           "varint,2001,opt,name=optional_optional_int32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2002,
		Name:          "goproto.proto.test3.optional_optional_int64_ext",
		Tag:           "varint,2002,opt,name=optional_optional_int64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         2003,
		Name:          "goproto.proto.test3.optional_optional_uint32_ext",
		Tag:           "varint,2003,opt,name=optional_optional_uint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         2004,
		Name:          "goproto.proto.test3.optional_optional_uint64_ext",
		Tag:           "varint,2004,opt,name=optional_optional_uint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2005,
		Name:          "goproto.proto.test3.optional_optional_sint32_ext",
		Tag:           "zigzag32,2005,opt,name=optional_optional_sint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2006,
		Name:          "goproto.proto.test3.optional_optional_sint64_ext",
		Tag:           "zigzag64,2006,opt,name=optional_optional_sint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         2007,
		Name:          "goproto.proto.test3.optional_optional_fixed32_ext",
		Tag:           "fixed32,2007,opt,name=optional_optional_fixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         2008,
		Name:          "goproto.proto.test3.optional_optional_fixed64_ext",
		Tag:           "fixed64,2008,opt,name=optional_optional_fixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         2009,
		Name:          "goproto.proto.test3.optional_optional_sfixed32_ext",
		Tag:           "fixed32,2009,opt,name=optional_optional_sfixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         2010,
		Name:          "goproto.proto.test3.optional_optional_sfixed64_ext",
		Tag:           "fixed64,2010,opt,name=optional_optional_sfixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         2011,
		Name:          "goproto.proto.test3.optional_optional_float_ext",
		Tag:           "fixed32,2011,opt,name=optional_optional_float_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         2012,
		Name:          "goproto.proto.test3.optional_optional_double_ext",
		Tag:           "fixed64,2012,opt,name=optional_optional_double_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         2013,
		Name:          "goproto.proto.test3.optional_optional_bool_ext",
		Tag:           "varint,2013,opt,name=optional_optional_bool_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         2014,
		Name:          "goproto.proto.test3.optional_optional_string_ext",
		Tag:           "bytes,2014,opt,name=optional_optional_string_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         2015,
		Name:          "goproto.proto.test3.optional_optional_bytes_ext",
		Tag:           "bytes,2015,opt,name=optional_optional_bytes_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignMessage)(nil),
		Field:         2016,
		Name:          "goproto.proto.test3.optional_optional_foreign_message_ext",
		Tag:           "bytes,2016,opt,name=optional_optional_foreign_message_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*ForeignEnum)(nil),
		Field:         2017,
		Name:          "goproto.proto.test3.optional_optional_foreign_enum_ext",
		Tag:           "varint,2017,opt,name=optional_optional_foreign_enum_ext,enum=goproto.proto.test3.ForeignEnum",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         3001,
		Name:          "goproto.proto.test3.repeated_int32_ext",
		Tag:           "varint,3001,rep,packed,name=repeated_int32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         3002,
		Name:          "goproto.proto.test3.repeated_int64_ext",
		Tag:           "varint,3002,rep,packed,name=repeated_int64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         3003,
		Name:          "goproto.proto.test3.repeated_uint32_ext",
		Tag:           "varint,3003,rep,packed,name=repeated_uint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         3004,
		Name:          "goproto.proto.test3.repeated_uint64_ext",
		Tag:           "varint,3004,rep,packed,name=repeated_uint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         3005,
		Name:          "goproto.proto.test3.repeated_sint32_ext",
		Tag:           "zigzag32,3005,rep,packed,name=repeated_sint32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         3006,
		Name:          "goproto.proto.test3.repeated_sint64_ext",
		Tag:           "zigzag64,3006,rep,packed,name=repeated_sint64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         3007,
		Name:          "goproto.proto.test3.repeated_fixed32_ext",
		Tag:           "fixed32,3007,rep,packed,name=repeated_fixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         3008,
		Name:          "goproto.proto.test3.repeated_fixed64_ext",
		Tag:           "fixed64,3008,rep,packed,name=repeated_fixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         3009,
		Name:          "goproto.proto.test3.repeated_sfixed32_ext",
		Tag:           "fixed32,3009,rep,packed,name=repeated_sfixed32_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         3010,
		Name:          "goproto.proto.test3.repeated_sfixed64_ext",
		Tag:           "fixed64,3010,rep,packed,name=repeated_sfixed64_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         3011,
		Name:          "goproto.proto.test3.repeated_float_ext",
		Tag:           "fixed32,3011,rep,packed,name=repeated_float_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         3012,
		Name:          "goproto.proto.test3.repeated_double_ext",
		Tag:           "fixed64,3012,rep,packed,name=repeated_double_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         3013,
		Name:          "goproto.proto.test3.repeated_bool_ext",
		Tag:           "varint,3013,rep,packed,name=repeated_bool_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         3014,
		Name:          "goproto.proto.test3.repeated_string_ext",
		Tag:           "bytes,3014,rep,name=repeated_string_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([][]byte)(nil),
		Field:         3015,
		Name:          "goproto.proto.test3.repeated_bytes_ext",
		Tag:           "bytes,3015,rep,name=repeated_bytes_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]*ForeignMessage)(nil),
		Field:         3016,
		Name:          "goproto.proto.test3.repeated_foreign_message_ext",
		Tag:           "bytes,3016,rep,name=repeated_foreign_message_ext",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]ForeignEnum)(nil),
		Field:         3017,
		Name:          "goproto.proto.test3.repeated_foreign_enum_ext",
		Tag:           "varint,3017,rep,packed,name=repeated_foreign_enum_ext,enum=goproto.proto.test3.ForeignEnum",
		Filename:      "internal/testprotos/test3/test_extension.proto",
	},
}

// Extension fields to descriptorpb.MessageOptions.
var (
	// optional int32 optional_int32_ext = 1001;
	E_OptionalInt32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[0]
	// optional int64 optional_int64_ext = 1002;
	E_OptionalInt64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[1]
	// optional uint32 optional_uint32_ext = 1003;
	E_OptionalUint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[2]
	// optional uint64 optional_uint64_ext = 1004;
	E_OptionalUint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[3]
	// optional sint32 optional_sint32_ext = 1005;
	E_OptionalSint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[4]
	// optional sint64 optional_sint64_ext = 1006;
	E_OptionalSint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[5]
	// optional fixed32 optional_fixed32_ext = 1007;
	E_OptionalFixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[6]
	// optional fixed64 optional_fixed64_ext = 1008;
	E_OptionalFixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[7]
	// optional sfixed32 optional_sfixed32_ext = 1009;
	E_OptionalSfixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[8]
	// optional sfixed64 optional_sfixed64_ext = 1010;
	E_OptionalSfixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[9]
	// optional float optional_float_ext = 1011;
	E_OptionalFloatExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[10]
	// optional double optional_double_ext = 1012;
	E_OptionalDoubleExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[11]
	// optional bool optional_bool_ext = 1013;
	E_OptionalBoolExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[12]
	// optional string optional_string_ext = 1014;
	E_OptionalStringExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[13]
	// optional bytes optional_bytes_ext = 1015;
	E_OptionalBytesExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[14]
	// optional goproto.proto.test3.ForeignMessage optional_foreign_message_ext = 1016;
	E_OptionalForeignMessageExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[15]
	// optional goproto.proto.test3.ForeignEnum optional_foreign_enum_ext = 1017;
	E_OptionalForeignEnumExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[16]
	// optional int32 optional_optional_int32_ext = 2001;
	E_OptionalOptionalInt32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[17]
	// optional int64 optional_optional_int64_ext = 2002;
	E_OptionalOptionalInt64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[18]
	// optional uint32 optional_optional_uint32_ext = 2003;
	E_OptionalOptionalUint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[19]
	// optional uint64 optional_optional_uint64_ext = 2004;
	E_OptionalOptionalUint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[20]
	// optional sint32 optional_optional_sint32_ext = 2005;
	E_OptionalOptionalSint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[21]
	// optional sint64 optional_optional_sint64_ext = 2006;
	E_OptionalOptionalSint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[22]
	// optional fixed32 optional_optional_fixed32_ext = 2007;
	E_OptionalOptionalFixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[23]
	// optional fixed64 optional_optional_fixed64_ext = 2008;
	E_OptionalOptionalFixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[24]
	// optional sfixed32 optional_optional_sfixed32_ext = 2009;
	E_OptionalOptionalSfixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[25]
	// optional sfixed64 optional_optional_sfixed64_ext = 2010;
	E_OptionalOptionalSfixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[26]
	// optional float optional_optional_float_ext = 2011;
	E_OptionalOptionalFloatExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[27]
	// optional double optional_optional_double_ext = 2012;
	E_OptionalOptionalDoubleExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[28]
	// optional bool optional_optional_bool_ext = 2013;
	E_OptionalOptionalBoolExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[29]
	// optional string optional_optional_string_ext = 2014;
	E_OptionalOptionalStringExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[30]
	// optional bytes optional_optional_bytes_ext = 2015;
	E_OptionalOptionalBytesExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[31]
	// optional goproto.proto.test3.ForeignMessage optional_optional_foreign_message_ext = 2016;
	E_OptionalOptionalForeignMessageExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[32]
	// optional goproto.proto.test3.ForeignEnum optional_optional_foreign_enum_ext = 2017;
	E_OptionalOptionalForeignEnumExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[33]
	// repeated int32 repeated_int32_ext = 3001;
	E_RepeatedInt32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[34]
	// repeated int64 repeated_int64_ext = 3002;
	E_RepeatedInt64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[35]
	// repeated uint32 repeated_uint32_ext = 3003;
	E_RepeatedUint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[36]
	// repeated uint64 repeated_uint64_ext = 3004;
	E_RepeatedUint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[37]
	// repeated sint32 repeated_sint32_ext = 3005;
	E_RepeatedSint32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[38]
	// repeated sint64 repeated_sint64_ext = 3006;
	E_RepeatedSint64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[39]
	// repeated fixed32 repeated_fixed32_ext = 3007;
	E_RepeatedFixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[40]
	// repeated fixed64 repeated_fixed64_ext = 3008;
	E_RepeatedFixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[41]
	// repeated sfixed32 repeated_sfixed32_ext = 3009;
	E_RepeatedSfixed32Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[42]
	// repeated sfixed64 repeated_sfixed64_ext = 3010;
	E_RepeatedSfixed64Ext = &file_internal_testprotos_test3_test_extension_proto_extTypes[43]
	// repeated float repeated_float_ext = 3011;
	E_RepeatedFloatExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[44]
	// repeated double repeated_double_ext = 3012;
	E_RepeatedDoubleExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[45]
	// repeated bool repeated_bool_ext = 3013;
	E_RepeatedBoolExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[46]
	// repeated string repeated_string_ext = 3014;
	E_RepeatedStringExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[47]
	// repeated bytes repeated_bytes_ext = 3015;
	E_RepeatedBytesExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[48]
	// repeated goproto.proto.test3.ForeignMessage repeated_foreign_message_ext = 3016;
	E_RepeatedForeignMessageExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[49]
	// repeated goproto.proto.test3.ForeignEnum repeated_foreign_enum_ext = 3017;
	E_RepeatedForeignEnumExt = &file_internal_testprotos_test3_test_extension_proto_extTypes[50]
)

var File_internal_testprotos_test3_test_extension_proto protoreflect.FileDescriptor

var file_internal_testprotos_test3_test_extension_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x13, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73,
	0x74, 0x33, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3a, 0x4e, 0x0a,
	0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f,
	0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe9, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74, 0x3a, 0x4e, 0x0a,
	0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f,
	0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a,
	0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74, 0x3a,
	0x50, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xec, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78,
	0x74, 0x3a, 0x50, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xed, 0x07, 0x20, 0x01, 0x28, 0x11,
	0x52, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xee, 0x07, 0x20, 0x01,
	0x28, 0x12, 0x52, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x52, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xef,
	0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x74, 0x3a, 0x52, 0x0a, 0x14, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf0, 0x07, 0x20, 0x01, 0x28, 0x06, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x54, 0x0a,
	0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf1, 0x07, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x13,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x45, 0x78, 0x74, 0x3a, 0x54, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf2, 0x07,
	0x20, 0x01, 0x28, 0x10, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x4e, 0x0a, 0x12, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xf3, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x74,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xf4, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x74, 0x3a, 0x4c, 0x0a, 0x11, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x78, 0x74,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xf5, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x74,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xf6, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x3a, 0x4e, 0x0a, 0x12, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf7, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x3a, 0x86, 0x01, 0x0a, 0x1c,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf8, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x78, 0x74, 0x3a, 0x7d, 0x0a, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf9, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x45, 0x78, 0x74, 0x3a, 0x62, 0x0a, 0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65,
	0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xd1, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x62, 0x0a, 0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd2, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x64, 0x0a, 0x1c, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd3, 0x0f, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74, 0x88, 0x01,
	0x01, 0x3a, 0x64, 0x0a, 0x1c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xd4, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x64, 0x0a, 0x1c, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd5, 0x0f, 0x20, 0x01, 0x28, 0x11, 0x52,
	0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x64, 0x0a,
	0x1c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd6,
	0x0f, 0x20, 0x01, 0x28, 0x12, 0x52, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x74,
	0x88, 0x01, 0x01, 0x3a, 0x66, 0x0a, 0x1d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd7, 0x0f, 0x20, 0x01, 0x28, 0x07, 0x52, 0x1a, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x66, 0x0a, 0x1d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd8, 0x0f,
	0x20, 0x01, 0x28, 0x06, 0x52, 0x1a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x74,
	0x88, 0x01, 0x01, 0x3a, 0x68, 0x0a, 0x1e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd9, 0x0f, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x1b, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x68, 0x0a,
	0x1e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xda, 0x0f, 0x20, 0x01, 0x28, 0x10, 0x52, 0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x62, 0x0a, 0x1b, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdb, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x18,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x64, 0x0a, 0x1c, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdc, 0x0f, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x19, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x74, 0x88, 0x01,
	0x01, 0x3a, 0x60, 0x0a, 0x1a, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x78, 0x74, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xdd, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x74,
	0x88, 0x01, 0x01, 0x3a, 0x64, 0x0a, 0x1c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xde, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x62, 0x0a, 0x1b, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdf, 0x0f, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x9a, 0x01,
	0x0a, 0x25, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe0, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x21, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x91, 0x01, 0x0a, 0x22, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xe1, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e,
	0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x1e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72,
	0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x78, 0x74, 0x88, 0x01, 0x01, 0x3a, 0x4e,
	0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xb9, 0x17, 0x20, 0x03, 0x28, 0x05, 0x52, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74, 0x3a, 0x4e,
	0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xba, 0x17, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x50,
	0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xbb, 0x17, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x11, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x78, 0x74,
	0x3a, 0x50, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xbc, 0x17, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xbd, 0x17, 0x20, 0x03, 0x28,
	0x11, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xbe, 0x17, 0x20,
	0x03, 0x28, 0x12, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x52, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xbf, 0x17, 0x20, 0x03, 0x28, 0x07, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x78, 0x74, 0x3a, 0x52, 0x0a, 0x14, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65,
	0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xc0, 0x17, 0x20, 0x03, 0x28, 0x06, 0x52, 0x12, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x54,
	0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc1, 0x17, 0x20, 0x03, 0x28, 0x0f, 0x52,
	0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x45, 0x78, 0x74, 0x3a, 0x54, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc2,
	0x17, 0x20, 0x03, 0x28, 0x10, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x78, 0x74, 0x3a, 0x4e, 0x0a, 0x12, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x65, 0x78, 0x74,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xc3, 0x17, 0x20, 0x03, 0x28, 0x02, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xc4, 0x17, 0x20, 0x03, 0x28, 0x01, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x78, 0x74, 0x3a, 0x4c, 0x0a, 0x11,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xc5, 0x17, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x74, 0x3a, 0x50, 0x0a, 0x13, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78,
	0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xc6, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x74, 0x3a, 0x4e, 0x0a, 0x12,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x65,
	0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xc7, 0x17, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x78, 0x74, 0x3a, 0x86, 0x01, 0x0a,
	0x1c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc8,
	0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x19, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x45, 0x78, 0x74, 0x3a, 0x7d, 0x0a, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x5f, 0x65,
	0x78, 0x74, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xc9, 0x17, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x33,
	0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x16, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75,
	0x6d, 0x45, 0x78, 0x74, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x33, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var file_internal_testprotos_test3_test_extension_proto_goTypes = []interface{}{
	(*descriptorpb.MessageOptions)(nil), // 0: google.protobuf.MessageOptions
	(*ForeignMessage)(nil),              // 1: goproto.proto.test3.ForeignMessage
	(ForeignEnum)(0),                    // 2: goproto.proto.test3.ForeignEnum
}
var file_internal_testprotos_test3_test_extension_proto_depIdxs = []int32{
	0,  // 0: goproto.proto.test3.optional_int32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 1: goproto.proto.test3.optional_int64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 2: goproto.proto.test3.optional_uint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 3: goproto.proto.test3.optional_uint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 4: goproto.proto.test3.optional_sint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 5: goproto.proto.test3.optional_sint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 6: goproto.proto.test3.optional_fixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 7: goproto.proto.test3.optional_fixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 8: goproto.proto.test3.optional_sfixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 9: goproto.proto.test3.optional_sfixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 10: goproto.proto.test3.optional_float_ext:extendee -> google.protobuf.MessageOptions
	0,  // 11: goproto.proto.test3.optional_double_ext:extendee -> google.protobuf.MessageOptions
	0,  // 12: goproto.proto.test3.optional_bool_ext:extendee -> google.protobuf.MessageOptions
	0,  // 13: goproto.proto.test3.optional_string_ext:extendee -> google.protobuf.MessageOptions
	0,  // 14: goproto.proto.test3.optional_bytes_ext:extendee -> google.protobuf.MessageOptions
	0,  // 15: goproto.proto.test3.optional_foreign_message_ext:extendee -> google.protobuf.MessageOptions
	0,  // 16: goproto.proto.test3.optional_foreign_enum_ext:extendee -> google.protobuf.MessageOptions
	0,  // 17: goproto.proto.test3.optional_optional_int32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 18: goproto.proto.test3.optional_optional_int64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 19: goproto.proto.test3.optional_optional_uint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 20: goproto.proto.test3.optional_optional_uint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 21: goproto.proto.test3.optional_optional_sint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 22: goproto.proto.test3.optional_optional_sint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 23: goproto.proto.test3.optional_optional_fixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 24: goproto.proto.test3.optional_optional_fixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 25: goproto.proto.test3.optional_optional_sfixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 26: goproto.proto.test3.optional_optional_sfixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 27: goproto.proto.test3.optional_optional_float_ext:extendee -> google.protobuf.MessageOptions
	0,  // 28: goproto.proto.test3.optional_optional_double_ext:extendee -> google.protobuf.MessageOptions
	0,  // 29: goproto.proto.test3.optional_optional_bool_ext:extendee -> google.protobuf.MessageOptions
	0,  // 30: goproto.proto.test3.optional_optional_string_ext:extendee -> google.protobuf.MessageOptions
	0,  // 31: goproto.proto.test3.optional_optional_bytes_ext:extendee -> google.protobuf.MessageOptions
	0,  // 32: goproto.proto.test3.optional_optional_foreign_message_ext:extendee -> google.protobuf.MessageOptions
	0,  // 33: goproto.proto.test3.optional_optional_foreign_enum_ext:extendee -> google.protobuf.MessageOptions
	0,  // 34: goproto.proto.test3.repeated_int32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 35: goproto.proto.test3.repeated_int64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 36: goproto.proto.test3.repeated_uint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 37: goproto.proto.test3.repeated_uint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 38: goproto.proto.test3.repeated_sint32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 39: goproto.proto.test3.repeated_sint64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 40: goproto.proto.test3.repeated_fixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 41: goproto.proto.test3.repeated_fixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 42: goproto.proto.test3.repeated_sfixed32_ext:extendee -> google.protobuf.MessageOptions
	0,  // 43: goproto.proto.test3.repeated_sfixed64_ext:extendee -> google.protobuf.MessageOptions
	0,  // 44: goproto.proto.test3.repeated_float_ext:extendee -> google.protobuf.MessageOptions
	0,  // 45: goproto.proto.test3.repeated_double_ext:extendee -> google.protobuf.MessageOptions
	0,  // 46: goproto.proto.test3.repeated_bool_ext:extendee -> google.protobuf.MessageOptions
	0,  // 47: goproto.proto.test3.repeated_string_ext:extendee -> google.protobuf.MessageOptions
	0,  // 48: goproto.proto.test3.repeated_bytes_ext:extendee -> google.protobuf.MessageOptions
	0,  // 49: goproto.proto.test3.repeated_foreign_message_ext:extendee -> google.protobuf.MessageOptions
	0,  // 50: goproto.proto.test3.repeated_foreign_enum_ext:extendee -> google.protobuf.MessageOptions
	1,  // 51: goproto.proto.test3.optional_foreign_message_ext:type_name -> goproto.proto.test3.ForeignMessage
	2,  // 52: goproto.proto.test3.optional_foreign_enum_ext:type_name -> goproto.proto.test3.ForeignEnum
	1,  // 53: goproto.proto.test3.optional_optional_foreign_message_ext:type_name -> goproto.proto.test3.ForeignMessage
	2,  // 54: goproto.proto.test3.optional_optional_foreign_enum_ext:type_name -> goproto.proto.test3.ForeignEnum
	1,  // 55: goproto.proto.test3.repeated_foreign_message_ext:type_name -> goproto.proto.test3.ForeignMessage
	2,  // 56: goproto.proto.test3.repeated_foreign_enum_ext:type_name -> goproto.proto.test3.ForeignEnum
	57, // [57:57] is the sub-list for method output_type
	57, // [57:57] is the sub-list for method input_type
	51, // [51:57] is the sub-list for extension type_name
	0,  // [0:51] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_internal_testprotos_test3_test_extension_proto_init() }
func file_internal_testprotos_test3_test_extension_proto_init() {
	if File_internal_testprotos_test3_test_extension_proto != nil {
		return
	}
	file_internal_testprotos_test3_test_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_test3_test_extension_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 51,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_test3_test_extension_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_test3_test_extension_proto_depIdxs,
		ExtensionInfos:    file_internal_testprotos_test3_test_extension_proto_extTypes,
	}.Build()
	File_internal_testprotos_test3_test_extension_proto = out.File
	file_internal_testprotos_test3_test_extension_proto_rawDesc = nil
	file_internal_testprotos_test3_test_extension_proto_goTypes = nil
	file_internal_testprotos_test3_test_extension_proto_depIdxs = nil
}
