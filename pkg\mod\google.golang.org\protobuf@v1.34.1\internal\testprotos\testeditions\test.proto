// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

edition = "2023";

package goproto.proto.testeditions;

option go_package = "google.golang.org/protobuf/internal/testprotos/testeditions";

message TestAllTypes {
  message NestedMessage {
    int32 a = 1;
    TestAllTypes corecursive = 2;
  }

  enum NestedEnum {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
    NEG = -1;  // Intentionally negative.
  }

  int32 singular_int32 = 124 [features.field_presence = IMPLICIT];
  int64 singular_int64 = 125 [features.field_presence = IMPLICIT];
  uint32 singular_uint32 = 126 [features.field_presence = IMPLICIT];
  uint64 singular_uint64 = 127 [features.field_presence = IMPLICIT];
  sint32 singular_sint32 = 128 [features.field_presence = IMPLICIT];
  sint64 singular_sint64 = 129 [features.field_presence = IMPLICIT];
  fixed32 singular_fixed32 = 130 [features.field_presence = IMPLICIT];
  fixed64 singular_fixed64 = 131 [features.field_presence = IMPLICIT];
  sfixed32 singular_sfixed32 = 132 [features.field_presence = IMPLICIT];
  sfixed64 singular_sfixed64 = 133 [features.field_presence = IMPLICIT];
  float singular_float = 134 [features.field_presence = IMPLICIT];
  double singular_double = 135 [features.field_presence = IMPLICIT];
  bool singular_bool = 136 [features.field_presence = IMPLICIT];
  string singular_string = 137 [features.field_presence = IMPLICIT];
  bytes singular_bytes = 138 [features.field_presence = IMPLICIT];

  int32 optional_int32 = 1;
  int64 optional_int64 = 2;
  uint32 optional_uint32 = 3;
  uint64 optional_uint64 = 4;
  sint32 optional_sint32 = 5;
  sint64 optional_sint64 = 6;
  fixed32 optional_fixed32 = 7;
  fixed64 optional_fixed64 = 8;
  sfixed32 optional_sfixed32 = 9;
  sfixed64 optional_sfixed64 = 10;
  float optional_float = 11;
  double optional_double = 12;
  bool optional_bool = 13;
  string optional_string = 14;
  bytes optional_bytes = 15;
  message OptionalGroup {
    int32 a = 17;
    NestedMessage optional_nested_message = 1000;
    int32 same_field_number = 16;
  }
  OptionalGroup optionalgroup = 16 [features.message_encoding = DELIMITED];
  OptionalGroup not_group_like_delimited = 17
      [features.message_encoding = DELIMITED];
  NestedMessage optional_nested_message = 18;
  ForeignMessage optional_foreign_message = 19;
  NestedEnum optional_nested_enum = 21;
  ForeignEnum optional_foreign_enum = 22;

  repeated int32 repeated_int32 = 31;
  repeated int64 repeated_int64 = 32;
  repeated uint32 repeated_uint32 = 33;
  repeated uint64 repeated_uint64 = 34;
  repeated sint32 repeated_sint32 = 35;
  repeated sint64 repeated_sint64 = 36;
  repeated fixed32 repeated_fixed32 = 37;
  repeated fixed64 repeated_fixed64 = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated float repeated_float = 41;
  repeated double repeated_double = 42;
  repeated bool repeated_bool = 43;
  repeated string repeated_string = 44;
  repeated bytes repeated_bytes = 45;

  message RepeatedGroup {
    int32 a = 47;
    NestedMessage optional_nested_message = 1001;
  }
  repeated RepeatedGroup repeatedgroup = 46 [
    features.message_encoding = DELIMITED,
    features.repeated_field_encoding = EXPANDED
  ];
  repeated NestedMessage repeated_nested_message = 48;
  repeated ForeignMessage repeated_foreign_message = 49;
  repeated NestedEnum repeated_nested_enum = 51;
  repeated ForeignEnum repeated_foreign_enum = 52;

  map<int32, int32> map_int32_int32 = 56;
  map<int64, int64> map_int64_int64 = 57;
  map<uint32, uint32> map_uint32_uint32 = 58;
  map<uint64, uint64> map_uint64_uint64 = 59;
  map<sint32, sint32> map_sint32_sint32 = 60;
  map<sint64, sint64> map_sint64_sint64 = 61;
  map<fixed32, fixed32> map_fixed32_fixed32 = 62;
  map<fixed64, fixed64> map_fixed64_fixed64 = 63;
  map<sfixed32, sfixed32> map_sfixed32_sfixed32 = 64;
  map<sfixed64, sfixed64> map_sfixed64_sfixed64 = 65;
  map<int32, float> map_int32_float = 66;
  map<int32, double> map_int32_double = 67;
  map<bool, bool> map_bool_bool = 68;
  map<string, string> map_string_string = 69;
  map<string, bytes> map_string_bytes = 70;
  map<string, NestedMessage> map_string_nested_message = 71;
  map<string, NestedEnum> map_string_nested_enum = 73;

  // Singular with defaults
  int32 default_int32 = 81 [default = 81];
  int64 default_int64 = 82 [default = 82];
  uint32 default_uint32 = 83 [default = 83];
  uint64 default_uint64 = 84 [default = 84];
  sint32 default_sint32 = 85 [default = -85];
  sint64 default_sint64 = 86 [default = 86];
  fixed32 default_fixed32 = 87 [default = 87];
  fixed64 default_fixed64 = 88 [default = 88];
  sfixed32 default_sfixed32 = 89 [default = 89];
  sfixed64 default_sfixed64 = 80 [default = -90];
  float default_float = 91 [default = 91.5];
  double default_double = 92 [default = 92e3];
  bool default_bool = 93 [default = true];
  string default_string = 94 [default = "hello"];
  bytes default_bytes = 95 [default = "world"];
  NestedEnum default_nested_enum = 96 [default = BAR];
  ForeignEnum default_foreign_enum = 97 [default = FOREIGN_BAR];

  message OneofGroup {
    int32 a = 1;
    int32 b = 2;
  }
  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 113;
    bytes oneof_bytes = 114;
    bool oneof_bool = 115;
    uint64 oneof_uint64 = 116;
    float oneof_float = 117;
    double oneof_double = 118;
    NestedEnum oneof_enum = 119;
    OneofGroup oneofgroup = 121 [features.message_encoding = DELIMITED];
  }

  // A oneof with exactly one field.
  oneof oneof_optional {
    uint32 oneof_optional_uint32 = 120;
  }
}

message ForeignMessage {
  int32 c = 1;
  int32 d = 2;
}

enum ForeignEnum {
  FOREIGN_ZERO = 0;
  FOREIGN_FOO = 4;
  FOREIGN_BAR = 5;
  FOREIGN_BAZ = 6;
}

message TestRequired {
  int32 required_field = 1 [features.field_presence = LEGACY_REQUIRED];
}

message TestRequiredForeign {
  TestRequired optional_message = 1;
  repeated TestRequired repeated_message = 2;
  map<int32, TestRequired> map_message = 3;
  oneof oneof_field {
    TestRequired oneof_message = 4;
  }
}

message TestRequiredGroupFields {
  message OptionalGroup {
    int32 a = 2 [features.field_presence = LEGACY_REQUIRED];
  }
  OptionalGroup optionalgroup = 1 [features.message_encoding = DELIMITED];
  message RepeatedGroup {
    int32 a = 4 [features.field_presence = LEGACY_REQUIRED];
  }
  repeated RepeatedGroup repeatedgroup = 3
      [features.message_encoding = DELIMITED];
}

message TestPackedTypes {
  repeated int32 packed_int32 = 90 [features.repeated_field_encoding = PACKED];
  repeated int64 packed_int64 = 91 [features.repeated_field_encoding = PACKED];
  repeated uint32 packed_uint32 = 92
      [features.repeated_field_encoding = PACKED];
  repeated uint64 packed_uint64 = 93
      [features.repeated_field_encoding = PACKED];
  repeated sint32 packed_sint32 = 94
      [features.repeated_field_encoding = PACKED];
  repeated sint64 packed_sint64 = 95
      [features.repeated_field_encoding = PACKED];
  repeated fixed32 packed_fixed32 = 96
      [features.repeated_field_encoding = PACKED];
  repeated fixed64 packed_fixed64 = 97
      [features.repeated_field_encoding = PACKED];
  repeated sfixed32 packed_sfixed32 = 98
      [features.repeated_field_encoding = PACKED];
  repeated sfixed64 packed_sfixed64 = 99
      [features.repeated_field_encoding = PACKED];
  repeated float packed_float = 100 [features.repeated_field_encoding = PACKED];
  repeated double packed_double = 101
      [features.repeated_field_encoding = PACKED];
  repeated bool packed_bool = 102 [features.repeated_field_encoding = PACKED];
  repeated ForeignEnum packed_enum = 103
      [features.repeated_field_encoding = PACKED];
}

message TestPackedExtensions {
  extensions 1 to max;
}

extend TestPackedExtensions {
  repeated int32 packed_int32 = 90 [features.repeated_field_encoding = PACKED];
  repeated int64 packed_int64 = 91 [features.repeated_field_encoding = PACKED];
  repeated uint32 packed_uint32 = 92
      [features.repeated_field_encoding = PACKED];
  repeated uint64 packed_uint64 = 93
      [features.repeated_field_encoding = PACKED];
  repeated sint32 packed_sint32 = 94
      [features.repeated_field_encoding = PACKED];
  repeated sint64 packed_sint64 = 95
      [features.repeated_field_encoding = PACKED];
  repeated fixed32 packed_fixed32 = 96
      [features.repeated_field_encoding = PACKED];
  repeated fixed64 packed_fixed64 = 97
      [features.repeated_field_encoding = PACKED];
  repeated sfixed32 packed_sfixed32 = 98
      [features.repeated_field_encoding = PACKED];
  repeated sfixed64 packed_sfixed64 = 99
      [features.repeated_field_encoding = PACKED];
  repeated float packed_float = 100 [features.repeated_field_encoding = PACKED];
  repeated double packed_double = 101
      [features.repeated_field_encoding = PACKED];
  repeated bool packed_bool = 102 [features.repeated_field_encoding = PACKED];
  repeated ForeignEnum packed_enum = 103
      [features.repeated_field_encoding = PACKED];
}
