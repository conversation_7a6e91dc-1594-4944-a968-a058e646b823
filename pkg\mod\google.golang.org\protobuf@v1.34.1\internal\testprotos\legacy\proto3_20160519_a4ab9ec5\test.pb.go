// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go.
// source: proto3_20160519_a4ab9ec5/test.proto
// DO NOT EDIT!

/*
Package proto3_20160519_a4ab9ec5 is a generated protocol buffer package.

It is generated from these files:

	proto3_20160519_a4ab9ec5/test.proto

It has these top-level messages:

	SiblingMessage
	Message
*/
package proto3_20160519_a4ab9ec5

import proto "google.golang.org/protobuf/internal/protolegacy"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
const _ = proto.ProtoPackageIsVersion1

type SiblingEnum int32

const (
	SiblingEnum_ALPHA   SiblingEnum = 0
	SiblingEnum_BRAVO   SiblingEnum = 10
	SiblingEnum_CHARLIE SiblingEnum = 200
)

var SiblingEnum_name = map[int32]string{
	0:   "ALPHA",
	10:  "BRAVO",
	200: "CHARLIE",
}
var SiblingEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   10,
	"CHARLIE": 200,
}

func (x SiblingEnum) String() string {
	return proto.EnumName(SiblingEnum_name, int32(x))
}
func (SiblingEnum) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

type Message_ChildEnum int32

const (
	Message_ALPHA   Message_ChildEnum = 0
	Message_BRAVO   Message_ChildEnum = 1
	Message_CHARLIE Message_ChildEnum = 2
)

var Message_ChildEnum_name = map[int32]string{
	0: "ALPHA",
	1: "BRAVO",
	2: "CHARLIE",
}
var Message_ChildEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   1,
	"CHARLIE": 2,
}

func (x Message_ChildEnum) String() string {
	return proto.EnumName(Message_ChildEnum_name, int32(x))
}
func (Message_ChildEnum) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 0} }

type SiblingMessage struct {
	F1 string   `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2 []string `protobuf:"bytes,2,rep,name=f2" json:"f2,omitempty"`
	F3 *Message `protobuf:"bytes,3,opt,name=f3" json:"f3,omitempty"`
}

func (m *SiblingMessage) Reset()                    { *m = SiblingMessage{} }
func (m *SiblingMessage) String() string            { return proto.CompactTextString(m) }
func (*SiblingMessage) ProtoMessage()               {}
func (*SiblingMessage) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *SiblingMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message struct {
	// Optional fields.
	OptionalBool           bool                  `protobuf:"varint,100,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalInt32          int32                 `protobuf:"varint,101,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalSint32         int32                 `protobuf:"zigzag32,102,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalUint32         uint32                `protobuf:"varint,103,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalInt64          int64                 `protobuf:"varint,104,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalSint64         int64                 `protobuf:"zigzag64,105,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalUint64         uint64                `protobuf:"varint,106,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalFixed32        uint32                `protobuf:"fixed32,107,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalSfixed32       int32                 `protobuf:"fixed32,108,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalFloat          float32               `protobuf:"fixed32,109,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalFixed64        uint64                `protobuf:"fixed64,110,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed64       int64                 `protobuf:"fixed64,111,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalDouble         float64               `protobuf:"fixed64,112,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalString         string                `protobuf:"bytes,113,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                `protobuf:"bytes,114,opt,name=optional_bytes,json=optionalBytes,proto3" json:"optional_bytes,omitempty"`
	OptionalChildEnum      Message_ChildEnum     `protobuf:"varint,115,opt,name=optional_child_enum,json=optionalChildEnum,enum=google.golang.org.proto3_20160519.Message_ChildEnum" json:"optional_child_enum,omitempty"`
	OptionalChildMessage   *Message_ChildMessage `protobuf:"bytes,116,opt,name=optional_child_message,json=optionalChildMessage" json:"optional_child_message,omitempty"`
	OptionalSiblingEnum    SiblingEnum           `protobuf:"varint,117,opt,name=optional_sibling_enum,json=optionalSiblingEnum,enum=google.golang.org.proto3_20160519.SiblingEnum" json:"optional_sibling_enum,omitempty"`
	OptionalSiblingMessage *SiblingMessage       `protobuf:"bytes,118,opt,name=optional_sibling_message,json=optionalSiblingMessage" json:"optional_sibling_message,omitempty"`
	// Repeated fields.
	RepeatedBool           []bool                  `protobuf:"varint,200,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedInt32          []int32                 `protobuf:"varint,201,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedSint32         []int32                 `protobuf:"zigzag32,202,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedUint32         []uint32                `protobuf:"varint,203,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedInt64          []int64                 `protobuf:"varint,204,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedSint64         []int64                 `protobuf:"zigzag64,205,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedUint64         []uint64                `protobuf:"varint,206,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedFixed32        []uint32                `protobuf:"fixed32,207,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedSfixed32       []int32                 `protobuf:"fixed32,208,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedFloat          []float32               `protobuf:"fixed32,209,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedFixed64        []uint64                `protobuf:"fixed64,210,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed64       []int64                 `protobuf:"fixed64,211,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedDouble         []float64               `protobuf:"fixed64,212,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedString         []string                `protobuf:"bytes,213,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                `protobuf:"bytes,214,rep,name=repeated_bytes,json=repeatedBytes,proto3" json:"repeated_bytes,omitempty"`
	RepeatedChildEnum      []Message_ChildEnum     `protobuf:"varint,215,rep,name=repeated_child_enum,json=repeatedChildEnum,enum=google.golang.org.proto3_20160519.Message_ChildEnum" json:"repeated_child_enum,omitempty"`
	RepeatedChildMessage   []*Message_ChildMessage `protobuf:"bytes,216,rep,name=repeated_child_message,json=repeatedChildMessage" json:"repeated_child_message,omitempty"`
	RepeatedSiblingEnum    []SiblingEnum           `protobuf:"varint,217,rep,name=repeated_sibling_enum,json=repeatedSiblingEnum,enum=google.golang.org.proto3_20160519.SiblingEnum" json:"repeated_sibling_enum,omitempty"`
	RepeatedSiblingMessage []*SiblingMessage       `protobuf:"bytes,218,rep,name=repeated_sibling_message,json=repeatedSiblingMessage" json:"repeated_sibling_message,omitempty"`
	// Map fields.
	MapBoolBool           map[bool]bool                  `protobuf:"bytes,300,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt32          map[bool]int32                 `protobuf:"bytes,301,rep,name=map_bool_int32,json=mapBoolInt32" json:"map_bool_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint32         map[bool]int32                 `protobuf:"bytes,302,rep,name=map_bool_sint32,json=mapBoolSint32" json:"map_bool_sint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapBoolUint32         map[bool]uint32                `protobuf:"bytes,303,rep,name=map_bool_uint32,json=mapBoolUint32" json:"map_bool_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolInt64          map[bool]int64                 `protobuf:"bytes,304,rep,name=map_bool_int64,json=mapBoolInt64" json:"map_bool_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolSint64         map[bool]int64                 `protobuf:"bytes,305,rep,name=map_bool_sint64,json=mapBoolSint64" json:"map_bool_sint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapBoolUint64         map[bool]uint64                `protobuf:"bytes,306,rep,name=map_bool_uint64,json=mapBoolUint64" json:"map_bool_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapBoolFixed32        map[bool]uint32                `protobuf:"bytes,307,rep,name=map_bool_fixed32,json=mapBoolFixed32" json:"map_bool_fixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolSfixed32       map[bool]int32                 `protobuf:"bytes,308,rep,name=map_bool_sfixed32,json=mapBoolSfixed32" json:"map_bool_sfixed32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFloat          map[bool]float32               `protobuf:"bytes,309,rep,name=map_bool_float,json=mapBoolFloat" json:"map_bool_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapBoolFixed64        map[bool]uint64                `protobuf:"bytes,310,rep,name=map_bool_fixed64,json=mapBoolFixed64" json:"map_bool_fixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolSfixed64       map[bool]int64                 `protobuf:"bytes,311,rep,name=map_bool_sfixed64,json=mapBoolSfixed64" json:"map_bool_sfixed64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolDouble         map[bool]float64               `protobuf:"bytes,312,rep,name=map_bool_double,json=mapBoolDouble" json:"map_bool_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolString         map[bool]string                `protobuf:"bytes,313,rep,name=map_bool_string,json=mapBoolString" json:"map_bool_string,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolBytes          map[bool][]byte                `protobuf:"bytes,314,rep,name=map_bool_bytes,json=mapBoolBytes" json:"map_bool_bytes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolChildEnum      map[bool]Message_ChildEnum     `protobuf:"bytes,315,rep,name=map_bool_child_enum,json=mapBoolChildEnum" json:"map_bool_child_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto3_20160519.Message_ChildEnum"`
	MapBoolChildMessage   map[bool]*Message_ChildMessage `protobuf:"bytes,316,rep,name=map_bool_child_message,json=mapBoolChildMessage" json:"map_bool_child_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapBoolSiblingEnum    map[bool]SiblingEnum           `protobuf:"bytes,317,rep,name=map_bool_sibling_enum,json=mapBoolSiblingEnum" json:"map_bool_sibling_enum,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=google.golang.org.proto3_20160519.SiblingEnum"`
	MapBoolSiblingMessage map[bool]*SiblingMessage       `protobuf:"bytes,318,rep,name=map_bool_sibling_message,json=mapBoolSiblingMessage" json:"map_bool_sibling_message,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapInt32Bool          map[int32]bool                 `protobuf:"bytes,319,rep,name=map_int32_bool,json=mapInt32Bool" json:"map_int32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Bool         map[int32]bool                 `protobuf:"bytes,320,rep,name=map_sint32_bool,json=mapSint32Bool" json:"map_sint32_bool,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Bool         map[uint32]bool                `protobuf:"bytes,321,rep,name=map_uint32_bool,json=mapUint32Bool" json:"map_uint32_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Bool          map[int64]bool                 `protobuf:"bytes,322,rep,name=map_int64_bool,json=mapInt64Bool" json:"map_int64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint64Bool         map[int64]bool                 `protobuf:"bytes,323,rep,name=map_sint64_bool,json=mapSint64Bool" json:"map_sint64_bool,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Bool         map[uint64]bool                `protobuf:"bytes,324,rep,name=map_uint64_bool,json=mapUint64Bool" json:"map_uint64_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapFixed32Bool        map[uint32]bool                `protobuf:"bytes,325,rep,name=map_fixed32_bool,json=mapFixed32Bool" json:"map_fixed32_bool,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringBool         map[string]bool                `protobuf:"bytes,326,rep,name=map_string_bool,json=mapStringBool" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// Oneof fields.
	//
	// Types that are valid to be assigned to OneofUnion:
	//	*Message_OneofBool
	//	*Message_OneofInt32
	//	*Message_OneofSint32
	//	*Message_OneofUint32
	//	*Message_OneofInt64
	//	*Message_OneofSint64
	//	*Message_OneofUint64
	//	*Message_OneofFixed32
	//	*Message_OneofSfixed32
	//	*Message_OneofFloat
	//	*Message_OneofFixed64
	//	*Message_OneofSfixed64
	//	*Message_OneofDouble
	//	*Message_OneofString
	//	*Message_OneofBytes
	//	*Message_OneofChildEnum
	//	*Message_OneofChildMessage
	//	*Message_OneofSiblingEnum
	//	*Message_OneofSiblingMessage
	//	*Message_OneofString1
	//	*Message_OneofString2
	//	*Message_OneofString3
	OneofUnion isMessage_OneofUnion `protobuf_oneof:"oneof_union"`
}

func (m *Message) Reset()                    { *m = Message{} }
func (m *Message) String() string            { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()               {}
func (*Message) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

type isMessage_OneofUnion interface{ isMessage_OneofUnion() }

type Message_OneofBool struct {
	OneofBool bool `protobuf:"varint,400,opt,name=oneof_bool,json=oneofBool,oneof"`
}
type Message_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,401,opt,name=oneof_int32,json=oneofInt32,oneof"`
}
type Message_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,402,opt,name=oneof_sint32,json=oneofSint32,oneof"`
}
type Message_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,403,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}
type Message_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,404,opt,name=oneof_int64,json=oneofInt64,oneof"`
}
type Message_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,405,opt,name=oneof_sint64,json=oneofSint64,oneof"`
}
type Message_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,406,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}
type Message_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,407,opt,name=oneof_fixed32,json=oneofFixed32,oneof"`
}
type Message_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,408,opt,name=oneof_sfixed32,json=oneofSfixed32,oneof"`
}
type Message_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,409,opt,name=oneof_float,json=oneofFloat,oneof"`
}
type Message_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,410,opt,name=oneof_fixed64,json=oneofFixed64,oneof"`
}
type Message_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,411,opt,name=oneof_sfixed64,json=oneofSfixed64,oneof"`
}
type Message_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,412,opt,name=oneof_double,json=oneofDouble,oneof"`
}
type Message_OneofString struct {
	OneofString string `protobuf:"bytes,413,opt,name=oneof_string,json=oneofString,oneof"`
}
type Message_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,414,opt,name=oneof_bytes,json=oneofBytes,proto3,oneof"`
}
type Message_OneofChildEnum struct {
	OneofChildEnum Message_ChildEnum `protobuf:"varint,415,opt,name=oneof_child_enum,json=oneofChildEnum,enum=google.golang.org.proto3_20160519.Message_ChildEnum,oneof"`
}
type Message_OneofChildMessage struct {
	OneofChildMessage *Message_ChildMessage `protobuf:"bytes,416,opt,name=oneof_child_message,json=oneofChildMessage,oneof"`
}
type Message_OneofSiblingEnum struct {
	OneofSiblingEnum SiblingEnum `protobuf:"varint,417,opt,name=oneof_sibling_enum,json=oneofSiblingEnum,enum=google.golang.org.proto3_20160519.SiblingEnum,oneof"`
}
type Message_OneofSiblingMessage struct {
	OneofSiblingMessage *SiblingMessage `protobuf:"bytes,418,opt,name=oneof_sibling_message,json=oneofSiblingMessage,oneof"`
}
type Message_OneofString1 struct {
	OneofString1 string `protobuf:"bytes,419,opt,name=oneof_string1,json=oneofString1,oneof"`
}
type Message_OneofString2 struct {
	OneofString2 string `protobuf:"bytes,420,opt,name=oneof_string2,json=oneofString2,oneof"`
}
type Message_OneofString3 struct {
	OneofString3 string `protobuf:"bytes,421,opt,name=oneof_string3,json=oneofString3,oneof"`
}

func (*Message_OneofBool) isMessage_OneofUnion()           {}
func (*Message_OneofInt32) isMessage_OneofUnion()          {}
func (*Message_OneofSint32) isMessage_OneofUnion()         {}
func (*Message_OneofUint32) isMessage_OneofUnion()         {}
func (*Message_OneofInt64) isMessage_OneofUnion()          {}
func (*Message_OneofSint64) isMessage_OneofUnion()         {}
func (*Message_OneofUint64) isMessage_OneofUnion()         {}
func (*Message_OneofFixed32) isMessage_OneofUnion()        {}
func (*Message_OneofSfixed32) isMessage_OneofUnion()       {}
func (*Message_OneofFloat) isMessage_OneofUnion()          {}
func (*Message_OneofFixed64) isMessage_OneofUnion()        {}
func (*Message_OneofSfixed64) isMessage_OneofUnion()       {}
func (*Message_OneofDouble) isMessage_OneofUnion()         {}
func (*Message_OneofString) isMessage_OneofUnion()         {}
func (*Message_OneofBytes) isMessage_OneofUnion()          {}
func (*Message_OneofChildEnum) isMessage_OneofUnion()      {}
func (*Message_OneofChildMessage) isMessage_OneofUnion()   {}
func (*Message_OneofSiblingEnum) isMessage_OneofUnion()    {}
func (*Message_OneofSiblingMessage) isMessage_OneofUnion() {}
func (*Message_OneofString1) isMessage_OneofUnion()        {}
func (*Message_OneofString2) isMessage_OneofUnion()        {}
func (*Message_OneofString3) isMessage_OneofUnion()        {}

func (m *Message) GetOneofUnion() isMessage_OneofUnion {
	if m != nil {
		return m.OneofUnion
	}
	return nil
}

func (m *Message) GetOptionalChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.OptionalChildMessage
	}
	return nil
}

func (m *Message) GetOptionalSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.OptionalSiblingMessage
	}
	return nil
}

func (m *Message) GetRepeatedChildMessage() []*Message_ChildMessage {
	if m != nil {
		return m.RepeatedChildMessage
	}
	return nil
}

func (m *Message) GetRepeatedSiblingMessage() []*SiblingMessage {
	if m != nil {
		return m.RepeatedSiblingMessage
	}
	return nil
}

func (m *Message) GetMapBoolBool() map[bool]bool {
	if m != nil {
		return m.MapBoolBool
	}
	return nil
}

func (m *Message) GetMapBoolInt32() map[bool]int32 {
	if m != nil {
		return m.MapBoolInt32
	}
	return nil
}

func (m *Message) GetMapBoolSint32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSint32
	}
	return nil
}

func (m *Message) GetMapBoolUint32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolUint32
	}
	return nil
}

func (m *Message) GetMapBoolInt64() map[bool]int64 {
	if m != nil {
		return m.MapBoolInt64
	}
	return nil
}

func (m *Message) GetMapBoolSint64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSint64
	}
	return nil
}

func (m *Message) GetMapBoolUint64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolUint64
	}
	return nil
}

func (m *Message) GetMapBoolFixed32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolFixed32
	}
	return nil
}

func (m *Message) GetMapBoolSfixed32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSfixed32
	}
	return nil
}

func (m *Message) GetMapBoolFloat() map[bool]float32 {
	if m != nil {
		return m.MapBoolFloat
	}
	return nil
}

func (m *Message) GetMapBoolFixed64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolFixed64
	}
	return nil
}

func (m *Message) GetMapBoolSfixed64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSfixed64
	}
	return nil
}

func (m *Message) GetMapBoolDouble() map[bool]float64 {
	if m != nil {
		return m.MapBoolDouble
	}
	return nil
}

func (m *Message) GetMapBoolString() map[bool]string {
	if m != nil {
		return m.MapBoolString
	}
	return nil
}

func (m *Message) GetMapBoolBytes() map[bool][]byte {
	if m != nil {
		return m.MapBoolBytes
	}
	return nil
}

func (m *Message) GetMapBoolChildEnum() map[bool]Message_ChildEnum {
	if m != nil {
		return m.MapBoolChildEnum
	}
	return nil
}

func (m *Message) GetMapBoolChildMessage() map[bool]*Message_ChildMessage {
	if m != nil {
		return m.MapBoolChildMessage
	}
	return nil
}

func (m *Message) GetMapBoolSiblingEnum() map[bool]SiblingEnum {
	if m != nil {
		return m.MapBoolSiblingEnum
	}
	return nil
}

func (m *Message) GetMapBoolSiblingMessage() map[bool]*SiblingMessage {
	if m != nil {
		return m.MapBoolSiblingMessage
	}
	return nil
}

func (m *Message) GetMapInt32Bool() map[int32]bool {
	if m != nil {
		return m.MapInt32Bool
	}
	return nil
}

func (m *Message) GetMapSint32Bool() map[int32]bool {
	if m != nil {
		return m.MapSint32Bool
	}
	return nil
}

func (m *Message) GetMapUint32Bool() map[uint32]bool {
	if m != nil {
		return m.MapUint32Bool
	}
	return nil
}

func (m *Message) GetMapInt64Bool() map[int64]bool {
	if m != nil {
		return m.MapInt64Bool
	}
	return nil
}

func (m *Message) GetMapSint64Bool() map[int64]bool {
	if m != nil {
		return m.MapSint64Bool
	}
	return nil
}

func (m *Message) GetMapUint64Bool() map[uint64]bool {
	if m != nil {
		return m.MapUint64Bool
	}
	return nil
}

func (m *Message) GetMapFixed32Bool() map[uint32]bool {
	if m != nil {
		return m.MapFixed32Bool
	}
	return nil
}

func (m *Message) GetMapStringBool() map[string]bool {
	if m != nil {
		return m.MapStringBool
	}
	return nil
}

func (m *Message) GetOneofBool() bool {
	if x, ok := m.GetOneofUnion().(*Message_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (m *Message) GetOneofInt32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (m *Message) GetOneofSint32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (m *Message) GetOneofUint32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (m *Message) GetOneofInt64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (m *Message) GetOneofSint64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (m *Message) GetOneofUint64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (m *Message) GetOneofFixed32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (m *Message) GetOneofSfixed32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (m *Message) GetOneofFloat() float32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (m *Message) GetOneofFixed64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (m *Message) GetOneofSfixed64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (m *Message) GetOneofDouble() float64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (m *Message) GetOneofString() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (m *Message) GetOneofBytes() []byte {
	if x, ok := m.GetOneofUnion().(*Message_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (m *Message) GetOneofChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildEnum); ok {
		return x.OneofChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOneofChildMessage() *Message_ChildMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildMessage); ok {
		return x.OneofChildMessage
	}
	return nil
}

func (m *Message) GetOneofSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingEnum); ok {
		return x.OneofSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOneofSiblingMessage() *SiblingMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingMessage); ok {
		return x.OneofSiblingMessage
	}
	return nil
}

func (m *Message) GetOneofString1() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString1); ok {
		return x.OneofString1
	}
	return ""
}

func (m *Message) GetOneofString2() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString2); ok {
		return x.OneofString2
	}
	return ""
}

func (m *Message) GetOneofString3() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString3); ok {
		return x.OneofString3
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*Message) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _Message_OneofMarshaler, _Message_OneofUnmarshaler, _Message_OneofSizer, []interface{}{
		(*Message_OneofBool)(nil),
		(*Message_OneofInt32)(nil),
		(*Message_OneofSint32)(nil),
		(*Message_OneofUint32)(nil),
		(*Message_OneofInt64)(nil),
		(*Message_OneofSint64)(nil),
		(*Message_OneofUint64)(nil),
		(*Message_OneofFixed32)(nil),
		(*Message_OneofSfixed32)(nil),
		(*Message_OneofFloat)(nil),
		(*Message_OneofFixed64)(nil),
		(*Message_OneofSfixed64)(nil),
		(*Message_OneofDouble)(nil),
		(*Message_OneofString)(nil),
		(*Message_OneofBytes)(nil),
		(*Message_OneofChildEnum)(nil),
		(*Message_OneofChildMessage)(nil),
		(*Message_OneofSiblingEnum)(nil),
		(*Message_OneofSiblingMessage)(nil),
		(*Message_OneofString1)(nil),
		(*Message_OneofString2)(nil),
		(*Message_OneofString3)(nil),
	}
}

func _Message_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		t := uint64(0)
		if x.OneofBool {
			t = 1
		}
		b.EncodeVarint(400<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case *Message_OneofInt32:
		b.EncodeVarint(401<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		b.EncodeVarint(402<<3 | proto.WireVarint)
		b.EncodeZigzag32(uint64(x.OneofSint32))
	case *Message_OneofUint32:
		b.EncodeVarint(403<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		b.EncodeVarint(404<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		b.EncodeVarint(405<<3 | proto.WireVarint)
		b.EncodeZigzag64(uint64(x.OneofSint64))
	case *Message_OneofUint64:
		b.EncodeVarint(406<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		b.EncodeVarint(407<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofFixed32))
	case *Message_OneofSfixed32:
		b.EncodeVarint(408<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofSfixed32))
	case *Message_OneofFloat:
		b.EncodeVarint(409<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(math.Float32bits(x.OneofFloat)))
	case *Message_OneofFixed64:
		b.EncodeVarint(410<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofFixed64))
	case *Message_OneofSfixed64:
		b.EncodeVarint(411<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofSfixed64))
	case *Message_OneofDouble:
		b.EncodeVarint(412<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.OneofDouble))
	case *Message_OneofString:
		b.EncodeVarint(413<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString)
	case *Message_OneofBytes:
		b.EncodeVarint(414<<3 | proto.WireBytes)
		b.EncodeRawBytes(x.OneofBytes)
	case *Message_OneofChildEnum:
		b.EncodeVarint(415<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		b.EncodeVarint(416<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofChildMessage); err != nil {
			return err
		}
	case *Message_OneofSiblingEnum:
		b.EncodeVarint(417<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		b.EncodeVarint(418<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofSiblingMessage); err != nil {
			return err
		}
	case *Message_OneofString1:
		b.EncodeVarint(419<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString1)
	case *Message_OneofString2:
		b.EncodeVarint(420<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString2)
	case *Message_OneofString3:
		b.EncodeVarint(421<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString3)
	case nil:
	default:
		return fmt.Errorf("Message.OneofUnion has unexpected type %T", x)
	}
	return nil
}

func _Message_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*Message)
	switch tag {
	case 400: // oneof_union.oneof_bool
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofBool{x != 0}
		return true, err
	case 401: // oneof_union.oneof_int32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt32{int32(x)}
		return true, err
	case 402: // oneof_union.oneof_sint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag32()
		m.OneofUnion = &Message_OneofSint32{int32(x)}
		return true, err
	case 403: // oneof_union.oneof_uint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint32{uint32(x)}
		return true, err
	case 404: // oneof_union.oneof_int64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt64{int64(x)}
		return true, err
	case 405: // oneof_union.oneof_sint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag64()
		m.OneofUnion = &Message_OneofSint64{int64(x)}
		return true, err
	case 406: // oneof_union.oneof_uint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint64{x}
		return true, err
	case 407: // oneof_union.oneof_fixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFixed32{uint32(x)}
		return true, err
	case 408: // oneof_union.oneof_sfixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofSfixed32{int32(x)}
		return true, err
	case 409: // oneof_union.oneof_float
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFloat{math.Float32frombits(uint32(x))}
		return true, err
	case 410: // oneof_union.oneof_fixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofFixed64{x}
		return true, err
	case 411: // oneof_union.oneof_sfixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofSfixed64{int64(x)}
		return true, err
	case 412: // oneof_union.oneof_double
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofDouble{math.Float64frombits(x)}
		return true, err
	case 413: // oneof_union.oneof_string
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString{x}
		return true, err
	case 414: // oneof_union.oneof_bytes
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeRawBytes(true)
		m.OneofUnion = &Message_OneofBytes{x}
		return true, err
	case 415: // oneof_union.oneof_child_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofChildEnum{Message_ChildEnum(x)}
		return true, err
	case 416: // oneof_union.oneof_child_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message_ChildMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofChildMessage{msg}
		return true, err
	case 417: // oneof_union.oneof_sibling_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofSiblingEnum{SiblingEnum(x)}
		return true, err
	case 418: // oneof_union.oneof_sibling_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SiblingMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofSiblingMessage{msg}
		return true, err
	case 419: // oneof_union.oneof_string1
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString1{x}
		return true, err
	case 420: // oneof_union.oneof_string2
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString2{x}
		return true, err
	case 421: // oneof_union.oneof_string3
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString3{x}
		return true, err
	default:
		return false, nil
	}
}

func _Message_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		n += proto.SizeVarint(400<<3 | proto.WireVarint)
		n += 1
	case *Message_OneofInt32:
		n += proto.SizeVarint(401<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		n += proto.SizeVarint(402<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64((uint32(x.OneofSint32) << 1) ^ uint32((int32(x.OneofSint32) >> 31))))
	case *Message_OneofUint32:
		n += proto.SizeVarint(403<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		n += proto.SizeVarint(404<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		n += proto.SizeVarint(405<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(uint64(x.OneofSint64<<1) ^ uint64((int64(x.OneofSint64) >> 63))))
	case *Message_OneofUint64:
		n += proto.SizeVarint(406<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		n += proto.SizeVarint(407<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofSfixed32:
		n += proto.SizeVarint(408<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofFloat:
		n += proto.SizeVarint(409<<3 | proto.WireFixed32)
		n += 4
	case *Message_OneofFixed64:
		n += proto.SizeVarint(410<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofSfixed64:
		n += proto.SizeVarint(411<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofDouble:
		n += proto.SizeVarint(412<<3 | proto.WireFixed64)
		n += 8
	case *Message_OneofString:
		n += proto.SizeVarint(413<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString)))
		n += len(x.OneofString)
	case *Message_OneofBytes:
		n += proto.SizeVarint(414<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofBytes)))
		n += len(x.OneofBytes)
	case *Message_OneofChildEnum:
		n += proto.SizeVarint(415<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		s := proto.Size(x.OneofChildMessage)
		n += proto.SizeVarint(416<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofSiblingEnum:
		n += proto.SizeVarint(417<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		s := proto.Size(x.OneofSiblingMessage)
		n += proto.SizeVarint(418<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofString1:
		n += proto.SizeVarint(419<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString1)))
		n += len(x.OneofString1)
	case *Message_OneofString2:
		n += proto.SizeVarint(420<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString2)))
		n += len(x.OneofString2)
	case *Message_OneofString3:
		n += proto.SizeVarint(421<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.OneofString3)))
		n += len(x.OneofString3)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type Message_ChildMessage struct {
	F1 string   `protobuf:"bytes,1,opt,name=f1" json:"f1,omitempty"`
	F2 []string `protobuf:"bytes,2,rep,name=f2" json:"f2,omitempty"`
	F3 *Message `protobuf:"bytes,3,opt,name=f3" json:"f3,omitempty"`
}

func (m *Message_ChildMessage) Reset()                    { *m = Message_ChildMessage{} }
func (m *Message_ChildMessage) String() string            { return proto.CompactTextString(m) }
func (*Message_ChildMessage) ProtoMessage()               {}
func (*Message_ChildMessage) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1, 0} }

func (m *Message_ChildMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

func init() {
	proto.RegisterType((*SiblingMessage)(nil), "google.golang.org.proto3_20160519.SiblingMessage")
	proto.RegisterType((*Message)(nil), "google.golang.org.proto3_20160519.Message")
	proto.RegisterType((*Message_ChildMessage)(nil), "google.golang.org.proto3_20160519.Message.ChildMessage")
	proto.RegisterEnum("google.golang.org.proto3_20160519.SiblingEnum", SiblingEnum_name, SiblingEnum_value)
	proto.RegisterEnum("google.golang.org.proto3_20160519.Message_ChildEnum", Message_ChildEnum_name, Message_ChildEnum_value)
}

var fileDescriptor0 = []byte{
	// 1947 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x9a, 0x57, 0x73, 0xdb, 0xca,
	0x15, 0xc7, 0x09, 0x52, 0xc5, 0x5a, 0xb1, 0x82, 0x96, 0xb2, 0xa3, 0x27, 0x44, 0x76, 0x1c, 0xc4,
	0xc9, 0x50, 0x26, 0x05, 0x23, 0xb1, 0x93, 0xd8, 0x96, 0x6c, 0x39, 0x74, 0xc6, 0x4e, 0x3c, 0xf0,
	0x28, 0x0f, 0x79, 0x51, 0x48, 0x09, 0xa4, 0x69, 0x83, 0x84, 0x22, 0x92, 0x9e, 0x68, 0xf2, 0xe0,
	0xaf, 0x90, 0xde, 0xeb, 0x2d, 0x6f, 0xb7, 0xf7, 0xde, 0x7d, 0xc7, 0xb7, 0xf7, 0xfa, 0x69, 0xee,
	0x2c, 0x0e, 0xb6, 0x01, 0xa0, 0x49, 0x82, 0x73, 0x1f, 0x3c, 0x23, 0x1d, 0xfe, 0xf7, 0xfc, 0x78,
	0x0e, 0xce, 0x9e, 0xb3, 0x0b, 0x0b, 0x1d, 0xda, 0xdd, 0x73, 0x7b, 0xee, 0xea, 0x56, 0xe5, 0x58,
	0xd9, 0x3c, 0x76, 0xbc, 0x7c, 0x62, 0xab, 0x66, 0xd4, 0xea, 0x27, 0xec, 0xed, 0xe3, 0x2b, 0x3d,
	0xbb, 0xdb, 0x2b, 0x79, 0x9f, 0xaa, 0xdf, 0x6c, 0xba, 0x6e, 0xd3, 0xb1, 0x4b, 0x4d, 0xd7, 0xa9,
	0x75, 0x9a, 0x25, 0x77, 0xaf, 0x59, 0x0a, 0x2c, 0x5b, 0x76, 0x50, 0xf6, 0x4a, 0xab, 0xee, 0xb4,
	0x3a, 0xcd, 0x4b, 0x76, 0xb7, 0x5b, 0x6b, 0xda, 0x6a, 0x16, 0x25, 0x1b, 0x65, 0xac, 0x68, 0x8a,
	0x3e, 0x67, 0x25, 0x1b, 0x65, 0xef, 0xf7, 0x0a, 0x4e, 0x6a, 0x29, 0xef, 0xf7, 0x8a, 0x7a, 0x12,
	0x25, 0x1b, 0xab, 0x38, 0xa5, 0x29, 0xfa, 0x7c, 0xe5, 0x68, 0x69, 0x28, 0xa1, 0xe4, 0xfb, 0xb5,
	0x92, 0x8d, 0xd5, 0xe5, 0x5b, 0x67, 0xd0, 0x2c, 0xe5, 0x1c, 0x42, 0x19, 0x77, 0xb7, 0xd7, 0x72,
	0x3b, 0x35, 0x67, 0xab, 0xee, 0xba, 0x0e, 0xde, 0xd1, 0x14, 0xfd, 0x80, 0x95, 0xa6, 0xc6, 0x75,
	0xd7, 0x75, 0xd4, 0x6f, 0xa1, 0x2c, 0x13, 0xb5, 0x3a, 0xbd, 0xd5, 0x0a, 0xb6, 0x35, 0x45, 0x9f,
	0xb6, 0xd8, 0xd2, 0x0b, 0xc4, 0xa8, 0x7e, 0x1b, 0xe5, 0x98, 0xac, 0x0b, 0xba, 0x86, 0xa6, 0xe8,
	0x05, 0x8b, 0xad, 0xbe, 0xd2, 0x0a, 0x09, 0xfb, 0x20, 0x6c, 0x6a, 0x8a, 0x9e, 0xe1, 0xc2, 0x4d,
	0x10, 0x06, 0xc0, 0xa6, 0x81, 0xaf, 0x6a, 0x8a, 0x9e, 0x92, 0xc0, 0xa6, 0x11, 0x02, 0x9b, 0x06,
	0x6e, 0x69, 0x8a, 0xae, 0xca, 0xe0, 0x80, 0xb0, 0x0f, 0xc2, 0x6b, 0x9a, 0xa2, 0x4f, 0xc9, 0x60,
	0xd3, 0x50, 0xbf, 0x83, 0xf2, 0x4c, 0xd8, 0x68, 0xfd, 0xc6, 0xde, 0x59, 0xad, 0xe0, 0xeb, 0x9a,
	0xa2, 0xcf, 0x5a, 0xcc, 0xc1, 0x79, 0x30, 0xab, 0xdf, 0x45, 0x05, 0x0e, 0xa7, 0x5a, 0x47, 0x53,
	0xf4, 0x9c, 0xc5, 0x7c, 0x5c, 0xf1, 0xed, 0x52, 0x40, 0x0d, 0xc7, 0xad, 0xf5, 0x70, 0x5b, 0x53,
	0xf4, 0x24, 0x0f, 0xe8, 0x3c, 0x31, 0x86, 0xf1, 0xa6, 0x81, 0x3b, 0x9a, 0xa2, 0xcf, 0x04, 0xf0,
	0xa6, 0x11, 0x81, 0x37, 0x0d, 0xec, 0x6a, 0x8a, 0x9e, 0x0f, 0xe2, 0x03, 0xf1, 0xef, 0xb8, 0xfd,
	0xba, 0x63, 0xe3, 0x5d, 0x4d, 0xd1, 0x15, 0x1e, 0xff, 0x39, 0xcf, 0x2a, 0x67, 0xb4, 0xb7, 0xd7,
	0xea, 0x34, 0xf1, 0xaf, 0xbd, 0x5a, 0xe4, 0x19, 0xf5, 0xac, 0x52, 0x40, 0xf5, 0xfd, 0x9e, 0xdd,
	0xc5, 0x7b, 0x9a, 0xa2, 0xa7, 0x79, 0x40, 0xeb, 0xc4, 0xa8, 0xee, 0xa0, 0x22, 0x93, 0x6d, 0x5f,
	0x6d, 0x39, 0x3b, 0x5b, 0x76, 0xa7, 0xdf, 0xc6, 0x5d, 0x4d, 0xd1, 0xb3, 0x15, 0x63, 0xf4, 0xfa,
	0x2d, 0x9d, 0x25, 0x8b, 0x37, 0x3a, 0xfd, 0xb6, 0xc5, 0xc2, 0x66, 0x26, 0xb5, 0x8d, 0x16, 0x03,
	0x94, 0x36, 0x2c, 0xc3, 0x3d, 0x6f, 0xa3, 0x7c, 0x7f, 0x5c, 0x10, 0xdd, 0x35, 0x07, 0x25, 0x16,
	0xdd, 0x3b, 0x75, 0xb4, 0x20, 0x94, 0x9d, 0xb7, 0x7d, 0x21, 0xac, 0xbe, 0x17, 0x56, 0x69, 0x04,
	0x9a, 0xbf, 0xeb, 0xbd, 0x80, 0x8a, 0xbc, 0x58, 0x99, 0x51, 0xbd, 0x8e, 0x70, 0x88, 0x41, 0x83,
	0xba, 0xe1, 0x05, 0x55, 0x1e, 0x1d, 0x43, 0xc3, 0x59, 0x0c, 0x90, 0x68, 0x40, 0x87, 0x51, 0x66,
	0xcf, 0xde, 0xb5, 0x6b, 0x3d, 0x7b, 0x07, 0x9a, 0xc1, 0x6d, 0x45, 0x4b, 0x91, 0x6e, 0x40, 0xad,
	0x5e, 0x37, 0x38, 0x82, 0xb2, 0x4c, 0x05, 0x9b, 0xf7, 0x0d, 0x22, 0x9b, 0xb6, 0xd8, 0x62, 0x68,
	0x07, 0x3a, 0xca, 0x31, 0x9d, 0xdf, 0x0e, 0xde, 0x24, 0xc2, 0x82, 0xc5, 0xd6, 0xfb, 0xfd, 0x40,
	0x54, 0xfa, 0xfd, 0xe0, 0x2d, 0xa2, 0xcc, 0x70, 0xa5, 0xdf, 0x10, 0x02, 0x6c, 0xd3, 0xc0, 0x6f,
	0x13, 0x61, 0x4a, 0x62, 0x9b, 0x46, 0x88, 0x6d, 0x1a, 0xf8, 0x1d, 0x22, 0x54, 0x65, 0x76, 0x40,
	0xe9, 0xb7, 0x84, 0x77, 0x89, 0x72, 0x4a, 0x66, 0x9b, 0x86, 0x7a, 0x14, 0xe5, 0x99, 0x92, 0xee,
	0xf3, 0xf7, 0x88, 0x74, 0xd6, 0x62, 0x2e, 0x68, 0x53, 0xf8, 0x1e, 0x2a, 0x70, 0x3e, 0x15, 0xbf,
	0x4f, 0xc4, 0x39, 0x8b, 0x79, 0x61, 0x5d, 0x41, 0x8c, 0x0a, 0xba, 0xc2, 0x07, 0x44, 0x9a, 0xe4,
	0x51, 0x41, 0x5b, 0x08, 0x7d, 0x03, 0xd3, 0xc0, 0x1f, 0x12, 0xe5, 0x4c, 0xe0, 0x1b, 0x98, 0x46,
	0xc4, 0x37, 0x30, 0x0d, 0xfc, 0x11, 0x11, 0xe7, 0x83, 0xdf, 0x20, 0x90, 0x05, 0xbf, 0x31, 0x7c,
	0x4c, 0xb4, 0x0a, 0xcf, 0x82, 0xdf, 0x19, 0xa4, 0xcc, 0x42, 0x67, 0xf8, 0x44, 0xf1, 0xc6, 0x12,
	0xcf, 0x2c, 0xb4, 0x06, 0x31, 0x2a, 0x68, 0x0d, 0x9f, 0x12, 0x61, 0x9a, 0x47, 0x05, 0xbd, 0xc1,
	0x46, 0x45, 0xa6, 0x13, 0x7a, 0xc3, 0x67, 0x44, 0x1c, 0xbb, 0x39, 0x50, 0x8f, 0xbc, 0x39, 0x74,
	0xd0, 0x62, 0x00, 0x43, 0xf7, 0xd1, 0xe7, 0x84, 0x34, 0x49, 0x77, 0x90, 0x60, 0x74, 0x33, 0x6d,
	0xa3, 0x05, 0xa1, 0x04, 0x85, 0xee, 0xf0, 0x05, 0x04, 0x36, 0x76, 0x7b, 0xe0, 0x85, 0xcb, 0xdb,
	0x83, 0x83, 0x70, 0x08, 0x42, 0xc3, 0xfa, 0x12, 0xc2, 0x8a, 0xd3, 0x1f, 0x02, 0x28, 0x1a, 0xd2,
	0xaf, 0x50, 0xa6, 0x5d, 0xdb, 0xf5, 0x5a, 0x03, 0xf4, 0x87, 0xfb, 0x92, 0x1e, 0xe2, 0x87, 0x63,
	0x64, 0xee, 0x52, 0x6d, 0x97, 0x74, 0x11, 0xf2, 0x6f, 0xa3, 0xd3, 0xdb, 0xdb, 0xb7, 0xe6, 0xdb,
	0xdc, 0xa2, 0x6e, 0xa3, 0x2c, 0x23, 0x40, 0x23, 0xb8, 0x1f, 0x10, 0x3f, 0x1a, 0x1f, 0xe1, 0x75,
	0x21, 0x60, 0xa4, 0xdb, 0x82, 0x49, 0x6d, 0xa0, 0x1c, 0x83, 0xf8, 0x8d, 0xe9, 0x01, 0xa0, 0xfc,
	0x78, 0x7c, 0x0a, 0xb4, 0x30, 0xc0, 0x64, 0xda, 0xa2, 0x4d, 0xe2, 0xf8, 0x6d, 0xed, 0xc1, 0xd8,
	0x9c, 0xcd, 0x08, 0x8e, 0xdf, 0x14, 0x03, 0x49, 0x33, 0x0d, 0xfc, 0xd0, 0x24, 0x49, 0x33, 0x8d,
	0x50, 0xd2, 0x4c, 0x23, 0x94, 0x34, 0xd3, 0xc0, 0x0f, 0x4f, 0x94, 0x34, 0x8a, 0x11, 0x93, 0x16,
	0xe0, 0xf8, 0xfd, 0xf8, 0x91, 0x89, 0x92, 0x16, 0xe4, 0xf8, 0xdd, 0xbc, 0x85, 0xf2, 0x8c, 0x43,
	0x1b, 0xf4, 0xa3, 0x00, 0x3a, 0x35, 0x3e, 0xc8, 0xef, 0xfb, 0x40, 0xca, 0xb6, 0x25, 0xa3, 0xea,
	0xa0, 0x02, 0x4f, 0x1d, 0x65, 0x3d, 0x06, 0xac, 0xd3, 0x31, 0x92, 0xd7, 0x10, 0x61, 0xb9, 0xb6,
	0x6c, 0x95, 0xaa, 0x01, 0x86, 0xc9, 0xe3, 0xb1, 0xab, 0xc1, 0x1b, 0x3b, 0x72, 0x35, 0xc0, 0x24,
	0x0a, 0x65, 0xcf, 0x34, 0xf0, 0x13, 0x93, 0x65, 0x8f, 0x3e, 0x27, 0x29, 0x7b, 0xa6, 0x11, 0x91,
	0x3d, 0xd3, 0xc0, 0x4f, 0x4e, 0x98, 0x3d, 0x0a, 0x93, 0xb3, 0x17, 0x28, 0x3f, 0x7f, 0x10, 0x3e,
	0x15, 0xbb, 0xfc, 0x60, 0x64, 0xca, 0xe5, 0xe7, 0x8f, 0x51, 0x69, 0x3b, 0xc1, 0x18, 0x7d, 0x3a,
	0xfe, 0x76, 0xf2, 0x1c, 0x04, 0xb6, 0x13, 0x0c, 0x61, 0xb1, 0x1a, 0x60, 0x08, 0x3f, 0x13, 0xbb,
	0x1a, 0xbc, 0x71, 0x2d, 0x57, 0x03, 0x4c, 0xf0, 0x5d, 0x54, 0x64, 0x10, 0x61, 0x82, 0x3f, 0x0b,
	0xa4, 0x33, 0xe3, 0x93, 0xd8, 0xd4, 0x06, 0x5a, 0xbe, 0x1d, 0x30, 0xab, 0xfb, 0x68, 0x31, 0x40,
	0xa4, 0x53, 0xef, 0x39, 0x80, 0x9e, 0x8d, 0x09, 0xf5, 0x6d, 0xc0, 0x2d, 0xb6, 0xc3, 0x9f, 0xa8,
	0x37, 0xd0, 0x82, 0xd0, 0x08, 0x85, 0xb9, 0xfe, 0x3c, 0x90, 0xd7, 0xe3, 0xb4, 0x43, 0x36, 0xd1,
	0x01, 0xac, 0xb6, 0x43, 0x1f, 0xa8, 0x37, 0x11, 0x0e, 0x71, 0x69, 0xd0, 0x2f, 0x00, 0x7a, 0x23,
	0x36, 0x5a, 0x0a, 0x7b, 0xa1, 0x1d, 0xf5, 0x19, 0x2d, 0x25, 0x6f, 0xe6, 0xc0, 0xf8, 0x7f, 0x31,
	0x56, 0x29, 0x79, 0x43, 0x98, 0xcf, 0x7f, 0x52, 0x4a, 0xcc, 0x44, 0xf7, 0x45, 0x57, 0xa0, 0xbc,
	0x14, 0x6b, 0x5f, 0xc0, 0x0c, 0xe6, 0x18, 0xb2, 0x2f, 0xb8, 0x8d, 0x72, 0xfa, 0x02, 0xe7, 0xe5,
	0x58, 0x9c, 0xcd, 0x08, 0x0e, 0xb7, 0x09, 0x49, 0x33, 0x0d, 0xc0, 0xbc, 0x12, 0x37, 0x69, 0xa6,
	0x11, 0x4a, 0x1a, 0x98, 0xc4, 0xa4, 0x51, 0xca, 0xab, 0xb1, 0x93, 0x26, 0x62, 0x68, 0xd2, 0x64,
	0x4e, 0x5f, 0xe0, 0xbc, 0x16, 0x3b, 0x69, 0x41, 0x0e, 0xb7, 0xd1, 0xe9, 0xe2, 0x4f, 0x34, 0x00,
	0xdd, 0x8a, 0x35, 0x5d, 0xfc, 0x11, 0xcc, 0x49, 0xe4, 0x69, 0x08, 0x46, 0x96, 0x3a, 0xaf, 0x5b,
	0x02, 0xe9, 0xf5, 0x78, 0xa9, 0xf3, 0x3c, 0x04, 0x52, 0xc7, 0x6c, 0xaa, 0x86, 0x90, 0xdb, 0xb1,
	0xdd, 0x06, 0x20, 0x7e, 0x97, 0xd2, 0x14, 0xfd, 0x40, 0x35, 0x61, 0xcd, 0x79, 0x46, 0x4f, 0xb1,
	0x8c, 0xe6, 0x41, 0x01, 0x27, 0xc5, 0xdf, 0x13, 0xc9, 0x74, 0x35, 0x61, 0xc1, 0x3a, 0x38, 0xb9,
	0x1e, 0x46, 0x69, 0xd0, 0xf8, 0xc7, 0xd6, 0x3f, 0x10, 0x51, 0xa1, 0x9a, 0xb0, 0x60, 0xa9, 0x7f,
	0xee, 0x64, 0x2a, 0xff, 0xd0, 0xf9, 0x47, 0xa2, 0xca, 0x30, 0x95, 0x7f, 0x6a, 0x14, 0x79, 0xa6,
	0x81, 0xff, 0x44, 0x44, 0x29, 0x91, 0x67, 0x1a, 0x32, 0xcf, 0x34, 0xf0, 0x9f, 0x89, 0x48, 0x95,
	0x78, 0xa2, 0xca, 0x3f, 0xaf, 0xfd, 0x85, 0xa8, 0xa6, 0x24, 0x9e, 0x69, 0xa8, 0x47, 0x50, 0x06,
	0x54, 0xf4, 0x04, 0xf4, 0x57, 0x22, 0x9b, 0xad, 0x26, 0x2c, 0x58, 0x4d, 0x4f, 0x4b, 0x3a, 0xca,
	0xfa, 0x4c, 0x2a, 0xfc, 0x1b, 0x11, 0xe6, 0xaa, 0x09, 0x0b, 0x1c, 0xb0, 0x93, 0x0e, 0x8b, 0x00,
	0x8e, 0x39, 0x7f, 0x27, 0xb2, 0x24, 0x8b, 0x00, 0x0e, 0x2a, 0x32, 0xd5, 0x34, 0xf0, 0x3f, 0x88,
	0x6a, 0x46, 0xa6, 0x7a, 0x17, 0x60, 0x89, 0x6a, 0x1a, 0xf8, 0x9f, 0x44, 0x98, 0x0f, 0x50, 0xc5,
	0x68, 0xfd, 0xe3, 0xc1, 0xbf, 0x88, 0x4e, 0x61, 0xd1, 0xfa, 0xf3, 0x9d, 0x67, 0x0e, 0x86, 0xfb,
	0xbf, 0x89, 0x6a, 0x8e, 0x67, 0x0e, 0xa6, 0x33, 0x8b, 0x00, 0x46, 0xf3, 0x7f, 0x88, 0x28, 0xcd,
	0x22, 0x80, 0xe1, 0x5a, 0x43, 0x79, 0xd0, 0x08, 0x93, 0xf5, 0xbf, 0xa9, 0xf8, 0x2f, 0xce, 0xaa,
	0x09, 0x0b, 0x42, 0xe5, 0xd3, 0xf4, 0x1a, 0x2a, 0x8a, 0x08, 0x3a, 0x55, 0xfe, 0x97, 0x9a, 0xe8,
	0xad, 0x59, 0x35, 0x61, 0x15, 0x38, 0x88, 0x4e, 0x91, 0x2d, 0xa4, 0xd2, 0x92, 0x12, 0x66, 0xe7,
	0xff, 0x53, 0x71, 0x5e, 0x99, 0x55, 0x13, 0x56, 0xde, 0x2f, 0x44, 0x3e, 0x27, 0xaf, 0xa2, 0x05,
	0x19, 0x40, 0xc3, 0xb9, 0x2b, 0x15, 0xf3, 0x7d, 0x59, 0x35, 0x61, 0x15, 0x45, 0x0c, 0x0d, 0x85,
	0xd5, 0x16, 0x3c, 0xe3, 0x32, 0xbe, 0x9b, 0x3e, 0xe4, 0xb4, 0xf0, 0x90, 0xcb, 0x41, 0x5d, 0x05,
	0xdf, 0x13, 0xa5, 0xab, 0x04, 0x75, 0xab, 0xf8, 0xde, 0x28, 0xdd, 0xea, 0xd2, 0x35, 0x94, 0x96,
	0x52, 0xfa, 0x35, 0xfe, 0x5f, 0xc1, 0xd2, 0x29, 0x94, 0x0f, 0xde, 0xd8, 0xd5, 0x3c, 0x4a, 0x5d,
	0xb7, 0xf7, 0x3d, 0xe0, 0x01, 0x8b, 0xfc, 0xa8, 0x1e, 0x44, 0xd3, 0x37, 0x6a, 0x4e, 0xdf, 0xc6,
	0x49, 0xcf, 0x06, 0xbf, 0x9c, 0x4c, 0xfe, 0x40, 0x59, 0x3a, 0x8d, 0x0a, 0xa1, 0xeb, 0xf8, 0x30,
	0x07, 0xd3, 0xa2, 0x83, 0x33, 0x48, 0x0d, 0xdf, 0xb4, 0x87, 0x79, 0x28, 0x44, 0x7b, 0xd8, 0x1c,
	0xdd, 0x43, 0x66, 0x60, 0x10, 0xfe, 0xd5, 0x61, 0x98, 0x83, 0xd4, 0xe0, 0x20, 0x46, 0xf4, 0xa0,
	0x0e, 0x0e, 0x62, 0x44, 0x0f, 0x53, 0xa2, 0x87, 0x35, 0x54, 0x8c, 0xb8, 0xac, 0x0e, 0x73, 0x31,
	0x2b, 0xba, 0x58, 0x47, 0x07, 0xa3, 0xee, 0xa0, 0xc3, 0x7c, 0xe4, 0xa2, 0x73, 0xc9, 0x2f, 0x97,
	0xc3, 0x1c, 0x24, 0xef, 0x10, 0xc7, 0x88, 0xa9, 0x98, 0xb9, 0x53, 0x1c, 0x23, 0xfa, 0xc8, 0x47,
	0x3f, 0x10, 0xe1, 0x96, 0x37, 0xcc, 0x83, 0x32, 0xa0, 0x28, 0xf8, 0xfd, 0x6d, 0x98, 0x87, 0xb9,
	0xe8, 0x5c, 0xf2, 0xab, 0xd9, 0x30, 0x07, 0x69, 0xd1, 0xc1, 0x3e, 0x5a, 0x88, 0xbc, 0x71, 0x45,
	0x38, 0xf9, 0xa9, 0xe8, 0x24, 0xee, 0x6b, 0x59, 0x01, 0x7d, 0x13, 0xe1, 0x41, 0xf7, 0xae, 0x08,
	0xfa, 0x25, 0x91, 0x3e, 0xc1, 0xab, 0x5a, 0xe1, 0x0b, 0xf4, 0xd1, 0x37, 0x06, 0x5c, 0xbf, 0x22,
	0xf8, 0xe7, 0xe4, 0xe8, 0xc7, 0x7d, 0x77, 0x2b, 0x60, 0x7f, 0x8b, 0x96, 0x06, 0x5f, 0xbd, 0x22,
	0xc8, 0x3f, 0x91, 0x23, 0x8f, 0xf1, 0x36, 0x37, 0x54, 0x30, 0xf2, 0x05, 0x4c, 0x64, 0x4e, 0x0f,
	0x6b, 0xe7, 0x50, 0xb3, 0x81, 0xbb, 0x95, 0xe8, 0xa1, 0x30, 0x9a, 0x87, 0xcd, 0xc1, 0x1e, 0x32,
	0xa3, 0x8d, 0x14, 0xf9, 0x42, 0x24, 0x3a, 0x48, 0x8d, 0x1e, 0xc4, 0x00, 0x0f, 0xea, 0xe8, 0x41,
	0x0c, 0xf0, 0x30, 0x35, 0xcc, 0x03, 0x74, 0xb1, 0xe0, 0xf5, 0x44, 0x74, 0x31, 0x3b, 0x62, 0x18,
	0xf2, 0xbd, 0x43, 0xf4, 0x30, 0x37, 0xc4, 0xc3, 0x72, 0x09, 0xcd, 0xf1, 0x43, 0xe0, 0x1c, 0x9a,
	0x5e, 0xbb, 0x78, 0xb9, 0xba, 0x96, 0x4f, 0x90, 0x1f, 0xd7, 0xad, 0xb5, 0x5f, 0xfc, 0x3c, 0xaf,
	0xa8, 0xf3, 0x68, 0xf6, 0x6c, 0x75, 0xcd, 0xba, 0x78, 0x61, 0x23, 0x9f, 0x5c, 0xcf, 0xd0, 0xe3,
	0x6a, 0xbf, 0xd3, 0x72, 0x3b, 0x47, 0xcb, 0x68, 0x5e, 0x3c, 0x78, 0x45, 0x39, 0x40, 0x6a, 0x9a,
	0x3b, 0xb8, 0xad, 0xac, 0x5f, 0xfe, 0xe5, 0xcf, 0x42, 0xf5, 0xbb, 0xe2, 0xd5, 0x6f, 0xbd, 0xdf,
	0x58, 0x69, 0x75, 0x7a, 0xf6, 0x5e, 0xa7, 0xe6, 0x78, 0x7f, 0x3d, 0xe1, 0x59, 0xbb, 0x2b, 0x8e,
	0xdd, 0xac, 0x6d, 0xef, 0xaf, 0x0c, 0xfa, 0x43, 0x8b, 0xfa, 0x0c, 0x7c, 0xf2, 0x55, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xb9, 0x46, 0x32, 0x11, 0x8b, 0x21, 0x00, 0x00,
}
