// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto3_20180814_aa810b61/test.proto

package proto3_20180814_aa810b61 // import "google.golang.org/protobuf/internal/testprotos/legacy/proto3_20180814_aa810b61"

import proto "google.golang.org/protobuf/internal/protolegacy"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SiblingEnum int32

const (
	SiblingEnum_ALPHA   SiblingEnum = 0
	SiblingEnum_BRAVO   SiblingEnum = 10
	SiblingEnum_CHARLIE SiblingEnum = 200
)

var SiblingEnum_name = map[int32]string{
	0:   "ALPHA",
	10:  "BRAVO",
	200: "CHARLIE",
}
var SiblingEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   10,
	"CHARLIE": 200,
}

func (x SiblingEnum) String() string {
	return proto.EnumName(SiblingEnum_name, int32(x))
}
func (SiblingEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_test_14f9d28b9a7006c3, []int{0}
}

type Message_ChildEnum int32

const (
	Message_ALPHA   Message_ChildEnum = 0
	Message_BRAVO   Message_ChildEnum = 1
	Message_CHARLIE Message_ChildEnum = 2
)

var Message_ChildEnum_name = map[int32]string{
	0: "ALPHA",
	1: "BRAVO",
	2: "CHARLIE",
}
var Message_ChildEnum_value = map[string]int32{
	"ALPHA":   0,
	"BRAVO":   1,
	"CHARLIE": 2,
}

func (x Message_ChildEnum) String() string {
	return proto.EnumName(Message_ChildEnum_name, int32(x))
}
func (Message_ChildEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_test_14f9d28b9a7006c3, []int{1, 0}
}

type SiblingMessage struct {
	F1                   string   `protobuf:"bytes,1,opt,name=f1,proto3" json:"f1,omitempty"`
	F2                   []string `protobuf:"bytes,2,rep,name=f2,proto3" json:"f2,omitempty"`
	F3                   *Message `protobuf:"bytes,3,opt,name=f3,proto3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SiblingMessage) Reset()         { *m = SiblingMessage{} }
func (m *SiblingMessage) String() string { return proto.CompactTextString(m) }
func (*SiblingMessage) ProtoMessage()    {}
func (*SiblingMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_test_14f9d28b9a7006c3, []int{0}
}
func (m *SiblingMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SiblingMessage.Unmarshal(m, b)
}
func (m *SiblingMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SiblingMessage.Marshal(b, m, deterministic)
}
func (dst *SiblingMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SiblingMessage.Merge(dst, src)
}
func (m *SiblingMessage) XXX_Size() int {
	return xxx_messageInfo_SiblingMessage.Size(m)
}
func (m *SiblingMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_SiblingMessage.DiscardUnknown(m)
}

var xxx_messageInfo_SiblingMessage proto.InternalMessageInfo

func (m *SiblingMessage) GetF1() string {
	if m != nil {
		return m.F1
	}
	return ""
}

func (m *SiblingMessage) GetF2() []string {
	if m != nil {
		return m.F2
	}
	return nil
}

func (m *SiblingMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

type Message struct {
	// Optional fields.
	OptionalBool           bool                  `protobuf:"varint,100,opt,name=optional_bool,json=optionalBool,proto3" json:"optional_bool,omitempty"`
	OptionalInt32          int32                 `protobuf:"varint,101,opt,name=optional_int32,json=optionalInt32,proto3" json:"optional_int32,omitempty"`
	OptionalSint32         int32                 `protobuf:"zigzag32,102,opt,name=optional_sint32,json=optionalSint32,proto3" json:"optional_sint32,omitempty"`
	OptionalUint32         uint32                `protobuf:"varint,103,opt,name=optional_uint32,json=optionalUint32,proto3" json:"optional_uint32,omitempty"`
	OptionalInt64          int64                 `protobuf:"varint,104,opt,name=optional_int64,json=optionalInt64,proto3" json:"optional_int64,omitempty"`
	OptionalSint64         int64                 `protobuf:"zigzag64,105,opt,name=optional_sint64,json=optionalSint64,proto3" json:"optional_sint64,omitempty"`
	OptionalUint64         uint64                `protobuf:"varint,106,opt,name=optional_uint64,json=optionalUint64,proto3" json:"optional_uint64,omitempty"`
	OptionalFixed32        uint32                `protobuf:"fixed32,107,opt,name=optional_fixed32,json=optionalFixed32,proto3" json:"optional_fixed32,omitempty"`
	OptionalSfixed32       int32                 `protobuf:"fixed32,108,opt,name=optional_sfixed32,json=optionalSfixed32,proto3" json:"optional_sfixed32,omitempty"`
	OptionalFloat          float32               `protobuf:"fixed32,109,opt,name=optional_float,json=optionalFloat,proto3" json:"optional_float,omitempty"`
	OptionalFixed64        uint64                `protobuf:"fixed64,110,opt,name=optional_fixed64,json=optionalFixed64,proto3" json:"optional_fixed64,omitempty"`
	OptionalSfixed64       int64                 `protobuf:"fixed64,111,opt,name=optional_sfixed64,json=optionalSfixed64,proto3" json:"optional_sfixed64,omitempty"`
	OptionalDouble         float64               `protobuf:"fixed64,112,opt,name=optional_double,json=optionalDouble,proto3" json:"optional_double,omitempty"`
	OptionalString         string                `protobuf:"bytes,113,opt,name=optional_string,json=optionalString,proto3" json:"optional_string,omitempty"`
	OptionalBytes          []byte                `protobuf:"bytes,114,opt,name=optional_bytes,json=optionalBytes,proto3" json:"optional_bytes,omitempty"`
	OptionalChildEnum      Message_ChildEnum     `protobuf:"varint,115,opt,name=optional_child_enum,json=optionalChildEnum,proto3,enum=google.golang.org.proto3_20180814.Message_ChildEnum" json:"optional_child_enum,omitempty"`
	OptionalChildMessage   *Message_ChildMessage `protobuf:"bytes,116,opt,name=optional_child_message,json=optionalChildMessage,proto3" json:"optional_child_message,omitempty"`
	OptionalSiblingEnum    SiblingEnum           `protobuf:"varint,117,opt,name=optional_sibling_enum,json=optionalSiblingEnum,proto3,enum=google.golang.org.proto3_20180814.SiblingEnum" json:"optional_sibling_enum,omitempty"`
	OptionalSiblingMessage *SiblingMessage       `protobuf:"bytes,118,opt,name=optional_sibling_message,json=optionalSiblingMessage,proto3" json:"optional_sibling_message,omitempty"`
	// Repeated fields.
	RepeatedBool           []bool                  `protobuf:"varint,200,rep,packed,name=repeated_bool,json=repeatedBool,proto3" json:"repeated_bool,omitempty"`
	RepeatedInt32          []int32                 `protobuf:"varint,201,rep,packed,name=repeated_int32,json=repeatedInt32,proto3" json:"repeated_int32,omitempty"`
	RepeatedSint32         []int32                 `protobuf:"zigzag32,202,rep,packed,name=repeated_sint32,json=repeatedSint32,proto3" json:"repeated_sint32,omitempty"`
	RepeatedUint32         []uint32                `protobuf:"varint,203,rep,packed,name=repeated_uint32,json=repeatedUint32,proto3" json:"repeated_uint32,omitempty"`
	RepeatedInt64          []int64                 `protobuf:"varint,204,rep,packed,name=repeated_int64,json=repeatedInt64,proto3" json:"repeated_int64,omitempty"`
	RepeatedSint64         []int64                 `protobuf:"zigzag64,205,rep,packed,name=repeated_sint64,json=repeatedSint64,proto3" json:"repeated_sint64,omitempty"`
	RepeatedUint64         []uint64                `protobuf:"varint,206,rep,packed,name=repeated_uint64,json=repeatedUint64,proto3" json:"repeated_uint64,omitempty"`
	RepeatedFixed32        []uint32                `protobuf:"fixed32,207,rep,packed,name=repeated_fixed32,json=repeatedFixed32,proto3" json:"repeated_fixed32,omitempty"`
	RepeatedSfixed32       []int32                 `protobuf:"fixed32,208,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32,proto3" json:"repeated_sfixed32,omitempty"`
	RepeatedFloat          []float32               `protobuf:"fixed32,209,rep,packed,name=repeated_float,json=repeatedFloat,proto3" json:"repeated_float,omitempty"`
	RepeatedFixed64        []uint64                `protobuf:"fixed64,210,rep,packed,name=repeated_fixed64,json=repeatedFixed64,proto3" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed64       []int64                 `protobuf:"fixed64,211,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64,proto3" json:"repeated_sfixed64,omitempty"`
	RepeatedDouble         []float64               `protobuf:"fixed64,212,rep,packed,name=repeated_double,json=repeatedDouble,proto3" json:"repeated_double,omitempty"`
	RepeatedString         []string                `protobuf:"bytes,213,rep,name=repeated_string,json=repeatedString,proto3" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                `protobuf:"bytes,214,rep,name=repeated_bytes,json=repeatedBytes,proto3" json:"repeated_bytes,omitempty"`
	RepeatedChildEnum      []Message_ChildEnum     `protobuf:"varint,215,rep,packed,name=repeated_child_enum,json=repeatedChildEnum,proto3,enum=google.golang.org.proto3_20180814.Message_ChildEnum" json:"repeated_child_enum,omitempty"`
	RepeatedChildMessage   []*Message_ChildMessage `protobuf:"bytes,216,rep,name=repeated_child_message,json=repeatedChildMessage,proto3" json:"repeated_child_message,omitempty"`
	RepeatedSiblingEnum    []SiblingEnum           `protobuf:"varint,217,rep,packed,name=repeated_sibling_enum,json=repeatedSiblingEnum,proto3,enum=google.golang.org.proto3_20180814.SiblingEnum" json:"repeated_sibling_enum,omitempty"`
	RepeatedSiblingMessage []*SiblingMessage       `protobuf:"bytes,218,rep,name=repeated_sibling_message,json=repeatedSiblingMessage,proto3" json:"repeated_sibling_message,omitempty"`
	// Map fields.
	MapBoolBool           map[bool]bool                  `protobuf:"bytes,300,rep,name=map_bool_bool,json=mapBoolBool,proto3" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolInt32          map[bool]int32                 `protobuf:"bytes,301,rep,name=map_bool_int32,json=mapBoolInt32,proto3" json:"map_bool_int32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolSint32         map[bool]int32                 `protobuf:"bytes,302,rep,name=map_bool_sint32,json=mapBoolSint32,proto3" json:"map_bool_sint32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"zigzag32,2,opt,name=value,proto3"`
	MapBoolUint32         map[bool]uint32                `protobuf:"bytes,303,rep,name=map_bool_uint32,json=mapBoolUint32,proto3" json:"map_bool_uint32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolInt64          map[bool]int64                 `protobuf:"bytes,304,rep,name=map_bool_int64,json=mapBoolInt64,proto3" json:"map_bool_int64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolSint64         map[bool]int64                 `protobuf:"bytes,305,rep,name=map_bool_sint64,json=mapBoolSint64,proto3" json:"map_bool_sint64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"zigzag64,2,opt,name=value,proto3"`
	MapBoolUint64         map[bool]uint64                `protobuf:"bytes,306,rep,name=map_bool_uint64,json=mapBoolUint64,proto3" json:"map_bool_uint64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapBoolFixed32        map[bool]uint32                `protobuf:"bytes,307,rep,name=map_bool_fixed32,json=mapBoolFixed32,proto3" json:"map_bool_fixed32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolSfixed32       map[bool]int32                 `protobuf:"bytes,308,rep,name=map_bool_sfixed32,json=mapBoolSfixed32,proto3" json:"map_bool_sfixed32,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolFloat          map[bool]float32               `protobuf:"bytes,309,rep,name=map_bool_float,json=mapBoolFloat,proto3" json:"map_bool_float,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	MapBoolFixed64        map[bool]uint64                `protobuf:"bytes,310,rep,name=map_bool_fixed64,json=mapBoolFixed64,proto3" json:"map_bool_fixed64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolSfixed64       map[bool]int64                 `protobuf:"bytes,311,rep,name=map_bool_sfixed64,json=mapBoolSfixed64,proto3" json:"map_bool_sfixed64,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolDouble         map[bool]float64               `protobuf:"bytes,312,rep,name=map_bool_double,json=mapBoolDouble,proto3" json:"map_bool_double,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	MapBoolString         map[bool]string                `protobuf:"bytes,313,rep,name=map_bool_string,json=mapBoolString,proto3" json:"map_bool_string,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolBytes          map[bool][]byte                `protobuf:"bytes,314,rep,name=map_bool_bytes,json=mapBoolBytes,proto3" json:"map_bool_bytes,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolChildEnum      map[bool]Message_ChildEnum     `protobuf:"bytes,315,rep,name=map_bool_child_enum,json=mapBoolChildEnum,proto3" json:"map_bool_child_enum,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=google.golang.org.proto3_20180814.Message_ChildEnum"`
	MapBoolChildMessage   map[bool]*Message_ChildMessage `protobuf:"bytes,316,rep,name=map_bool_child_message,json=mapBoolChildMessage,proto3" json:"map_bool_child_message,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapBoolSiblingEnum    map[bool]SiblingEnum           `protobuf:"bytes,317,rep,name=map_bool_sibling_enum,json=mapBoolSiblingEnum,proto3" json:"map_bool_sibling_enum,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=google.golang.org.proto3_20180814.SiblingEnum"`
	MapBoolSiblingMessage map[bool]*SiblingMessage       `protobuf:"bytes,318,rep,name=map_bool_sibling_message,json=mapBoolSiblingMessage,proto3" json:"map_bool_sibling_message,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MapInt32Bool          map[int32]bool                 `protobuf:"bytes,319,rep,name=map_int32_bool,json=mapInt32Bool,proto3" json:"map_int32_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapSint32Bool         map[int32]bool                 `protobuf:"bytes,320,rep,name=map_sint32_bool,json=mapSint32Bool,proto3" json:"map_sint32_bool,omitempty" protobuf_key:"zigzag32,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint32Bool         map[uint32]bool                `protobuf:"bytes,321,rep,name=map_uint32_bool,json=mapUint32Bool,proto3" json:"map_uint32_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapInt64Bool          map[int64]bool                 `protobuf:"bytes,322,rep,name=map_int64_bool,json=mapInt64Bool,proto3" json:"map_int64_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapSint64Bool         map[int64]bool                 `protobuf:"bytes,323,rep,name=map_sint64_bool,json=mapSint64Bool,proto3" json:"map_sint64_bool,omitempty" protobuf_key:"zigzag64,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapUint64Bool         map[uint64]bool                `protobuf:"bytes,324,rep,name=map_uint64_bool,json=mapUint64Bool,proto3" json:"map_uint64_bool,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapFixed32Bool        map[uint32]bool                `protobuf:"bytes,325,rep,name=map_fixed32_bool,json=mapFixed32Bool,proto3" json:"map_fixed32_bool,omitempty" protobuf_key:"fixed32,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	MapStringBool         map[string]bool                `protobuf:"bytes,326,rep,name=map_string_bool,json=mapStringBool,proto3" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// Oneof fields.
	//
	// Types that are valid to be assigned to OneofUnion:
	//	*Message_OneofBool
	//	*Message_OneofInt32
	//	*Message_OneofSint32
	//	*Message_OneofUint32
	//	*Message_OneofInt64
	//	*Message_OneofSint64
	//	*Message_OneofUint64
	//	*Message_OneofFixed32
	//	*Message_OneofSfixed32
	//	*Message_OneofFloat
	//	*Message_OneofFixed64
	//	*Message_OneofSfixed64
	//	*Message_OneofDouble
	//	*Message_OneofString
	//	*Message_OneofBytes
	//	*Message_OneofChildEnum
	//	*Message_OneofChildMessage
	//	*Message_OneofSiblingEnum
	//	*Message_OneofSiblingMessage
	//	*Message_OneofString1
	//	*Message_OneofString2
	//	*Message_OneofString3
	OneofUnion           isMessage_OneofUnion `protobuf_oneof:"oneof_union"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Message) Reset()         { *m = Message{} }
func (m *Message) String() string { return proto.CompactTextString(m) }
func (*Message) ProtoMessage()    {}
func (*Message) Descriptor() ([]byte, []int) {
	return fileDescriptor_test_14f9d28b9a7006c3, []int{1}
}
func (m *Message) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message.Unmarshal(m, b)
}
func (m *Message) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message.Marshal(b, m, deterministic)
}
func (dst *Message) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message.Merge(dst, src)
}
func (m *Message) XXX_Size() int {
	return xxx_messageInfo_Message.Size(m)
}
func (m *Message) XXX_DiscardUnknown() {
	xxx_messageInfo_Message.DiscardUnknown(m)
}

var xxx_messageInfo_Message proto.InternalMessageInfo

func (m *Message) GetOptionalBool() bool {
	if m != nil {
		return m.OptionalBool
	}
	return false
}

func (m *Message) GetOptionalInt32() int32 {
	if m != nil {
		return m.OptionalInt32
	}
	return 0
}

func (m *Message) GetOptionalSint32() int32 {
	if m != nil {
		return m.OptionalSint32
	}
	return 0
}

func (m *Message) GetOptionalUint32() uint32 {
	if m != nil {
		return m.OptionalUint32
	}
	return 0
}

func (m *Message) GetOptionalInt64() int64 {
	if m != nil {
		return m.OptionalInt64
	}
	return 0
}

func (m *Message) GetOptionalSint64() int64 {
	if m != nil {
		return m.OptionalSint64
	}
	return 0
}

func (m *Message) GetOptionalUint64() uint64 {
	if m != nil {
		return m.OptionalUint64
	}
	return 0
}

func (m *Message) GetOptionalFixed32() uint32 {
	if m != nil {
		return m.OptionalFixed32
	}
	return 0
}

func (m *Message) GetOptionalSfixed32() int32 {
	if m != nil {
		return m.OptionalSfixed32
	}
	return 0
}

func (m *Message) GetOptionalFloat() float32 {
	if m != nil {
		return m.OptionalFloat
	}
	return 0
}

func (m *Message) GetOptionalFixed64() uint64 {
	if m != nil {
		return m.OptionalFixed64
	}
	return 0
}

func (m *Message) GetOptionalSfixed64() int64 {
	if m != nil {
		return m.OptionalSfixed64
	}
	return 0
}

func (m *Message) GetOptionalDouble() float64 {
	if m != nil {
		return m.OptionalDouble
	}
	return 0
}

func (m *Message) GetOptionalString() string {
	if m != nil {
		return m.OptionalString
	}
	return ""
}

func (m *Message) GetOptionalBytes() []byte {
	if m != nil {
		return m.OptionalBytes
	}
	return nil
}

func (m *Message) GetOptionalChildEnum() Message_ChildEnum {
	if m != nil {
		return m.OptionalChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOptionalChildMessage() *Message_ChildMessage {
	if m != nil {
		return m.OptionalChildMessage
	}
	return nil
}

func (m *Message) GetOptionalSiblingEnum() SiblingEnum {
	if m != nil {
		return m.OptionalSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOptionalSiblingMessage() *SiblingMessage {
	if m != nil {
		return m.OptionalSiblingMessage
	}
	return nil
}

func (m *Message) GetRepeatedBool() []bool {
	if m != nil {
		return m.RepeatedBool
	}
	return nil
}

func (m *Message) GetRepeatedInt32() []int32 {
	if m != nil {
		return m.RepeatedInt32
	}
	return nil
}

func (m *Message) GetRepeatedSint32() []int32 {
	if m != nil {
		return m.RepeatedSint32
	}
	return nil
}

func (m *Message) GetRepeatedUint32() []uint32 {
	if m != nil {
		return m.RepeatedUint32
	}
	return nil
}

func (m *Message) GetRepeatedInt64() []int64 {
	if m != nil {
		return m.RepeatedInt64
	}
	return nil
}

func (m *Message) GetRepeatedSint64() []int64 {
	if m != nil {
		return m.RepeatedSint64
	}
	return nil
}

func (m *Message) GetRepeatedUint64() []uint64 {
	if m != nil {
		return m.RepeatedUint64
	}
	return nil
}

func (m *Message) GetRepeatedFixed32() []uint32 {
	if m != nil {
		return m.RepeatedFixed32
	}
	return nil
}

func (m *Message) GetRepeatedSfixed32() []int32 {
	if m != nil {
		return m.RepeatedSfixed32
	}
	return nil
}

func (m *Message) GetRepeatedFloat() []float32 {
	if m != nil {
		return m.RepeatedFloat
	}
	return nil
}

func (m *Message) GetRepeatedFixed64() []uint64 {
	if m != nil {
		return m.RepeatedFixed64
	}
	return nil
}

func (m *Message) GetRepeatedSfixed64() []int64 {
	if m != nil {
		return m.RepeatedSfixed64
	}
	return nil
}

func (m *Message) GetRepeatedDouble() []float64 {
	if m != nil {
		return m.RepeatedDouble
	}
	return nil
}

func (m *Message) GetRepeatedString() []string {
	if m != nil {
		return m.RepeatedString
	}
	return nil
}

func (m *Message) GetRepeatedBytes() [][]byte {
	if m != nil {
		return m.RepeatedBytes
	}
	return nil
}

func (m *Message) GetRepeatedChildEnum() []Message_ChildEnum {
	if m != nil {
		return m.RepeatedChildEnum
	}
	return nil
}

func (m *Message) GetRepeatedChildMessage() []*Message_ChildMessage {
	if m != nil {
		return m.RepeatedChildMessage
	}
	return nil
}

func (m *Message) GetRepeatedSiblingEnum() []SiblingEnum {
	if m != nil {
		return m.RepeatedSiblingEnum
	}
	return nil
}

func (m *Message) GetRepeatedSiblingMessage() []*SiblingMessage {
	if m != nil {
		return m.RepeatedSiblingMessage
	}
	return nil
}

func (m *Message) GetMapBoolBool() map[bool]bool {
	if m != nil {
		return m.MapBoolBool
	}
	return nil
}

func (m *Message) GetMapBoolInt32() map[bool]int32 {
	if m != nil {
		return m.MapBoolInt32
	}
	return nil
}

func (m *Message) GetMapBoolSint32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSint32
	}
	return nil
}

func (m *Message) GetMapBoolUint32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolUint32
	}
	return nil
}

func (m *Message) GetMapBoolInt64() map[bool]int64 {
	if m != nil {
		return m.MapBoolInt64
	}
	return nil
}

func (m *Message) GetMapBoolSint64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSint64
	}
	return nil
}

func (m *Message) GetMapBoolUint64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolUint64
	}
	return nil
}

func (m *Message) GetMapBoolFixed32() map[bool]uint32 {
	if m != nil {
		return m.MapBoolFixed32
	}
	return nil
}

func (m *Message) GetMapBoolSfixed32() map[bool]int32 {
	if m != nil {
		return m.MapBoolSfixed32
	}
	return nil
}

func (m *Message) GetMapBoolFloat() map[bool]float32 {
	if m != nil {
		return m.MapBoolFloat
	}
	return nil
}

func (m *Message) GetMapBoolFixed64() map[bool]uint64 {
	if m != nil {
		return m.MapBoolFixed64
	}
	return nil
}

func (m *Message) GetMapBoolSfixed64() map[bool]int64 {
	if m != nil {
		return m.MapBoolSfixed64
	}
	return nil
}

func (m *Message) GetMapBoolDouble() map[bool]float64 {
	if m != nil {
		return m.MapBoolDouble
	}
	return nil
}

func (m *Message) GetMapBoolString() map[bool]string {
	if m != nil {
		return m.MapBoolString
	}
	return nil
}

func (m *Message) GetMapBoolBytes() map[bool][]byte {
	if m != nil {
		return m.MapBoolBytes
	}
	return nil
}

func (m *Message) GetMapBoolChildEnum() map[bool]Message_ChildEnum {
	if m != nil {
		return m.MapBoolChildEnum
	}
	return nil
}

func (m *Message) GetMapBoolChildMessage() map[bool]*Message_ChildMessage {
	if m != nil {
		return m.MapBoolChildMessage
	}
	return nil
}

func (m *Message) GetMapBoolSiblingEnum() map[bool]SiblingEnum {
	if m != nil {
		return m.MapBoolSiblingEnum
	}
	return nil
}

func (m *Message) GetMapBoolSiblingMessage() map[bool]*SiblingMessage {
	if m != nil {
		return m.MapBoolSiblingMessage
	}
	return nil
}

func (m *Message) GetMapInt32Bool() map[int32]bool {
	if m != nil {
		return m.MapInt32Bool
	}
	return nil
}

func (m *Message) GetMapSint32Bool() map[int32]bool {
	if m != nil {
		return m.MapSint32Bool
	}
	return nil
}

func (m *Message) GetMapUint32Bool() map[uint32]bool {
	if m != nil {
		return m.MapUint32Bool
	}
	return nil
}

func (m *Message) GetMapInt64Bool() map[int64]bool {
	if m != nil {
		return m.MapInt64Bool
	}
	return nil
}

func (m *Message) GetMapSint64Bool() map[int64]bool {
	if m != nil {
		return m.MapSint64Bool
	}
	return nil
}

func (m *Message) GetMapUint64Bool() map[uint64]bool {
	if m != nil {
		return m.MapUint64Bool
	}
	return nil
}

func (m *Message) GetMapFixed32Bool() map[uint32]bool {
	if m != nil {
		return m.MapFixed32Bool
	}
	return nil
}

func (m *Message) GetMapStringBool() map[string]bool {
	if m != nil {
		return m.MapStringBool
	}
	return nil
}

type isMessage_OneofUnion interface {
	isMessage_OneofUnion()
}

type Message_OneofBool struct {
	OneofBool bool `protobuf:"varint,400,opt,name=oneof_bool,json=oneofBool,proto3,oneof"`
}

type Message_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,401,opt,name=oneof_int32,json=oneofInt32,proto3,oneof"`
}

type Message_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,402,opt,name=oneof_sint32,json=oneofSint32,proto3,oneof"`
}

type Message_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,403,opt,name=oneof_uint32,json=oneofUint32,proto3,oneof"`
}

type Message_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,404,opt,name=oneof_int64,json=oneofInt64,proto3,oneof"`
}

type Message_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,405,opt,name=oneof_sint64,json=oneofSint64,proto3,oneof"`
}

type Message_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,406,opt,name=oneof_uint64,json=oneofUint64,proto3,oneof"`
}

type Message_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,407,opt,name=oneof_fixed32,json=oneofFixed32,proto3,oneof"`
}

type Message_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,408,opt,name=oneof_sfixed32,json=oneofSfixed32,proto3,oneof"`
}

type Message_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,409,opt,name=oneof_float,json=oneofFloat,proto3,oneof"`
}

type Message_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,410,opt,name=oneof_fixed64,json=oneofFixed64,proto3,oneof"`
}

type Message_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,411,opt,name=oneof_sfixed64,json=oneofSfixed64,proto3,oneof"`
}

type Message_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,412,opt,name=oneof_double,json=oneofDouble,proto3,oneof"`
}

type Message_OneofString struct {
	OneofString string `protobuf:"bytes,413,opt,name=oneof_string,json=oneofString,proto3,oneof"`
}

type Message_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,414,opt,name=oneof_bytes,json=oneofBytes,proto3,oneof"`
}

type Message_OneofChildEnum struct {
	OneofChildEnum Message_ChildEnum `protobuf:"varint,415,opt,name=oneof_child_enum,json=oneofChildEnum,proto3,enum=google.golang.org.proto3_20180814.Message_ChildEnum,oneof"`
}

type Message_OneofChildMessage struct {
	OneofChildMessage *Message_ChildMessage `protobuf:"bytes,416,opt,name=oneof_child_message,json=oneofChildMessage,proto3,oneof"`
}

type Message_OneofSiblingEnum struct {
	OneofSiblingEnum SiblingEnum `protobuf:"varint,417,opt,name=oneof_sibling_enum,json=oneofSiblingEnum,proto3,enum=google.golang.org.proto3_20180814.SiblingEnum,oneof"`
}

type Message_OneofSiblingMessage struct {
	OneofSiblingMessage *SiblingMessage `protobuf:"bytes,418,opt,name=oneof_sibling_message,json=oneofSiblingMessage,proto3,oneof"`
}

type Message_OneofString1 struct {
	OneofString1 string `protobuf:"bytes,419,opt,name=oneof_string1,json=oneofString1,proto3,oneof"`
}

type Message_OneofString2 struct {
	OneofString2 string `protobuf:"bytes,420,opt,name=oneof_string2,json=oneofString2,proto3,oneof"`
}

type Message_OneofString3 struct {
	OneofString3 string `protobuf:"bytes,421,opt,name=oneof_string3,json=oneofString3,proto3,oneof"`
}

func (*Message_OneofBool) isMessage_OneofUnion() {}

func (*Message_OneofInt32) isMessage_OneofUnion() {}

func (*Message_OneofSint32) isMessage_OneofUnion() {}

func (*Message_OneofUint32) isMessage_OneofUnion() {}

func (*Message_OneofInt64) isMessage_OneofUnion() {}

func (*Message_OneofSint64) isMessage_OneofUnion() {}

func (*Message_OneofUint64) isMessage_OneofUnion() {}

func (*Message_OneofFixed32) isMessage_OneofUnion() {}

func (*Message_OneofSfixed32) isMessage_OneofUnion() {}

func (*Message_OneofFloat) isMessage_OneofUnion() {}

func (*Message_OneofFixed64) isMessage_OneofUnion() {}

func (*Message_OneofSfixed64) isMessage_OneofUnion() {}

func (*Message_OneofDouble) isMessage_OneofUnion() {}

func (*Message_OneofString) isMessage_OneofUnion() {}

func (*Message_OneofBytes) isMessage_OneofUnion() {}

func (*Message_OneofChildEnum) isMessage_OneofUnion() {}

func (*Message_OneofChildMessage) isMessage_OneofUnion() {}

func (*Message_OneofSiblingEnum) isMessage_OneofUnion() {}

func (*Message_OneofSiblingMessage) isMessage_OneofUnion() {}

func (*Message_OneofString1) isMessage_OneofUnion() {}

func (*Message_OneofString2) isMessage_OneofUnion() {}

func (*Message_OneofString3) isMessage_OneofUnion() {}

func (m *Message) GetOneofUnion() isMessage_OneofUnion {
	if m != nil {
		return m.OneofUnion
	}
	return nil
}

func (m *Message) GetOneofBool() bool {
	if x, ok := m.GetOneofUnion().(*Message_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (m *Message) GetOneofInt32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (m *Message) GetOneofSint32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (m *Message) GetOneofUint32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (m *Message) GetOneofInt64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (m *Message) GetOneofSint64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (m *Message) GetOneofUint64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (m *Message) GetOneofFixed32() uint32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (m *Message) GetOneofSfixed32() int32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (m *Message) GetOneofFloat() float32 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (m *Message) GetOneofFixed64() uint64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (m *Message) GetOneofSfixed64() int64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (m *Message) GetOneofDouble() float64 {
	if x, ok := m.GetOneofUnion().(*Message_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (m *Message) GetOneofString() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (m *Message) GetOneofBytes() []byte {
	if x, ok := m.GetOneofUnion().(*Message_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (m *Message) GetOneofChildEnum() Message_ChildEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildEnum); ok {
		return x.OneofChildEnum
	}
	return Message_ALPHA
}

func (m *Message) GetOneofChildMessage() *Message_ChildMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofChildMessage); ok {
		return x.OneofChildMessage
	}
	return nil
}

func (m *Message) GetOneofSiblingEnum() SiblingEnum {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingEnum); ok {
		return x.OneofSiblingEnum
	}
	return SiblingEnum_ALPHA
}

func (m *Message) GetOneofSiblingMessage() *SiblingMessage {
	if x, ok := m.GetOneofUnion().(*Message_OneofSiblingMessage); ok {
		return x.OneofSiblingMessage
	}
	return nil
}

func (m *Message) GetOneofString1() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString1); ok {
		return x.OneofString1
	}
	return ""
}

func (m *Message) GetOneofString2() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString2); ok {
		return x.OneofString2
	}
	return ""
}

func (m *Message) GetOneofString3() string {
	if x, ok := m.GetOneofUnion().(*Message_OneofString3); ok {
		return x.OneofString3
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*Message) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _Message_OneofMarshaler, _Message_OneofUnmarshaler, _Message_OneofSizer, []interface{}{
		(*Message_OneofBool)(nil),
		(*Message_OneofInt32)(nil),
		(*Message_OneofSint32)(nil),
		(*Message_OneofUint32)(nil),
		(*Message_OneofInt64)(nil),
		(*Message_OneofSint64)(nil),
		(*Message_OneofUint64)(nil),
		(*Message_OneofFixed32)(nil),
		(*Message_OneofSfixed32)(nil),
		(*Message_OneofFloat)(nil),
		(*Message_OneofFixed64)(nil),
		(*Message_OneofSfixed64)(nil),
		(*Message_OneofDouble)(nil),
		(*Message_OneofString)(nil),
		(*Message_OneofBytes)(nil),
		(*Message_OneofChildEnum)(nil),
		(*Message_OneofChildMessage)(nil),
		(*Message_OneofSiblingEnum)(nil),
		(*Message_OneofSiblingMessage)(nil),
		(*Message_OneofString1)(nil),
		(*Message_OneofString2)(nil),
		(*Message_OneofString3)(nil),
	}
}

func _Message_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		t := uint64(0)
		if x.OneofBool {
			t = 1
		}
		b.EncodeVarint(400<<3 | proto.WireVarint)
		b.EncodeVarint(t)
	case *Message_OneofInt32:
		b.EncodeVarint(401<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		b.EncodeVarint(402<<3 | proto.WireVarint)
		b.EncodeZigzag32(uint64(x.OneofSint32))
	case *Message_OneofUint32:
		b.EncodeVarint(403<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		b.EncodeVarint(404<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		b.EncodeVarint(405<<3 | proto.WireVarint)
		b.EncodeZigzag64(uint64(x.OneofSint64))
	case *Message_OneofUint64:
		b.EncodeVarint(406<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		b.EncodeVarint(407<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofFixed32))
	case *Message_OneofSfixed32:
		b.EncodeVarint(408<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(x.OneofSfixed32))
	case *Message_OneofFloat:
		b.EncodeVarint(409<<3 | proto.WireFixed32)
		b.EncodeFixed32(uint64(math.Float32bits(x.OneofFloat)))
	case *Message_OneofFixed64:
		b.EncodeVarint(410<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofFixed64))
	case *Message_OneofSfixed64:
		b.EncodeVarint(411<<3 | proto.WireFixed64)
		b.EncodeFixed64(uint64(x.OneofSfixed64))
	case *Message_OneofDouble:
		b.EncodeVarint(412<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.OneofDouble))
	case *Message_OneofString:
		b.EncodeVarint(413<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString)
	case *Message_OneofBytes:
		b.EncodeVarint(414<<3 | proto.WireBytes)
		b.EncodeRawBytes(x.OneofBytes)
	case *Message_OneofChildEnum:
		b.EncodeVarint(415<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		b.EncodeVarint(416<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofChildMessage); err != nil {
			return err
		}
	case *Message_OneofSiblingEnum:
		b.EncodeVarint(417<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		b.EncodeVarint(418<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.OneofSiblingMessage); err != nil {
			return err
		}
	case *Message_OneofString1:
		b.EncodeVarint(419<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString1)
	case *Message_OneofString2:
		b.EncodeVarint(420<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString2)
	case *Message_OneofString3:
		b.EncodeVarint(421<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.OneofString3)
	case nil:
	default:
		return fmt.Errorf("Message.OneofUnion has unexpected type %T", x)
	}
	return nil
}

func _Message_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*Message)
	switch tag {
	case 400: // oneof_union.oneof_bool
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofBool{x != 0}
		return true, err
	case 401: // oneof_union.oneof_int32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt32{int32(x)}
		return true, err
	case 402: // oneof_union.oneof_sint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag32()
		m.OneofUnion = &Message_OneofSint32{int32(x)}
		return true, err
	case 403: // oneof_union.oneof_uint32
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint32{uint32(x)}
		return true, err
	case 404: // oneof_union.oneof_int64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofInt64{int64(x)}
		return true, err
	case 405: // oneof_union.oneof_sint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeZigzag64()
		m.OneofUnion = &Message_OneofSint64{int64(x)}
		return true, err
	case 406: // oneof_union.oneof_uint64
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofUint64{x}
		return true, err
	case 407: // oneof_union.oneof_fixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFixed32{uint32(x)}
		return true, err
	case 408: // oneof_union.oneof_sfixed32
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofSfixed32{int32(x)}
		return true, err
	case 409: // oneof_union.oneof_float
		if wire != proto.WireFixed32 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed32()
		m.OneofUnion = &Message_OneofFloat{math.Float32frombits(uint32(x))}
		return true, err
	case 410: // oneof_union.oneof_fixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofFixed64{x}
		return true, err
	case 411: // oneof_union.oneof_sfixed64
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofSfixed64{int64(x)}
		return true, err
	case 412: // oneof_union.oneof_double
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.OneofUnion = &Message_OneofDouble{math.Float64frombits(x)}
		return true, err
	case 413: // oneof_union.oneof_string
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString{x}
		return true, err
	case 414: // oneof_union.oneof_bytes
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeRawBytes(true)
		m.OneofUnion = &Message_OneofBytes{x}
		return true, err
	case 415: // oneof_union.oneof_child_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofChildEnum{Message_ChildEnum(x)}
		return true, err
	case 416: // oneof_union.oneof_child_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Message_ChildMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofChildMessage{msg}
		return true, err
	case 417: // oneof_union.oneof_sibling_enum
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.OneofUnion = &Message_OneofSiblingEnum{SiblingEnum(x)}
		return true, err
	case 418: // oneof_union.oneof_sibling_message
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SiblingMessage)
		err := b.DecodeMessage(msg)
		m.OneofUnion = &Message_OneofSiblingMessage{msg}
		return true, err
	case 419: // oneof_union.oneof_string1
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString1{x}
		return true, err
	case 420: // oneof_union.oneof_string2
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString2{x}
		return true, err
	case 421: // oneof_union.oneof_string3
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.OneofUnion = &Message_OneofString3{x}
		return true, err
	default:
		return false, nil
	}
}

func _Message_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*Message)
	// oneof_union
	switch x := m.OneofUnion.(type) {
	case *Message_OneofBool:
		n += 2 // tag and wire
		n += 1
	case *Message_OneofInt32:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofInt32))
	case *Message_OneofSint32:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64((uint32(x.OneofSint32) << 1) ^ uint32((int32(x.OneofSint32) >> 31))))
	case *Message_OneofUint32:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofUint32))
	case *Message_OneofInt64:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofInt64))
	case *Message_OneofSint64:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(uint64(x.OneofSint64<<1) ^ uint64((int64(x.OneofSint64) >> 63))))
	case *Message_OneofUint64:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofUint64))
	case *Message_OneofFixed32:
		n += 2 // tag and wire
		n += 4
	case *Message_OneofSfixed32:
		n += 2 // tag and wire
		n += 4
	case *Message_OneofFloat:
		n += 2 // tag and wire
		n += 4
	case *Message_OneofFixed64:
		n += 2 // tag and wire
		n += 8
	case *Message_OneofSfixed64:
		n += 2 // tag and wire
		n += 8
	case *Message_OneofDouble:
		n += 2 // tag and wire
		n += 8
	case *Message_OneofString:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(len(x.OneofString)))
		n += len(x.OneofString)
	case *Message_OneofBytes:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(len(x.OneofBytes)))
		n += len(x.OneofBytes)
	case *Message_OneofChildEnum:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofChildEnum))
	case *Message_OneofChildMessage:
		s := proto.Size(x.OneofChildMessage)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofSiblingEnum:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(x.OneofSiblingEnum))
	case *Message_OneofSiblingMessage:
		s := proto.Size(x.OneofSiblingMessage)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *Message_OneofString1:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(len(x.OneofString1)))
		n += len(x.OneofString1)
	case *Message_OneofString2:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(len(x.OneofString2)))
		n += len(x.OneofString2)
	case *Message_OneofString3:
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(len(x.OneofString3)))
		n += len(x.OneofString3)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type Message_ChildMessage struct {
	F1                   string   `protobuf:"bytes,1,opt,name=f1,proto3" json:"f1,omitempty"`
	F2                   []string `protobuf:"bytes,2,rep,name=f2,proto3" json:"f2,omitempty"`
	F3                   *Message `protobuf:"bytes,3,opt,name=f3,proto3" json:"f3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Message_ChildMessage) Reset()         { *m = Message_ChildMessage{} }
func (m *Message_ChildMessage) String() string { return proto.CompactTextString(m) }
func (*Message_ChildMessage) ProtoMessage()    {}
func (*Message_ChildMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_test_14f9d28b9a7006c3, []int{1, 0}
}
func (m *Message_ChildMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Message_ChildMessage.Unmarshal(m, b)
}
func (m *Message_ChildMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Message_ChildMessage.Marshal(b, m, deterministic)
}
func (dst *Message_ChildMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Message_ChildMessage.Merge(dst, src)
}
func (m *Message_ChildMessage) XXX_Size() int {
	return xxx_messageInfo_Message_ChildMessage.Size(m)
}
func (m *Message_ChildMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_Message_ChildMessage.DiscardUnknown(m)
}

var xxx_messageInfo_Message_ChildMessage proto.InternalMessageInfo

func (m *Message_ChildMessage) GetF1() string {
	if m != nil {
		return m.F1
	}
	return ""
}

func (m *Message_ChildMessage) GetF2() []string {
	if m != nil {
		return m.F2
	}
	return nil
}

func (m *Message_ChildMessage) GetF3() *Message {
	if m != nil {
		return m.F3
	}
	return nil
}

func init() {
	proto.RegisterType((*SiblingMessage)(nil), "google.golang.org.proto3_20180814.SiblingMessage")
	proto.RegisterType((*Message)(nil), "google.golang.org.proto3_20180814.Message")
	proto.RegisterMapType((map[bool]bool)(nil), "google.golang.org.proto3_20180814.Message.MapBoolBoolEntry")
	proto.RegisterMapType((map[bool][]byte)(nil), "google.golang.org.proto3_20180814.Message.MapBoolBytesEntry")
	proto.RegisterMapType((map[bool]Message_ChildEnum)(nil), "google.golang.org.proto3_20180814.Message.MapBoolChildEnumEntry")
	proto.RegisterMapType((map[bool]*Message_ChildMessage)(nil), "google.golang.org.proto3_20180814.Message.MapBoolChildMessageEntry")
	proto.RegisterMapType((map[bool]float64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolDoubleEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolFixed32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolFixed64Entry")
	proto.RegisterMapType((map[bool]float32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolFloatEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolInt32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolInt64Entry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSfixed32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSfixed64Entry")
	proto.RegisterMapType((map[bool]SiblingEnum)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSiblingEnumEntry")
	proto.RegisterMapType((map[bool]*SiblingMessage)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSiblingMessageEntry")
	proto.RegisterMapType((map[bool]int32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSint32Entry")
	proto.RegisterMapType((map[bool]int64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolSint64Entry")
	proto.RegisterMapType((map[bool]string)(nil), "google.golang.org.proto3_20180814.Message.MapBoolStringEntry")
	proto.RegisterMapType((map[bool]uint32)(nil), "google.golang.org.proto3_20180814.Message.MapBoolUint32Entry")
	proto.RegisterMapType((map[bool]uint64)(nil), "google.golang.org.proto3_20180814.Message.MapBoolUint64Entry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto3_20180814.Message.MapFixed32BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto3_20180814.Message.MapInt32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto3_20180814.Message.MapInt64BoolEntry")
	proto.RegisterMapType((map[int32]bool)(nil), "google.golang.org.proto3_20180814.Message.MapSint32BoolEntry")
	proto.RegisterMapType((map[int64]bool)(nil), "google.golang.org.proto3_20180814.Message.MapSint64BoolEntry")
	proto.RegisterMapType((map[string]bool)(nil), "google.golang.org.proto3_20180814.Message.MapStringBoolEntry")
	proto.RegisterMapType((map[uint32]bool)(nil), "google.golang.org.proto3_20180814.Message.MapUint32BoolEntry")
	proto.RegisterMapType((map[uint64]bool)(nil), "google.golang.org.proto3_20180814.Message.MapUint64BoolEntry")
	proto.RegisterType((*Message_ChildMessage)(nil), "google.golang.org.proto3_20180814.Message.ChildMessage")
	proto.RegisterEnum("google.golang.org.proto3_20180814.SiblingEnum", SiblingEnum_name, SiblingEnum_value)
	proto.RegisterEnum("google.golang.org.proto3_20180814.Message_ChildEnum", Message_ChildEnum_name, Message_ChildEnum_value)
}

func init() {
	proto.RegisterFile("proto3_20180814_aa810b61/test.proto", fileDescriptor_test_14f9d28b9a7006c3)
}

var fileDescriptor_test_14f9d28b9a7006c3 = []byte{
	// 1946 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x9a, 0x57, 0x73, 0xdb, 0xca,
	0x15, 0xc7, 0x09, 0x52, 0xc5, 0x5a, 0xb1, 0x82, 0x96, 0xb2, 0xa3, 0x27, 0x44, 0x76, 0x1c, 0xc4,
	0xc9, 0x50, 0x26, 0x85, 0x41, 0x34, 0x4e, 0x62, 0x5b, 0xb2, 0xe5, 0xd0, 0x19, 0x3b, 0xf1, 0xc0,
	0xa3, 0x3c, 0xe4, 0x45, 0x21, 0x25, 0x90, 0xa6, 0x0d, 0x12, 0x8a, 0x48, 0x7a, 0xa2, 0xc9, 0x83,
	0xbf, 0x42, 0x7a, 0x6f, 0xb7, 0xbd, 0xdd, 0xde, 0x7b, 0xf7, 0x1d, 0xdf, 0xde, 0xeb, 0xa7, 0xb9,
	0xb3, 0x38, 0xd8, 0x06, 0x80, 0x26, 0x09, 0xce, 0x7d, 0xf0, 0x8c, 0x74, 0xf8, 0xdf, 0xf3, 0xe3,
	0x39, 0x38, 0x7b, 0xce, 0x2e, 0x2c, 0x74, 0x64, 0x6f, 0xdf, 0xed, 0xb9, 0xab, 0xdb, 0x95, 0x13,
	0xe5, 0xb5, 0x13, 0x6b, 0x65, 0x63, 0xbb, 0x56, 0x5b, 0x2b, 0x9f, 0xa8, 0x9b, 0xe5, 0x95, 0x9e,
	0xdd, 0xed, 0x95, 0xbc, 0x4f, 0xd5, 0x6f, 0x37, 0x5d, 0xb7, 0xe9, 0xd8, 0xa5, 0xa6, 0xeb, 0xd4,
	0x3a, 0xcd, 0x92, 0xbb, 0xdf, 0x2c, 0x05, 0x96, 0x2d, 0x3b, 0x28, 0x7b, 0xa5, 0x55, 0x77, 0x5a,
	0x9d, 0xe6, 0x25, 0xbb, 0xdb, 0xad, 0x35, 0x6d, 0x35, 0x8b, 0x92, 0x8d, 0x32, 0x56, 0x34, 0x45,
	0x9f, 0xb3, 0x92, 0x8d, 0xb2, 0xf7, 0x7b, 0x05, 0x27, 0xb5, 0x94, 0xf7, 0x7b, 0x45, 0x3d, 0x89,
	0x92, 0x8d, 0x55, 0x9c, 0xd2, 0x14, 0x7d, 0xbe, 0x72, 0xbc, 0x34, 0x94, 0x50, 0xf2, 0xfd, 0x5a,
	0xc9, 0xc6, 0xea, 0xf2, 0xad, 0x33, 0x68, 0x96, 0x72, 0x8e, 0xa0, 0x8c, 0xbb, 0xd7, 0x6b, 0xb9,
	0x9d, 0x9a, 0xb3, 0x5d, 0x77, 0x5d, 0x07, 0xef, 0x6a, 0x8a, 0x7e, 0xc8, 0x4a, 0x53, 0xe3, 0x86,
	0xeb, 0x3a, 0xea, 0x77, 0x50, 0x96, 0x89, 0x5a, 0x9d, 0xde, 0x6a, 0x05, 0xdb, 0x9a, 0xa2, 0x4f,
	0x5b, 0x6c, 0xe9, 0x05, 0x62, 0x54, 0xbf, 0x8b, 0x72, 0x4c, 0xd6, 0x05, 0x5d, 0x43, 0x53, 0xf4,
	0x82, 0xc5, 0x56, 0x5f, 0x69, 0x85, 0x84, 0x7d, 0x10, 0x36, 0x35, 0x45, 0xcf, 0x70, 0xe1, 0x16,
	0x08, 0x03, 0x60, 0xd3, 0xc0, 0x57, 0x35, 0x45, 0x4f, 0x49, 0x60, 0xd3, 0x08, 0x81, 0x4d, 0x03,
	0xb7, 0x34, 0x45, 0x57, 0x65, 0x70, 0x40, 0xd8, 0x07, 0xe1, 0x35, 0x4d, 0xd1, 0xa7, 0x64, 0xb0,
	0x69, 0xa8, 0xdf, 0x43, 0x79, 0x26, 0x6c, 0xb4, 0x7e, 0x6b, 0xef, 0xae, 0x56, 0xf0, 0x75, 0x4d,
	0xd1, 0x67, 0x2d, 0xe6, 0xe0, 0x3c, 0x98, 0xd5, 0xef, 0xa3, 0x02, 0x87, 0x53, 0xad, 0xa3, 0x29,
	0x7a, 0xce, 0x62, 0x3e, 0xae, 0xf8, 0x76, 0x29, 0xa0, 0x86, 0xe3, 0xd6, 0x7a, 0xb8, 0xad, 0x29,
	0x7a, 0x92, 0x07, 0x74, 0x9e, 0x18, 0xc3, 0x78, 0xd3, 0xc0, 0x1d, 0x4d, 0xd1, 0x67, 0x02, 0x78,
	0xd3, 0x88, 0xc0, 0x9b, 0x06, 0x76, 0x35, 0x45, 0xcf, 0x07, 0xf1, 0x81, 0xf8, 0x77, 0xdd, 0x7e,
	0xdd, 0xb1, 0xf1, 0x9e, 0xa6, 0xe8, 0x0a, 0x8f, 0xff, 0x9c, 0x67, 0x95, 0x33, 0xda, 0xdb, 0x6f,
	0x75, 0x9a, 0xf8, 0x37, 0x5e, 0x2d, 0xf2, 0x8c, 0x7a, 0x56, 0x29, 0xa0, 0xfa, 0x41, 0xcf, 0xee,
	0xe2, 0x7d, 0x4d, 0xd1, 0xd3, 0x3c, 0xa0, 0x0d, 0x62, 0x54, 0x77, 0x51, 0x91, 0xc9, 0x76, 0xae,
	0xb6, 0x9c, 0xdd, 0x6d, 0xbb, 0xd3, 0x6f, 0xe3, 0xae, 0xa6, 0xe8, 0xd9, 0x8a, 0x31, 0x7a, 0xfd,
	0x96, 0xce, 0x92, 0xc5, 0x9b, 0x9d, 0x7e, 0xdb, 0x62, 0x61, 0x33, 0x93, 0xda, 0x46, 0x8b, 0x01,
	0x4a, 0x1b, 0x96, 0xe1, 0x9e, 0xb7, 0x51, 0x7e, 0x38, 0x2e, 0x88, 0xee, 0x9a, 0xc3, 0x12, 0x8b,
	0xee, 0x9d, 0x3a, 0x5a, 0x10, 0xca, 0xce, 0xdb, 0xbe, 0x10, 0x56, 0xdf, 0x0b, 0xab, 0x34, 0x02,
	0xcd, 0xdf, 0xf5, 0x5e, 0x40, 0x45, 0x5e, 0xac, 0xcc, 0xa8, 0x5e, 0x47, 0x38, 0xc4, 0xa0, 0x41,
	0xdd, 0xf0, 0x82, 0x2a, 0x8f, 0x8e, 0xa1, 0xe1, 0x2c, 0x06, 0x48, 0x34, 0xa0, 0xa3, 0x28, 0xb3,
	0x6f, 0xef, 0xd9, 0xb5, 0x9e, 0xbd, 0x0b, 0xcd, 0xe0, 0xb6, 0xa2, 0xa5, 0x48, 0x37, 0xa0, 0x56,
	0xaf, 0x1b, 0x1c, 0x43, 0x59, 0xa6, 0x82, 0xcd, 0xfb, 0x26, 0x91, 0x4d, 0x5b, 0x6c, 0x31, 0xb4,
	0x03, 0x1d, 0xe5, 0x98, 0xce, 0x6f, 0x07, 0x6f, 0x11, 0x61, 0xc1, 0x62, 0xeb, 0xfd, 0x7e, 0x20,
	0x2a, 0xfd, 0x7e, 0xf0, 0x36, 0x51, 0x66, 0xb8, 0xd2, 0x6f, 0x08, 0x01, 0xb6, 0x69, 0xe0, 0x77,
	0x88, 0x30, 0x25, 0xb1, 0x4d, 0x23, 0xc4, 0x36, 0x0d, 0xfc, 0x2e, 0x11, 0xaa, 0x32, 0x3b, 0xa0,
	0xf4, 0x5b, 0xc2, 0x7b, 0x44, 0x39, 0x25, 0xb3, 0x4d, 0x43, 0x3d, 0x8e, 0xf2, 0x4c, 0x49, 0xf7,
	0xf9, 0xfb, 0x44, 0x3a, 0x6b, 0x31, 0x17, 0xb4, 0x29, 0xfc, 0x00, 0x15, 0x38, 0x9f, 0x8a, 0x3f,
	0x20, 0xe2, 0x9c, 0xc5, 0xbc, 0xb0, 0xae, 0x20, 0x46, 0x05, 0x5d, 0xe1, 0x43, 0x22, 0x4d, 0xf2,
	0xa8, 0xa0, 0x2d, 0x84, 0xbe, 0x81, 0x69, 0xe0, 0x8f, 0x88, 0x72, 0x26, 0xf0, 0x0d, 0x4c, 0x23,
	0xe2, 0x1b, 0x98, 0x06, 0xfe, 0x98, 0x88, 0xf3, 0xc1, 0x6f, 0x10, 0xc8, 0x82, 0xdf, 0x18, 0x3e,
	0x21, 0x5a, 0x85, 0x67, 0xc1, 0xef, 0x0c, 0x52, 0x66, 0xa1, 0x33, 0x7c, 0xaa, 0x78, 0x63, 0x89,
	0x67, 0x16, 0x5a, 0x83, 0x18, 0x15, 0xb4, 0x86, 0xcf, 0x88, 0x30, 0xcd, 0xa3, 0x82, 0xde, 0x60,
	0xa3, 0x22, 0xd3, 0x09, 0xbd, 0xe1, 0x73, 0x22, 0x8e, 0xdd, 0x1c, 0xa8, 0x47, 0xde, 0x1c, 0x3a,
	0x68, 0x31, 0x80, 0xa1, 0xfb, 0xe8, 0x0b, 0x42, 0x9a, 0xa4, 0x3b, 0x48, 0x30, 0xba, 0x99, 0x76,
	0xd0, 0x82, 0x50, 0x82, 0x42, 0x77, 0xf8, 0x12, 0x02, 0x1b, 0xbb, 0x3d, 0xf0, 0xc2, 0xe5, 0xed,
	0xc1, 0x41, 0x38, 0x04, 0xa1, 0x61, 0x7d, 0x05, 0x61, 0xc5, 0xe9, 0x0f, 0x01, 0x14, 0x0d, 0xe9,
	0xd7, 0x28, 0xd3, 0xae, 0xed, 0x79, 0xad, 0x01, 0xfa, 0xc3, 0xfd, 0x49, 0x0f, 0xf1, 0xa3, 0x31,
	0x32, 0x77, 0xa9, 0xb6, 0x47, 0xba, 0x08, 0xf9, 0xb7, 0xd9, 0xe9, 0xed, 0x1f, 0x58, 0xf3, 0x6d,
	0x6e, 0x51, 0x77, 0x50, 0x96, 0x11, 0xa0, 0x11, 0x3c, 0x00, 0x88, 0x1f, 0x8f, 0x8f, 0xf0, 0xba,
	0x10, 0x30, 0xd2, 0x6d, 0xc1, 0xa4, 0x36, 0x50, 0x8e, 0x41, 0xfc, 0xc6, 0xf4, 0x20, 0x50, 0x7e,
	0x32, 0x3e, 0x05, 0x5a, 0x18, 0x60, 0x32, 0x6d, 0xd1, 0x26, 0x71, 0xfc, 0xb6, 0xf6, 0x50, 0x6c,
	0xce, 0x56, 0x04, 0xc7, 0x6f, 0x8a, 0x81, 0xa4, 0x99, 0x06, 0x7e, 0x78, 0x92, 0xa4, 0x99, 0x46,
	0x28, 0x69, 0xa6, 0x11, 0x4a, 0x9a, 0x69, 0xe0, 0x47, 0x26, 0x4a, 0x1a, 0xc5, 0x88, 0x49, 0x0b,
	0x70, 0xfc, 0x7e, 0xfc, 0xe8, 0x44, 0x49, 0x0b, 0x72, 0xfc, 0x6e, 0xde, 0x42, 0x79, 0xc6, 0xa1,
	0x0d, 0xfa, 0x31, 0x00, 0x9d, 0x1a, 0x1f, 0xe4, 0xf7, 0x7d, 0x20, 0x65, 0xdb, 0x92, 0x51, 0x75,
	0x50, 0x81, 0xa7, 0x8e, 0xb2, 0x1e, 0x07, 0xd6, 0xe9, 0x18, 0xc9, 0x6b, 0x88, 0xb0, 0x5c, 0x5b,
	0xb6, 0x4a, 0xd5, 0x00, 0xc3, 0xe4, 0x89, 0xd8, 0xd5, 0xe0, 0x8d, 0x1d, 0xb9, 0x1a, 0x60, 0x12,
	0x85, 0xb2, 0x67, 0x1a, 0xf8, 0xc9, 0xc9, 0xb2, 0x47, 0x9f, 0x93, 0x94, 0x3d, 0xd3, 0x88, 0xc8,
	0x9e, 0x69, 0xe0, 0xa7, 0x26, 0xcc, 0x1e, 0x85, 0xc9, 0xd9, 0x0b, 0x94, 0x9f, 0x3f, 0x08, 0x9f,
	0x8e, 0x5d, 0x7e, 0x30, 0x32, 0xe5, 0xf2, 0xf3, 0xc7, 0xa8, 0xb4, 0x9d, 0x60, 0x8c, 0x3e, 0x13,
	0x7f, 0x3b, 0x79, 0x0e, 0x02, 0xdb, 0x09, 0x86, 0xb0, 0x58, 0x0d, 0x30, 0x84, 0x9f, 0x8d, 0x5d,
	0x0d, 0xde, 0xb8, 0x96, 0xab, 0x01, 0x26, 0xf8, 0x1e, 0x2a, 0x32, 0x88, 0x30, 0xc1, 0x9f, 0x03,
	0xd2, 0x99, 0xf1, 0x49, 0x6c, 0x6a, 0x03, 0x2d, 0xdf, 0x0e, 0x98, 0xd5, 0x03, 0xb4, 0x18, 0x20,
	0xd2, 0xa9, 0xf7, 0x3c, 0x40, 0xcf, 0xc6, 0x84, 0xfa, 0x36, 0xe0, 0x16, 0xdb, 0xe1, 0x4f, 0xd4,
	0x1b, 0x68, 0x41, 0x68, 0x84, 0xc2, 0x5c, 0x7f, 0x01, 0xc8, 0x1b, 0x71, 0xda, 0x21, 0x9b, 0xe8,
	0x00, 0x56, 0xdb, 0xa1, 0x0f, 0xd4, 0x9b, 0x08, 0x87, 0xb8, 0x34, 0xe8, 0x17, 0x01, 0xbd, 0x19,
	0x1b, 0x2d, 0x85, 0xbd, 0xd0, 0x8e, 0xfa, 0x8c, 0x96, 0x92, 0x37, 0x73, 0x60, 0xfc, 0xbf, 0x14,
	0xab, 0x94, 0xbc, 0x21, 0xcc, 0xe7, 0x3f, 0x29, 0x25, 0x66, 0xa2, 0xfb, 0xa2, 0x2b, 0x50, 0x5e,
	0x8e, 0xb5, 0x2f, 0x60, 0x06, 0x73, 0x0c, 0xd9, 0x17, 0xdc, 0x46, 0x39, 0x7d, 0x81, 0xf3, 0x4a,
	0x2c, 0xce, 0x56, 0x04, 0x87, 0xdb, 0x84, 0xa4, 0x99, 0x06, 0x60, 0x5e, 0x8d, 0x9b, 0x34, 0xd3,
	0x08, 0x25, 0x0d, 0x4c, 0x62, 0xd2, 0x28, 0xe5, 0xb5, 0xd8, 0x49, 0x13, 0x31, 0x34, 0x69, 0x32,
	0xa7, 0x2f, 0x70, 0x5e, 0x8f, 0x9d, 0xb4, 0x20, 0x87, 0xdb, 0xe8, 0x74, 0xf1, 0x27, 0x1a, 0x80,
	0x6e, 0xc5, 0x9a, 0x2e, 0xfe, 0x08, 0xe6, 0x24, 0xf2, 0x34, 0x04, 0x23, 0x4b, 0x9d, 0xd7, 0x2d,
	0x81, 0xf4, 0x46, 0xbc, 0xd4, 0x79, 0x1e, 0x02, 0xa9, 0x63, 0x36, 0x55, 0x43, 0xc8, 0xed, 0xd8,
	0x6e, 0x03, 0x10, 0xbf, 0x4f, 0x69, 0x8a, 0x7e, 0xa8, 0x9a, 0xb0, 0xe6, 0x3c, 0xa3, 0xa7, 0x58,
	0x46, 0xf3, 0xa0, 0x80, 0x93, 0xe2, 0x1f, 0x88, 0x64, 0xba, 0x9a, 0xb0, 0x60, 0x1d, 0x9c, 0x5c,
	0x8f, 0xa2, 0x34, 0x68, 0xfc, 0x63, 0xeb, 0x1f, 0x89, 0xa8, 0x50, 0x4d, 0x58, 0xb0, 0xd4, 0x3f,
	0x77, 0x32, 0x95, 0x7f, 0xe8, 0xfc, 0x13, 0x51, 0x65, 0x98, 0xca, 0x3f, 0x35, 0x8a, 0x3c, 0xd3,
	0xc0, 0x7f, 0x26, 0xa2, 0x94, 0xc8, 0x33, 0x0d, 0x99, 0x67, 0x1a, 0xf8, 0x2f, 0x44, 0xa4, 0x4a,
	0x3c, 0x51, 0xe5, 0x9f, 0xd7, 0xfe, 0x4a, 0x54, 0x53, 0x12, 0xcf, 0x34, 0xd4, 0x63, 0x28, 0x03,
	0x2a, 0x7a, 0x02, 0xfa, 0x1b, 0x91, 0xcd, 0x56, 0x13, 0x16, 0xac, 0xa6, 0xa7, 0x25, 0x1d, 0x65,
	0x7d, 0x26, 0x15, 0xfe, 0x9d, 0x08, 0x73, 0xd5, 0x84, 0x05, 0x0e, 0xd8, 0x49, 0x87, 0x45, 0x00,
	0xc7, 0x9c, 0x7f, 0x10, 0x59, 0x92, 0x45, 0x00, 0x07, 0x15, 0x99, 0x6a, 0x1a, 0xf8, 0x9f, 0x44,
	0x35, 0x23, 0x53, 0xbd, 0x0b, 0xb0, 0x44, 0x35, 0x0d, 0xfc, 0x2f, 0x22, 0xcc, 0x07, 0xa8, 0x62,
	0xb4, 0xfe, 0xf1, 0xe0, 0xdf, 0x44, 0xa7, 0xb0, 0x68, 0xfd, 0xf9, 0xce, 0x33, 0x07, 0xc3, 0xfd,
	0x3f, 0x44, 0x35, 0xc7, 0x33, 0x07, 0xd3, 0x99, 0x45, 0x00, 0xa3, 0xf9, 0xbf, 0x44, 0x94, 0x66,
	0x11, 0xc0, 0x70, 0xad, 0xa1, 0x3c, 0x68, 0x84, 0xc9, 0xfa, 0xbf, 0x54, 0xfc, 0x17, 0x67, 0xd5,
	0x84, 0x05, 0xa1, 0xf2, 0x69, 0x7a, 0x0d, 0x15, 0x45, 0x04, 0x9d, 0x2a, 0xff, 0x4f, 0x4d, 0xf4,
	0xd6, 0xac, 0x9a, 0xb0, 0x0a, 0x1c, 0x44, 0xa7, 0xc8, 0x36, 0x52, 0x69, 0x49, 0x09, 0xb3, 0xf3,
	0xae, 0x54, 0x9c, 0x57, 0x66, 0xd5, 0x84, 0x95, 0xf7, 0x0b, 0x91, 0xcf, 0xc9, 0xab, 0x68, 0x41,
	0x06, 0xd0, 0x70, 0xee, 0x4e, 0xc5, 0x7c, 0x5f, 0x56, 0x4d, 0x58, 0x45, 0x11, 0x43, 0x43, 0x61,
	0xb5, 0x05, 0xcf, 0xb8, 0x8c, 0xef, 0xa1, 0x0f, 0x39, 0x2d, 0x3c, 0xe4, 0x72, 0x50, 0x57, 0xc1,
	0xf7, 0x46, 0xe9, 0x2a, 0x41, 0xdd, 0x2a, 0xbe, 0x2f, 0x4a, 0xb7, 0xba, 0x74, 0x0d, 0xa5, 0xa5,
	0x94, 0x7e, 0x83, 0xff, 0x57, 0xb0, 0x74, 0x0a, 0xe5, 0x83, 0x37, 0x76, 0x35, 0x8f, 0x52, 0xd7,
	0xed, 0x03, 0x0f, 0x78, 0xc8, 0x22, 0x3f, 0xaa, 0x87, 0xd1, 0xf4, 0x8d, 0x9a, 0xd3, 0xb7, 0x71,
	0xd2, 0xb3, 0xc1, 0x2f, 0x27, 0x93, 0x6b, 0xca, 0xd2, 0x69, 0x54, 0x08, 0x5d, 0xc7, 0x87, 0x39,
	0x98, 0x16, 0x1d, 0x9c, 0x41, 0x6a, 0xf8, 0xa6, 0x3d, 0xcc, 0x43, 0x21, 0xda, 0xc3, 0xd6, 0xe8,
	0x1e, 0x32, 0x03, 0x83, 0xf0, 0xaf, 0x0e, 0xc3, 0x1c, 0xa4, 0x06, 0x07, 0x31, 0xa2, 0x07, 0x75,
	0x70, 0x10, 0x23, 0x7a, 0x98, 0x12, 0x3d, 0xac, 0xa3, 0x62, 0xc4, 0x65, 0x75, 0x98, 0x8b, 0x59,
	0xd1, 0xc5, 0x06, 0x3a, 0x1c, 0x75, 0x07, 0x1d, 0xe6, 0x23, 0x17, 0x9d, 0x4b, 0x7e, 0xb9, 0x1c,
	0xe6, 0x20, 0x79, 0x87, 0x38, 0x46, 0x4c, 0xc5, 0xcc, 0x9d, 0xe2, 0x18, 0xd1, 0x47, 0x3e, 0xfa,
	0x81, 0x08, 0xb7, 0xbc, 0x61, 0x1e, 0x94, 0x01, 0x45, 0xc1, 0xef, 0x6f, 0xc3, 0x3c, 0xcc, 0x45,
	0xe7, 0x92, 0x5f, 0xcd, 0x86, 0x39, 0x48, 0x8b, 0x0e, 0x0e, 0xd0, 0x42, 0xe4, 0x8d, 0x2b, 0xc2,
	0xc9, 0xcf, 0x44, 0x27, 0x71, 0x5f, 0xcb, 0x0a, 0xe8, 0x9b, 0x08, 0x0f, 0xba, 0x77, 0x45, 0xd0,
	0x2f, 0x89, 0xf4, 0x09, 0x5e, 0xd5, 0x0a, 0x5f, 0xa0, 0x8f, 0xbe, 0x35, 0xe0, 0xfa, 0x15, 0xc1,
	0x3f, 0x27, 0x47, 0x3f, 0xee, 0xbb, 0x5b, 0x01, 0xfb, 0x3b, 0xb4, 0x34, 0xf8, 0xea, 0x15, 0x41,
	0xfe, 0xa9, 0x1c, 0x79, 0x8c, 0xb7, 0xb9, 0xa1, 0x82, 0x91, 0x2f, 0x60, 0x22, 0x73, 0x7a, 0x58,
	0x3b, 0x87, 0x9a, 0x0d, 0xdc, 0xad, 0x44, 0x0f, 0x85, 0xd1, 0x3c, 0x6c, 0x0d, 0xf6, 0x90, 0x19,
	0x6d, 0xa4, 0xc8, 0x17, 0x22, 0xd1, 0x41, 0x6a, 0xf4, 0x20, 0x06, 0x78, 0x50, 0x47, 0x0f, 0x62,
	0x80, 0x87, 0xa9, 0x61, 0x1e, 0xa0, 0x8b, 0x05, 0xaf, 0x27, 0xa2, 0x8b, 0xd9, 0x11, 0xc3, 0x90,
	0xef, 0x1d, 0xa2, 0x87, 0xb9, 0x21, 0x1e, 0x96, 0x4b, 0x68, 0x8e, 0x1f, 0x02, 0xe7, 0xd0, 0xf4,
	0xfa, 0xc5, 0xcb, 0xd5, 0xf5, 0x7c, 0x82, 0xfc, 0xb8, 0x61, 0xad, 0xff, 0xf2, 0x17, 0x79, 0x45,
	0x9d, 0x47, 0xb3, 0x67, 0xab, 0xeb, 0xd6, 0xc5, 0x0b, 0x9b, 0xf9, 0xe4, 0x46, 0x86, 0x1e, 0x57,
	0xfb, 0x9d, 0x96, 0xdb, 0x39, 0x5e, 0x46, 0xf3, 0xe2, 0xc1, 0x2b, 0xca, 0x01, 0x52, 0xd3, 0xdc,
	0xc1, 0x6d, 0x65, 0xe3, 0xf2, 0xaf, 0x7e, 0x1e, 0xaa, 0xdf, 0x15, 0xaf, 0x7e, 0xeb, 0xfd, 0xc6,
	0x4a, 0xab, 0xd3, 0xb3, 0xf7, 0x3b, 0x35, 0xc7, 0xfb, 0xeb, 0x09, 0xcf, 0xda, 0x5d, 0x71, 0xec,
	0x66, 0x6d, 0xe7, 0x60, 0x65, 0xd0, 0x1f, 0x5a, 0xd4, 0x67, 0xe0, 0x93, 0xaf, 0x03, 0x00, 0x00,
	0xff, 0xff, 0xe8, 0x29, 0x22, 0xf1, 0x8b, 0x21, 0x00, 0x00,
}
