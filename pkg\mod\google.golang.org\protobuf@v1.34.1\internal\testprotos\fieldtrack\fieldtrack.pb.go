// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: internal/testprotos/fieldtrack/fieldtrack.proto

package fieldtrack

import (
	_ "google.golang.org/protobuf/internal/testprotos/annotation"
	test "google.golang.org/protobuf/internal/testprotos/test"
	proto "google.golang.org/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type TestFieldTrack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	weakFields    protoimpl.WeakFields
	unknownFields protoimpl.UnknownFields

	OptionalInt32         *int32                                      `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty" go:"track"`
	OptionalInt64         *int64                                      `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty" go:"track"`
	OptionalUint32        *uint32                                     `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty" go:"track"`
	OptionalUint64        *uint64                                     `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty" go:"track"`
	OptionalSint32        *int32                                      `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty" go:"track"`
	OptionalSint64        *int64                                      `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty" go:"track"`
	OptionalFixed32       *uint32                                     `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty" go:"track"`
	OptionalFixed64       *uint64                                     `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty" go:"track"`
	OptionalSfixed32      *int32                                      `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty" go:"track"`
	OptionalSfixed64      *int64                                      `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty" go:"track"`
	OptionalFloat         *float32                                    `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty" go:"track"`
	OptionalDouble        *float64                                    `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty" go:"track"`
	OptionalBool          *bool                                       `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty" go:"track"`
	OptionalString        *string                                     `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty" go:"track"`
	OptionalBytes         []byte                                      `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty" go:"track"`
	OptionalEnum          *test.TestAllTypes_NestedEnum               `protobuf:"varint,16,opt,name=optional_enum,json=optionalEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum" json:"optional_enum,omitempty" go:"track"`
	OptionalMessage       *test.TestAllTypes_NestedMessage            `protobuf:"bytes,17,opt,name=optional_message,json=optionalMessage" json:"optional_message,omitempty" go:"track"`
	RepeatedInt32         []int32                                     `protobuf:"varint,21,rep,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty" go:"track"`
	RepeatedInt64         []int64                                     `protobuf:"varint,22,rep,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty" go:"track"`
	RepeatedUint32        []uint32                                    `protobuf:"varint,23,rep,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty" go:"track"`
	RepeatedUint64        []uint64                                    `protobuf:"varint,24,rep,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty" go:"track"`
	RepeatedSint32        []int32                                     `protobuf:"zigzag32,25,rep,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty" go:"track"`
	RepeatedSint64        []int64                                     `protobuf:"zigzag64,26,rep,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty" go:"track"`
	RepeatedFixed32       []uint32                                    `protobuf:"fixed32,27,rep,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty" go:"track"`
	RepeatedFixed64       []uint64                                    `protobuf:"fixed64,28,rep,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty" go:"track"`
	RepeatedSfixed32      []int32                                     `protobuf:"fixed32,29,rep,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty" go:"track"`
	RepeatedSfixed64      []int64                                     `protobuf:"fixed64,30,rep,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty" go:"track"`
	RepeatedFloat         []float32                                   `protobuf:"fixed32,31,rep,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty" go:"track"`
	RepeatedDouble        []float64                                   `protobuf:"fixed64,32,rep,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty" go:"track"`
	RepeatedBool          []bool                                      `protobuf:"varint,33,rep,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty" go:"track"`
	RepeatedString        []string                                    `protobuf:"bytes,34,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty" go:"track"`
	RepeatedBytes         [][]byte                                    `protobuf:"bytes,35,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty" go:"track"`
	RepeatedEnum          []test.TestAllTypes_NestedEnum              `protobuf:"varint,36,rep,name=repeated_enum,json=repeatedEnum,enum=goproto.proto.test.TestAllTypes_NestedEnum" json:"repeated_enum,omitempty" go:"track"`
	RepeatedMessage       []*test.TestAllTypes_NestedMessage          `protobuf:"bytes,37,rep,name=repeated_message,json=repeatedMessage" json:"repeated_message,omitempty" go:"track"`
	MapStringInt32        map[string]int32                            `protobuf:"bytes,41,rep,name=map_string_int32,json=mapStringInt32" json:"map_string_int32,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value" go:"track"`
	MapStringInt64        map[string]int64                            `protobuf:"bytes,42,rep,name=map_string_int64,json=mapStringInt64" json:"map_string_int64,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value" go:"track"`
	MapStringUint32       map[string]uint32                           `protobuf:"bytes,43,rep,name=map_string_uint32,json=mapStringUint32" json:"map_string_uint32,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value" go:"track"`
	MapStringUint64       map[string]uint64                           `protobuf:"bytes,44,rep,name=map_string_uint64,json=mapStringUint64" json:"map_string_uint64,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value" go:"track"`
	MapStringSint32       map[string]int32                            `protobuf:"bytes,45,rep,name=map_string_sint32,json=mapStringSint32" json:"map_string_sint32,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value" go:"track"`
	MapStringSint64       map[string]int64                            `protobuf:"bytes,46,rep,name=map_string_sint64,json=mapStringSint64" json:"map_string_sint64,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value" go:"track"`
	MapStringFixed32      map[string]uint32                           `protobuf:"bytes,47,rep,name=map_string_fixed32,json=mapStringFixed32" json:"map_string_fixed32,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value" go:"track"`
	MapStringFixed64      map[string]uint64                           `protobuf:"bytes,48,rep,name=map_string_fixed64,json=mapStringFixed64" json:"map_string_fixed64,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value" go:"track"`
	MapStringSfixed32     map[string]int32                            `protobuf:"bytes,49,rep,name=map_string_sfixed32,json=mapStringSfixed32" json:"map_string_sfixed32,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value" go:"track"`
	MapStringSfixed64     map[string]int64                            `protobuf:"bytes,50,rep,name=map_string_sfixed64,json=mapStringSfixed64" json:"map_string_sfixed64,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value" go:"track"`
	MapStringFloat        map[string]float32                          `protobuf:"bytes,51,rep,name=map_string_float,json=mapStringFloat" json:"map_string_float,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value" go:"track"`
	MapStringDouble       map[string]float64                          `protobuf:"bytes,52,rep,name=map_string_double,json=mapStringDouble" json:"map_string_double,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value" go:"track"`
	MapStringBool         map[string]bool                             `protobuf:"bytes,53,rep,name=map_string_bool,json=mapStringBool" json:"map_string_bool,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value" go:"track"`
	MapStringString       map[string]string                           `protobuf:"bytes,54,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" go:"track"`
	MapStringBytes        map[string][]byte                           `protobuf:"bytes,55,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" go:"track"`
	MapStringEnum         map[string]test.TestAllTypes_NestedEnum     `protobuf:"bytes,56,rep,name=map_string_enum,json=mapStringEnum" json:"map_string_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=goproto.proto.test.TestAllTypes_NestedEnum" go:"track"`
	MapStringMessage      map[string]*test.TestAllTypes_NestedMessage `protobuf:"bytes,57,rep,name=map_string_message,json=mapStringMessage" json:"map_string_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value" go:"track"`
	XXX_weak_WeakMessage1 struct{}                                    `protobuf:"bytes,100,opt,name=weak_message1,json=weakMessage1,weak=goproto.proto.test.weak.WeakImportMessage1" json:"weak_message1,omitempty" go:"track"`
	XXX_weak_WeakMessage2 struct{}                                    `protobuf:"bytes,101,opt,name=weak_message2,json=weakMessage2,weak=goproto.proto.test.weak.WeakImportMessage2" json:"weak_message2,omitempty" go:"track"`
}

func (x *TestFieldTrack) Reset() {
	*x = TestFieldTrack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_testprotos_fieldtrack_fieldtrack_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestFieldTrack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestFieldTrack) ProtoMessage() {}

func (x *TestFieldTrack) ProtoReflect() protoreflect.Message {
	mi := &file_internal_testprotos_fieldtrack_fieldtrack_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestFieldTrack.ProtoReflect.Descriptor instead.
func (*TestFieldTrack) Descriptor() ([]byte, []int) {
	return file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescGZIP(), []int{0}
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalEnum() test.TestAllTypes_NestedEnum {
	if x != nil && x.OptionalEnum != nil {
		return *x.OptionalEnum
	}
	return test.TestAllTypes_NestedEnum(0)
}

//go:nointerface

func (x *TestFieldTrack) GetOptionalMessage() *test.TestAllTypes_NestedMessage {
	if x != nil {
		return x.OptionalMessage
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedEnum() []test.TestAllTypes_NestedEnum {
	if x != nil {
		return x.RepeatedEnum
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetRepeatedMessage() []*test.TestAllTypes_NestedMessage {
	if x != nil {
		return x.RepeatedMessage
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringInt32() map[string]int32 {
	if x != nil {
		return x.MapStringInt32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringInt64() map[string]int64 {
	if x != nil {
		return x.MapStringInt64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringUint32() map[string]uint32 {
	if x != nil {
		return x.MapStringUint32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringUint64() map[string]uint64 {
	if x != nil {
		return x.MapStringUint64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringSint32() map[string]int32 {
	if x != nil {
		return x.MapStringSint32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringSint64() map[string]int64 {
	if x != nil {
		return x.MapStringSint64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringFixed32() map[string]uint32 {
	if x != nil {
		return x.MapStringFixed32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringFixed64() map[string]uint64 {
	if x != nil {
		return x.MapStringFixed64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringSfixed32() map[string]int32 {
	if x != nil {
		return x.MapStringSfixed32
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringSfixed64() map[string]int64 {
	if x != nil {
		return x.MapStringSfixed64
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringFloat() map[string]float32 {
	if x != nil {
		return x.MapStringFloat
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringDouble() map[string]float64 {
	if x != nil {
		return x.MapStringDouble
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringBool() map[string]bool {
	if x != nil {
		return x.MapStringBool
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringEnum() map[string]test.TestAllTypes_NestedEnum {
	if x != nil {
		return x.MapStringEnum
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetMapStringMessage() map[string]*test.TestAllTypes_NestedMessage {
	if x != nil {
		return x.MapStringMessage
	}
	return nil
}

//go:nointerface

func (x *TestFieldTrack) GetWeakMessage1() proto.Message {
	var w protoimpl.WeakFields
	if x != nil {
		w = x.weakFields
		_ = x.XXX_weak_WeakMessage1
	}
	return protoimpl.X.GetWeak(w, 100, "goproto.proto.test.weak.WeakImportMessage1")
}

//go:nointerface

func (x *TestFieldTrack) GetWeakMessage2() proto.Message {
	var w protoimpl.WeakFields
	if x != nil {
		w = x.weakFields
		_ = x.XXX_weak_WeakMessage2
	}
	return protoimpl.X.GetWeak(w, 101, "goproto.proto.test.weak.WeakImportMessage2")
}

//go:nointerface

func (x *TestFieldTrack) SetWeakMessage1(v proto.Message) {
	var w *protoimpl.WeakFields
	if x != nil {
		w = &x.weakFields
		_ = x.XXX_weak_WeakMessage1
	}
	protoimpl.X.SetWeak(w, 100, "goproto.proto.test.weak.WeakImportMessage1", v)
}

//go:nointerface

func (x *TestFieldTrack) SetWeakMessage2(v proto.Message) {
	var w *protoimpl.WeakFields
	if x != nil {
		w = &x.weakFields
		_ = x.XXX_weak_WeakMessage2
	}
	protoimpl.X.SetWeak(w, 101, "goproto.proto.test.weak.WeakImportMessage2", v)
}

var File_internal_testprotos_fieldtrack_fieldtrack_proto protoreflect.FileDescriptor

var file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x12, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f,
	0x77, 0x65, 0x61, 0x6b, 0x31, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f,
	0x77, 0x65, 0x61, 0x6b, 0x32, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f,
	0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74,
	0x2f, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfe, 0x24, 0x0a, 0x0e,
	0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x25,
	0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27,
	0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25,
	0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x59, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0x15, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x16, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x18, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x19, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x1a,
	0x20, 0x03, 0x28, 0x12, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0x1d, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x1e, 0x20, 0x03,
	0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x21, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x22, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x18, 0x23, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x59, 0x0a, 0x10, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x25, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x60, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x29, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x60, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x2a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x63, 0x0a, 0x11, 0x6d, 0x61,
	0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18,
	0x2b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x63, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x63, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x63, 0x0a, 0x11, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x2e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x66,
	0x0a, 0x12, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x18, 0x2f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x66, 0x0a, 0x12, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x30, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x6d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x69,
	0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x31, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x69, 0x0a, 0x13, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x18, 0x32, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x11, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x60, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f, 0x61,
	0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x63, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x34, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x5d, 0x0a, 0x0f, 0x6d,
	0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x35,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x63, 0x0a, 0x11, 0x6d, 0x61,
	0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x36, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x60, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x18, 0x37, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x5d, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x18, 0x38, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x66, 0x0a, 0x12, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x77, 0x65, 0x61, 0x6b,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x74, 0x65, 0x73, 0x74, 0x2e, 0x77, 0x65, 0x61, 0x6b, 0x2e, 0x57, 0x65, 0x61, 0x6b, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x42, 0x02, 0x50, 0x01,
	0x52, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x31, 0x12, 0x54,
	0x0a, 0x0d, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x18,
	0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x77, 0x65, 0x61, 0x6b, 0x2e,
	0x57, 0x65, 0x61, 0x6b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x32, 0x42, 0x02, 0x50, 0x01, 0x52, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x32, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42,
	0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x43, 0x0a, 0x15, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x07, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x43, 0x0a, 0x15, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61,
	0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a,
	0x13, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x6d, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x75,
	0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x73, 0x0a, 0x15, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x44, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x3a, 0x06, 0xa8, 0xe0, 0xcd, 0x8e, 0x01, 0x01, 0x42, 0x3b, 0x5a, 0x39,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72,
	0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x58, 0x00, 0x58, 0x01,
}

var (
	file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescOnce sync.Once
	file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescData = file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDesc
)

func file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescGZIP() []byte {
	file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescOnce.Do(func() {
		file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescData)
	})
	return file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDescData
}

var file_internal_testprotos_fieldtrack_fieldtrack_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_internal_testprotos_fieldtrack_fieldtrack_proto_goTypes = []interface{}{
	(*TestFieldTrack)(nil),                  // 0: goproto.proto.test.TestFieldTrack
	nil,                                     // 1: goproto.proto.test.TestFieldTrack.MapStringInt32Entry
	nil,                                     // 2: goproto.proto.test.TestFieldTrack.MapStringInt64Entry
	nil,                                     // 3: goproto.proto.test.TestFieldTrack.MapStringUint32Entry
	nil,                                     // 4: goproto.proto.test.TestFieldTrack.MapStringUint64Entry
	nil,                                     // 5: goproto.proto.test.TestFieldTrack.MapStringSint32Entry
	nil,                                     // 6: goproto.proto.test.TestFieldTrack.MapStringSint64Entry
	nil,                                     // 7: goproto.proto.test.TestFieldTrack.MapStringFixed32Entry
	nil,                                     // 8: goproto.proto.test.TestFieldTrack.MapStringFixed64Entry
	nil,                                     // 9: goproto.proto.test.TestFieldTrack.MapStringSfixed32Entry
	nil,                                     // 10: goproto.proto.test.TestFieldTrack.MapStringSfixed64Entry
	nil,                                     // 11: goproto.proto.test.TestFieldTrack.MapStringFloatEntry
	nil,                                     // 12: goproto.proto.test.TestFieldTrack.MapStringDoubleEntry
	nil,                                     // 13: goproto.proto.test.TestFieldTrack.MapStringBoolEntry
	nil,                                     // 14: goproto.proto.test.TestFieldTrack.MapStringStringEntry
	nil,                                     // 15: goproto.proto.test.TestFieldTrack.MapStringBytesEntry
	nil,                                     // 16: goproto.proto.test.TestFieldTrack.MapStringEnumEntry
	nil,                                     // 17: goproto.proto.test.TestFieldTrack.MapStringMessageEntry
	(test.TestAllTypes_NestedEnum)(0),       // 18: goproto.proto.test.TestAllTypes.NestedEnum
	(*test.TestAllTypes_NestedMessage)(nil), // 19: goproto.proto.test.TestAllTypes.NestedMessage
}
var file_internal_testprotos_fieldtrack_fieldtrack_proto_depIdxs = []int32{
	18, // 0: goproto.proto.test.TestFieldTrack.optional_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	19, // 1: goproto.proto.test.TestFieldTrack.optional_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	18, // 2: goproto.proto.test.TestFieldTrack.repeated_enum:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	19, // 3: goproto.proto.test.TestFieldTrack.repeated_message:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	1,  // 4: goproto.proto.test.TestFieldTrack.map_string_int32:type_name -> goproto.proto.test.TestFieldTrack.MapStringInt32Entry
	2,  // 5: goproto.proto.test.TestFieldTrack.map_string_int64:type_name -> goproto.proto.test.TestFieldTrack.MapStringInt64Entry
	3,  // 6: goproto.proto.test.TestFieldTrack.map_string_uint32:type_name -> goproto.proto.test.TestFieldTrack.MapStringUint32Entry
	4,  // 7: goproto.proto.test.TestFieldTrack.map_string_uint64:type_name -> goproto.proto.test.TestFieldTrack.MapStringUint64Entry
	5,  // 8: goproto.proto.test.TestFieldTrack.map_string_sint32:type_name -> goproto.proto.test.TestFieldTrack.MapStringSint32Entry
	6,  // 9: goproto.proto.test.TestFieldTrack.map_string_sint64:type_name -> goproto.proto.test.TestFieldTrack.MapStringSint64Entry
	7,  // 10: goproto.proto.test.TestFieldTrack.map_string_fixed32:type_name -> goproto.proto.test.TestFieldTrack.MapStringFixed32Entry
	8,  // 11: goproto.proto.test.TestFieldTrack.map_string_fixed64:type_name -> goproto.proto.test.TestFieldTrack.MapStringFixed64Entry
	9,  // 12: goproto.proto.test.TestFieldTrack.map_string_sfixed32:type_name -> goproto.proto.test.TestFieldTrack.MapStringSfixed32Entry
	10, // 13: goproto.proto.test.TestFieldTrack.map_string_sfixed64:type_name -> goproto.proto.test.TestFieldTrack.MapStringSfixed64Entry
	11, // 14: goproto.proto.test.TestFieldTrack.map_string_float:type_name -> goproto.proto.test.TestFieldTrack.MapStringFloatEntry
	12, // 15: goproto.proto.test.TestFieldTrack.map_string_double:type_name -> goproto.proto.test.TestFieldTrack.MapStringDoubleEntry
	13, // 16: goproto.proto.test.TestFieldTrack.map_string_bool:type_name -> goproto.proto.test.TestFieldTrack.MapStringBoolEntry
	14, // 17: goproto.proto.test.TestFieldTrack.map_string_string:type_name -> goproto.proto.test.TestFieldTrack.MapStringStringEntry
	15, // 18: goproto.proto.test.TestFieldTrack.map_string_bytes:type_name -> goproto.proto.test.TestFieldTrack.MapStringBytesEntry
	16, // 19: goproto.proto.test.TestFieldTrack.map_string_enum:type_name -> goproto.proto.test.TestFieldTrack.MapStringEnumEntry
	17, // 20: goproto.proto.test.TestFieldTrack.map_string_message:type_name -> goproto.proto.test.TestFieldTrack.MapStringMessageEntry
	18, // 21: goproto.proto.test.TestFieldTrack.MapStringEnumEntry.value:type_name -> goproto.proto.test.TestAllTypes.NestedEnum
	19, // 22: goproto.proto.test.TestFieldTrack.MapStringMessageEntry.value:type_name -> goproto.proto.test.TestAllTypes.NestedMessage
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_internal_testprotos_fieldtrack_fieldtrack_proto_init() }
func file_internal_testprotos_fieldtrack_fieldtrack_proto_init() {
	if File_internal_testprotos_fieldtrack_fieldtrack_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_testprotos_fieldtrack_fieldtrack_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestFieldTrack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.weakFields
			case 3:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_testprotos_fieldtrack_fieldtrack_proto_goTypes,
		DependencyIndexes: file_internal_testprotos_fieldtrack_fieldtrack_proto_depIdxs,
		MessageInfos:      file_internal_testprotos_fieldtrack_fieldtrack_proto_msgTypes,
	}.Build()
	File_internal_testprotos_fieldtrack_fieldtrack_proto = out.File
	file_internal_testprotos_fieldtrack_fieldtrack_proto_rawDesc = nil
	file_internal_testprotos_fieldtrack_fieldtrack_proto_goTypes = nil
	file_internal_testprotos_fieldtrack_fieldtrack_proto_depIdxs = nil
}
