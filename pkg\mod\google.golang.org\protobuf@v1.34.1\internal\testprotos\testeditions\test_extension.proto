// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

edition = "2023";

package goproto.proto.testeditions;

import "internal/testprotos/testeditions/test.proto";

option go_package = "google.golang.org/protobuf/internal/testprotos/testeditions";
option features.repeated_field_encoding = EXPANDED;
option features.utf8_validation = NONE;

message TestAllExtensions {
  message NestedMessage {
    int32 a = 1;
    TestAllExtensions corecursive = 2;
  }

  extensions 1 to max;
}

extend TestAllExtensions {
  int32 optional_int32 = 1;
  int64 optional_int64 = 2;
  uint32 optional_uint32 = 3;
  uint64 optional_uint64 = 4;
  sint32 optional_sint32 = 5;
  sint64 optional_sint64 = 6;
  fixed32 optional_fixed32 = 7;
  fixed64 optional_fixed64 = 8;
  sfixed32 optional_sfixed32 = 9;
  sfixed64 optional_sfixed64 = 10;
  float optional_float = 11;
  double optional_double = 12;
  bool optional_bool = 13;
  string optional_string = 14;
  bytes optional_bytes = 15;
  OptionalGroup optionalgroup = 16 [features.message_encoding = DELIMITED];

  TestAllExtensions.NestedMessage optional_nested_message = 18;
  TestAllTypes.NestedEnum optional_nested_enum = 21;
  repeated int32 repeated_int32 = 31;
  repeated int64 repeated_int64 = 32;
  repeated uint32 repeated_uint32 = 33;
  repeated uint64 repeated_uint64 = 34;
  repeated sint32 repeated_sint32 = 35;
  repeated sint64 repeated_sint64 = 36;
  repeated fixed32 repeated_fixed32 = 37;
  repeated fixed64 repeated_fixed64 = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated float repeated_float = 41;
  repeated double repeated_double = 42;
  repeated bool repeated_bool = 43;
  repeated string repeated_string = 44;
  repeated bytes repeated_bytes = 45;
  repeated RepeatedGroup repeatedgroup = 46
      [features.message_encoding = DELIMITED];

  repeated TestAllExtensions.NestedMessage repeated_nested_message = 48;
  repeated TestAllTypes.NestedEnum repeated_nested_enum = 51;
  int32 default_int32 = 81 [default = 81];

  int64 default_int64 = 82 [default = 82];

  uint32 default_uint32 = 83 [default = 83];

  uint64 default_uint64 = 84 [default = 84];

  sint32 default_sint32 = 85 [default = -85];

  sint64 default_sint64 = 86 [default = 86];

  fixed32 default_fixed32 = 87 [default = 87];

  fixed64 default_fixed64 = 88 [default = 88];

  sfixed32 default_sfixed32 = 89 [default = 89];

  sfixed64 default_sfixed64 = 80 [default = -90];

  float default_float = 91 [default = 91.5];

  double default_double = 92 [default = 9.2e4];

  bool default_bool = 93 [default = true];

  string default_string = 94 [default = "hello"];

  bytes default_bytes = 95 [default = "world"];
}

message OptionalGroup {
  int32 a = 17;
  int32 same_field_number = 16;
  TestAllExtensions.NestedMessage optional_nested_message = 1000;
}

message RepeatedGroup {
  int32 a = 47;
  TestAllExtensions.NestedMessage optional_nested_message = 1001;
}

extend TestAllExtensions {
  TestRequired single = 1000;
  repeated TestRequired multi = 1001;
}

message TestFeatureResolution {
  extensions 2 to max;
}

extend TestFeatureResolution {
  repeated int32 global_expanded_extension = 2;
  repeated int32 global_packed_extension_overriden = 3
      [features.repeated_field_encoding = PACKED];
}

message RepeatedFieldEncoding {
  extend TestFeatureResolution {
    repeated int32 message_expanded_extension = 4;
    repeated int32 message_packed_extension_overriden = 5
        [features.repeated_field_encoding = PACKED];
  }
}
