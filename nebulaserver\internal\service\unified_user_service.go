package service

import (
	"context"
	"fmt"
	"strconv"

	"nebulaserver/internal/model"
	"nebulaserver/internal/pkg/grpc"
	unified_user "nebulaserver/proto/unified/user"

	dtalk_common "git.eykj.cn/base/grpc/dtalk/common"
	dtalk_user "git.eykj.cn/base/grpc/dtalk/user"

	"git.eykj.cn/base/core/pkg/logger"
)

// UnifiedUserService 统一用户服务实现
type UnifiedUserService struct {
	unified_user.UnimplementedUnifiedUserServiceServer
	router *grpc.PlatformRouter
}

// NewUnifiedUserService 创建统一用户服务
func NewUnifiedUserService() (*UnifiedUserService, error) {
	router, err := grpc.GetGlobalRouter()
	if err != nil {
		return nil, fmt.Errorf("获取平台路由器失败: %w", err)
	}

	return &UnifiedUserService{
		router: router,
	}, nil
}

// ============ GetUsers 统一接口实现 ============

// GetUsers 统一用户查询接口（整合三种查询场景）
// 根据 query_type 路由到不同的查询逻辑
func (s *UnifiedUserService) GetUsers(ctx context.Context, req *unified_user.GetUsersRequest) (*unified_user.GetUsersResponse, error) {
	logger.Info("🚀 统一用户服务：GetUsers接口",
		logger.String("app_id", req.AppId),
		logger.String("runtime", req.Runtime),
		logger.String("corp_id", req.CorpId),
		logger.String("query_type", req.QueryType.String()))

	// 参数验证
	if req.AppId == "" || req.CorpId == "" {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   "app_id和corp_id不能为空",
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 根据查询类型路由到不同的处理逻辑
	switch req.QueryType {
	case unified_user.QueryType_SINGLE_USER:
		return s.getUsersSingleUser(ctx, req)

	case unified_user.QueryType_DEPARTMENT_USERS:
		return s.getUsersDepartmentUsers(ctx, req)

	case unified_user.QueryType_ALL_USERS:
		return s.getUsersAllUsers(ctx, req)

	default:
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   "无效的查询类型",
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}
}

// getUsersSingleUser 处理单个用户查询（只查数据库）
func (s *UnifiedUserService) getUsersSingleUser(ctx context.Context, req *unified_user.GetUsersRequest) (*unified_user.GetUsersResponse, error) {
	logger.Info("📂 单用户查询：只从数据库查询",
		logger.String("user_id", req.UserId),
		logger.String("corp_id", req.CorpId))

	// 参数验证
	if req.UserId == "" {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   "SINGLE_USER模式下user_id不能为空",
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 从数据库查询
	dbUser, err := model.GetUserByUseridAndCorpid(req.UserId, req.CorpId)
	if err != nil {
		logger.Error("查询数据库用户失败", logger.Error2(err))
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("查询用户失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 数据库有数据，返回用户信息
	if dbUser != nil {
		logger.Info("✅ 从数据库返回用户信息", logger.String("user_id", req.UserId))
		return &unified_user.GetUsersResponse{
			Code:      0,
			Message:   "成功",
			QueryType: req.QueryType,
			Data: &unified_user.UsersData{
				Users:    []*unified_user.UserDetail{convertDBUserToUnified(dbUser)},
				Total:    1,
				Page:     1,
				PageSize: 1,
			},
		}, nil
	}

	// 数据库没有数据，返回空列表
	logger.Warn("⚠️ 数据库中未找到用户",
		logger.String("user_id", req.UserId),
		logger.String("corp_id", req.CorpId))

	return &unified_user.GetUsersResponse{
		Code:      0,
		Message:   "用户不存在",
		QueryType: req.QueryType,
		Data: &unified_user.UsersData{
			Users:    []*unified_user.UserDetail{},
			Total:    0,
			Page:     1,
			PageSize: 1,
		},
	}, nil
}

// getUsersDepartmentUsers 处理部门用户查询（调用钉钉服务）
func (s *UnifiedUserService) getUsersDepartmentUsers(ctx context.Context, req *unified_user.GetUsersRequest) (*unified_user.GetUsersResponse, error) {
	logger.Info("🏪 部门用户查询：调用钉钉服务",
		logger.String("dept_id", fmt.Sprintf("%d", req.DeptId)),
		logger.String("corp_id", req.CorpId))

	// 参数验证
	if req.DeptId == 0 {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   "DEPARTMENT_USERS模式下dept_id不能为空",
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 获取授权信息
	authInfo, err := grpc.GetAuthInfo(ctx)
	if err != nil {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("获取授权信息失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 获取用户客户端
	userClient, err := s.router.GetUserClient(authInfo.Runtime)
	if err != nil {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("获取平台客户端失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	// 调用钉钉服务GetUserList
	userListReq := &dtalk_user.GetUserListRequest{
		CorpId: req.CorpId,
		AppId:  req.AppId,
		DeptId: int64(req.DeptId),
		Pagination: &dtalk_common.PaginationRequest{
			Page:     page,
			PageSize: pageSize,
		},
		IncludeChildDepts: req.IncludeSubDept,
		OnlyActive:        true,
	}

	userListResp, err := userClient.GetUserList(ctx, userListReq)
	if err != nil {
		logger.Error("调用钉钉服务失败", logger.Error2(err))
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("调用钉钉服务失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	if !userListResp.Base.Success {
		return &unified_user.GetUsersResponse{
			Code:      userListResp.Base.ErrorCode,
			Message:   userListResp.Base.ErrorMessage,
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 转换用户数据
	var users []*unified_user.UserDetail
	if userListResp.Users != nil {
		for _, pbUser := range userListResp.Users {
			// 转换为统一格式
			userDetail := convertPlatformSimpleUserToUnified(pbUser, req.CorpId)
			if userDetail != nil {
				users = append(users, userDetail)
			}
		}
	}

	total := int64(len(users))
	if userListResp.Pagination != nil {
		total = userListResp.Pagination.Total
	}

	logger.Info("✅ 部门用户查询成功",
		logger.String("dept_id", fmt.Sprintf("%d", req.DeptId)),
		logger.Int("user_count", len(users)))

	return &unified_user.GetUsersResponse{
		Code:      0,
		Message:   "成功",
		QueryType: req.QueryType,
		Data: &unified_user.UsersData{
			Users:    users,
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
	}, nil
}

// getUsersAllUsers 处理企业所有用户查询（查DB部门 + 循环调钉钉 + 存DB）
func (s *UnifiedUserService) getUsersAllUsers(ctx context.Context, req *unified_user.GetUsersRequest) (*unified_user.GetUsersResponse, error) {
	logger.Info("🌍 企业所有用户查询：查DB部门 + 循环调钉钉",
		logger.String("corp_id", req.CorpId))

	// 步骤1: 从数据库获取所有部门
	var departments []model.Department
	tempDept := model.Department{}
	tempDept.CorpID = req.CorpId
	tableName := tempDept.TableName()

	err := model.DB.Table(tableName).Where("corp_id = ?", req.CorpId).Find(&departments).Error
	if err != nil {
		logger.Error("查询数据库部门信息失败", logger.Error2(err))
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("查询部门信息失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 如果没有部门，返回空列表
	if len(departments) == 0 {
		logger.Warn("⚠️ 数据库中没有部门信息", logger.String("corp_id", req.CorpId))
		return &unified_user.GetUsersResponse{
			Code:      0,
			Message:   "企业暂无部门信息",
			QueryType: req.QueryType,
			Data: &unified_user.UsersData{
				Users:    []*unified_user.UserDetail{},
				Total:    0,
				Page:     req.Page,
				PageSize: req.PageSize,
			},
		}, nil
	}
	logger.Info("✅ 从数据库获取到部门", logger.Int("dept_count", len(departments)))

	// 步骤2: 获取授权信息
	authInfo, err := grpc.GetAuthInfo(ctx)
	if err != nil {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("获取授权信息失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	userClient, err := s.router.GetUserClient(authInfo.Runtime)
	if err != nil {
		return &unified_user.GetUsersResponse{
			Code:      -1,
			Message:   fmt.Sprintf("获取平台客户端失败: %v", err),
			QueryType: req.QueryType,
			Data:      &unified_user.UsersData{Users: []*unified_user.UserDetail{}, Total: 0},
		}, nil
	}

	// 步骤3: 循环调用钉钉服务获取用户
	allUsers := make(map[string]*model.UserDynamicModel)
	userCount := 0
	duplicateCount := 0    // 统计重复用户数
	totalFetchedCount := 0 // 从钉钉获取的总用户数（含重复）

	for _, dept := range departments {
		deptIDInt64, err := strconv.ParseInt(dept.DeptID, 10, 64)
		if err != nil {
			logger.Warn("部门ID转换失败", logger.String("dept_id", dept.DeptID), logger.Error2(err))
			continue
		}

		userListReq := &dtalk_user.GetUserListRequest{
			CorpId: req.CorpId,
			AppId:  req.AppId,
			DeptId: deptIDInt64,
			Pagination: &dtalk_common.PaginationRequest{
				Page:     1,
				PageSize: 100,
			},
			IncludeChildDepts: false,
			OnlyActive:        req.IsLeave == 0, // 0=在职，1=离职
		}

		userListResp, err := userClient.GetUserList(ctx, userListReq)
		if err != nil {
			logger.Warn("获取部门用户失败", logger.String("dept_id", dept.DeptID), logger.Error2(err))
			continue
		}

		if !userListResp.Base.Success {
			logger.Warn("钉钉服务返回失败", logger.String("dept_id", dept.DeptID))
			continue
		}

		// 转换并存储用户
		if userListResp.Users != nil {
			logger.Info("🔍 开始处理部门用户",
				logger.String("dept_id", dept.DeptID),
				logger.String("dept_name", dept.Name),
				logger.Int("fetched_count", len(userListResp.Users)))

			for _, pbUser := range userListResp.Users {
				totalFetchedCount++

				// 去重
				if _, exists := allUsers[pbUser.UserId]; exists {
					duplicateCount++
					logger.Debug("⚠️ 发现重复用户，跳过",
						logger.String("user_id", pbUser.UserId),
						logger.String("user_name", pbUser.Name))
					continue
				}

				// 调用GetUserInfo获取完整用户信息
				userInfoReq := &dtalk_user.GetUserInfoRequest{
					CorpId:              req.CorpId,
					AppId:               req.AppId,
					UserId:              pbUser.UserId,
					IncludeExtendedInfo: true,
					IncludeDepartments:  true,
					IncludePermissions:  true,
				}

				userInfoResp, err := userClient.GetUserInfo(ctx, userInfoReq)
				if err != nil {
					logger.Warn("获取用户详细信息失败，使用简化信息",
						logger.String("user_id", pbUser.UserId),
						logger.Error2(err))
					// 降级使用简化信息
					dbUser := convertPlatformUserInfoToDB(pbUser, req.CorpId)
					if dbUser != nil {
						allUsers[pbUser.UserId] = dbUser
						userCount++
					}
					continue
				}

				if !userInfoResp.Base.Success || userInfoResp.UserInfo == nil {
					logger.Warn("钉钉服务返回失败或用户信息为空，使用简化信息",
						logger.String("user_id", pbUser.UserId))
					// 降级使用简化信息
					dbUser := convertPlatformUserInfoToDB(pbUser, req.CorpId)
					if dbUser != nil {
						allUsers[pbUser.UserId] = dbUser
						userCount++
					}
					continue
				}

				// 使用完整用户信息转换
				dbUser := convertUserFullInfoToDB(userInfoResp.UserInfo, req.CorpId)
				if dbUser != nil {
					allUsers[pbUser.UserId] = dbUser
					userCount++

					logger.Debug("✅ 添加完整用户信息",
						logger.String("user_id", pbUser.UserId),
						logger.String("user_name", pbUser.Name),
						logger.String("has_extended_info", fmt.Sprintf("%v", userInfoResp.UserInfo.ExtendedInfo != nil)),
						logger.String("has_permission_info", fmt.Sprintf("%v", userInfoResp.UserInfo.PermissionInfo != nil)))

					// 存入数据库
					if err := model.CreateUser(dbUser); err != nil {
						logger.Warn("保存用户到数据库失败",
							logger.String("user_id", pbUser.UserId),
							logger.Error2(err))
					}
				}
			}
		}

		logger.Info("📥 部门用户获取进度",
			logger.String("dept_id", dept.DeptID),
			logger.String("dept_name", dept.Name),
			logger.Int("dept_fetched", len(userListResp.Users)),
			logger.Int("unique_users", userCount),
			logger.Int("duplicates", duplicateCount))
	}

	logger.Info("✅ 所有用户获取完成",
		logger.Int("total_fetched", totalFetchedCount),
		logger.Int("unique_users", userCount),
		logger.Int("duplicate_count", duplicateCount),
		logger.Int("final_user_count", len(allUsers)),
		logger.String("formula", fmt.Sprintf("%d(获取) - %d(重复) = %d(唯一)", totalFetchedCount, duplicateCount, len(allUsers))))

	// 转换为统一格式
	var users []*unified_user.UserDetail
	for _, dbUser := range allUsers {
		users = append(users, convertDBUserToUnified(dbUser))
	}

	return &unified_user.GetUsersResponse{
		Code:      0,
		Message:   "成功",
		QueryType: req.QueryType,
		Data: &unified_user.UsersData{
			Users:    users,
			Total:    int64(len(users)),
			Page:     req.Page,
			PageSize: req.PageSize,
		},
	}, nil
}

// convertPlatformSimpleUserToUnified 将简单用户信息转换为统一格式
func convertPlatformSimpleUserToUnified(pbUser *dtalk_common.DingTalkUserInfo, corpID string) *unified_user.UserDetail {
	if pbUser == nil {
		return nil
	}

	// 转换部门ID列表
	var deptIdList []string
	if pbUser.DeptList != nil {
		for _, dept := range pbUser.DeptList {
			deptIdList = append(deptIdList, fmt.Sprintf("%d", dept.DeptId))
		}
	}

	return &unified_user.UserDetail{
		Id:         0,
		CorpId:     corpID,
		UserId:     pbUser.UserId,
		UnionId:    pbUser.UnionId,
		Username:   pbUser.UserId,
		Name:       pbUser.Name,
		Avatar:     pbUser.Avatar,
		Mobile:     pbUser.Mobile,
		JobNumber:  pbUser.JobNumber,
		Title:      pbUser.Title,
		Email:      pbUser.Email,
		WorkPlace:  pbUser.WorkPlace,
		Remark:     pbUser.Remark,
		DeptIdList: deptIdList,
		HiredDate:  pbUser.HiredDate,
		Active:     convertUserStatus(pbUser.Status),
		Admin:      convertBoolToInt32(pbUser.IsAdmin),
		Boss:       convertBoolToInt32(pbUser.IsBoss),
		Leader:     convertBoolToInt32(pbUser.IsLeader),
		CreatedAt:  pbUser.CreateTime,
		UpdatedAt:  pbUser.UpdateTime,
	}
}

// ============ 辅助函数 ============

// parseUint64 解析字符串为uint64
func parseUint64(s string) uint64 {
	if s == "" {
		return 0
	}
	val, err := strconv.ParseUint(s, 10, 64)
	if err != nil {
		logger.Warn("解析uint64失败",
			logger.String("value", s),
			logger.Error2(err))
		return 0
	}
	return val
}
