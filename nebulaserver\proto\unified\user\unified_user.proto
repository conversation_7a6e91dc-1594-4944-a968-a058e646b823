syntax = "proto3";

package unified.user;

option go_package = "git.eykj.cn/base/golib/proto/unified/user";

// UnifiedUserService 统一用户管理服务
// 其他业务系统通过此服务访问用户信息，本服务负责授权验证和路由到对应平台
service UnifiedUserService {
  // GetUsers 统一用户查询接口（整合三种查询场景）
  rpc GetUsers (GetUsersRequest) returns (GetUsersResponse) {}
}

// ============ 请求消息 ============

// 查询类型枚举
enum QueryType {
  QUERY_TYPE_UNSPECIFIED = 0;  // 未指定（默认）
  SINGLE_USER = 1;              // 查询单个用户
  DEPARTMENT_USERS = 2;         // 查询部门用户
  ALL_USERS = 3;                // 查询企业所有用户
}

// 统一用户查询请求
message GetUsersRequest {
  string app_id = 1;            // 必填：应用ID
  string runtime = 2;           // 可选：平台类型（dtalk/feishu/wework）
  string corp_id = 3;           // 必填：企业ID
  
  QueryType query_type = 4;     // 必填：查询类型
  
  // 以下字段根据 query_type 有选择的使用
  string user_id = 5;           // SINGLE_USER 时必填：用户ID
  uint64 dept_id = 6;           // DEPARTMENT_USERS 时必填：部门ID
  
  // 分页参数（适用于 DEPARTMENT_USERS 和 ALL_USERS）
  int32 page = 7;               // 页码，默认1
  int32 page_size = 8;          // 每页数量，默认20
  
  // 额外参数
  bool include_sub_dept = 9;    // DEPARTMENT_USERS 时可用：是否包含子部门用户
  int32 is_leave = 10;          // ALL_USERS 时可用：是否离职：0-在职，1-离职，-1-全部
}

// 统一用户查询响应
message GetUsersResponse {
  int32 code = 1;               // 状态码：0-成功，其他-失败
  string message = 2;           // 错误信息
  QueryType query_type = 3;     // 查询类型（回显）
  UsersData data = 4;           // 用户数据（统一返回格式）
}

// ============ 数据结构 ============

message UserDetail {
  uint64 id = 1;
  string corp_id = 2;
  string user_id = 3;
  string union_id = 4;
  string username = 5;
  string name = 6;
  string avatar = 7;
  string mobile = 8;
  string telephone = 9;
  string job_number = 10;
  string title = 11;
  string email = 12;
  string org_email = 13;
  string work_place = 14;
  string remark = 15;
  string department_name = 16;
  repeated string dept_id_list = 17;
  int64 hired_date = 18;
  int32 active = 19;
  int32 admin = 20;
  int32 boss = 21;
  int32 leader = 22;
  int32 is_leave = 23;
  int64 is_leave_time = 24;
  int64 created_at = 25;
  int64 updated_at = 26;
  int64 dept_id = 27;  // 🔧 添加缺失的dept_id字段
}

message UsersData {
  repeated UserDetail users = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}






