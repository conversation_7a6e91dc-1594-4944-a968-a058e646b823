// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto

package proto3

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
)

type Enum int32

const (
	Enum_ZERO Enum = 0
)

// Enum value maps for Enum.
var (
	Enum_name = map[int32]string{
		0: "ZERO",
	}
	Enum_value = map[string]int32{
		"ZERO": 0,
	}
)

func (x Enum) Enum() *Enum {
	p := new(Enum)
	*p = x
	return p
}

func (x Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_enumTypes[0].Descriptor()
}

func (Enum) Type() protoreflect.EnumType {
	return &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_enumTypes[0]
}

func (x Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Enum.Descriptor instead.
func (Enum) EnumDescriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescGZIP(), []int{0}
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescGZIP(), []int{0}
}

var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*bool)(nil),
		Field:         1001,
		Name:          "goproto.protoc.extension.proto3.extension_bool",
		Tag:           "varint,1001,opt,name=extension_bool",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         1002,
		Name:          "goproto.protoc.extension.proto3.extension_enum",
		Tag:           "varint,1002,opt,name=extension_enum,enum=goproto.protoc.extension.proto3.Enum",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1003,
		Name:          "goproto.protoc.extension.proto3.extension_int32",
		Tag:           "varint,1003,opt,name=extension_int32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1004,
		Name:          "goproto.protoc.extension.proto3.extension_sint32",
		Tag:           "zigzag32,1004,opt,name=extension_sint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1005,
		Name:          "goproto.protoc.extension.proto3.extension_uint32",
		Tag:           "varint,1005,opt,name=extension_uint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1006,
		Name:          "goproto.protoc.extension.proto3.extension_int64",
		Tag:           "varint,1006,opt,name=extension_int64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1007,
		Name:          "goproto.protoc.extension.proto3.extension_sint64",
		Tag:           "zigzag64,1007,opt,name=extension_sint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1008,
		Name:          "goproto.protoc.extension.proto3.extension_uint64",
		Tag:           "varint,1008,opt,name=extension_uint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int32)(nil),
		Field:         1009,
		Name:          "goproto.protoc.extension.proto3.extension_sfixed32",
		Tag:           "fixed32,1009,opt,name=extension_sfixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         1010,
		Name:          "goproto.protoc.extension.proto3.extension_fixed32",
		Tag:           "fixed32,1010,opt,name=extension_fixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float32)(nil),
		Field:         1011,
		Name:          "goproto.protoc.extension.proto3.extension_float",
		Tag:           "fixed32,1011,opt,name=extension_float",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*int64)(nil),
		Field:         1012,
		Name:          "goproto.protoc.extension.proto3.extension_sfixed64",
		Tag:           "fixed64,1012,opt,name=extension_sfixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         1013,
		Name:          "goproto.protoc.extension.proto3.extension_fixed64",
		Tag:           "fixed64,1013,opt,name=extension_fixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*float64)(nil),
		Field:         1014,
		Name:          "goproto.protoc.extension.proto3.extension_double",
		Tag:           "fixed64,1014,opt,name=extension_double",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1015,
		Name:          "goproto.protoc.extension.proto3.extension_string",
		Tag:           "bytes,1015,opt,name=extension_string",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         1016,
		Name:          "goproto.protoc.extension.proto3.extension_bytes",
		Tag:           "bytes,1016,opt,name=extension_bytes",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: (*Message)(nil),
		Field:         1017,
		Name:          "goproto.protoc.extension.proto3.extension_Message",
		Tag:           "bytes,1017,opt,name=extension_Message",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         2001,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_bool",
		Tag:           "varint,2001,rep,packed,name=repeated_extension_bool",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         2002,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_enum",
		Tag:           "varint,2002,rep,packed,name=repeated_extension_enum,enum=goproto.protoc.extension.proto3.Enum",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         2003,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_int32",
		Tag:           "varint,2003,rep,packed,name=repeated_extension_int32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         2004,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_sint32",
		Tag:           "zigzag32,2004,rep,packed,name=repeated_extension_sint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         2005,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_uint32",
		Tag:           "varint,2005,rep,packed,name=repeated_extension_uint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         2006,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_int64",
		Tag:           "varint,2006,rep,packed,name=repeated_extension_int64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         2007,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_sint64",
		Tag:           "zigzag64,2007,rep,packed,name=repeated_extension_sint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         2008,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_uint64",
		Tag:           "varint,2008,rep,packed,name=repeated_extension_uint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         2009,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_sfixed32",
		Tag:           "fixed32,2009,rep,packed,name=repeated_extension_sfixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         2010,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_fixed32",
		Tag:           "fixed32,2010,rep,packed,name=repeated_extension_fixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         2011,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_float",
		Tag:           "fixed32,2011,rep,packed,name=repeated_extension_float",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         2012,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_sfixed64",
		Tag:           "fixed64,2012,rep,packed,name=repeated_extension_sfixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         2013,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_fixed64",
		Tag:           "fixed64,2013,rep,packed,name=repeated_extension_fixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         2014,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_double",
		Tag:           "fixed64,2014,rep,packed,name=repeated_extension_double",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         2015,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_string",
		Tag:           "bytes,2015,rep,name=repeated_extension_string",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([][]byte)(nil),
		Field:         2016,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_bytes",
		Tag:           "bytes,2016,rep,name=repeated_extension_bytes",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
	{
		ExtendedType:  (*descriptorpb.MessageOptions)(nil),
		ExtensionType: ([]*Message)(nil),
		Field:         2017,
		Name:          "goproto.protoc.extension.proto3.repeated_extension_Message",
		Tag:           "bytes,2017,rep,name=repeated_extension_Message",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/proto3/ext3.proto",
	},
}

// Extension fields to descriptorpb.MessageOptions.
var (
	// optional bool extension_bool = 1001;
	E_ExtensionBool = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[0]
	// optional goproto.protoc.extension.proto3.Enum extension_enum = 1002;
	E_ExtensionEnum = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[1]
	// optional int32 extension_int32 = 1003;
	E_ExtensionInt32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[2]
	// optional sint32 extension_sint32 = 1004;
	E_ExtensionSint32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[3]
	// optional uint32 extension_uint32 = 1005;
	E_ExtensionUint32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[4]
	// optional int64 extension_int64 = 1006;
	E_ExtensionInt64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[5]
	// optional sint64 extension_sint64 = 1007;
	E_ExtensionSint64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[6]
	// optional uint64 extension_uint64 = 1008;
	E_ExtensionUint64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[7]
	// optional sfixed32 extension_sfixed32 = 1009;
	E_ExtensionSfixed32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[8]
	// optional fixed32 extension_fixed32 = 1010;
	E_ExtensionFixed32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[9]
	// optional float extension_float = 1011;
	E_ExtensionFloat = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[10]
	// optional sfixed64 extension_sfixed64 = 1012;
	E_ExtensionSfixed64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[11]
	// optional fixed64 extension_fixed64 = 1013;
	E_ExtensionFixed64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[12]
	// optional double extension_double = 1014;
	E_ExtensionDouble = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[13]
	// optional string extension_string = 1015;
	E_ExtensionString = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[14]
	// optional bytes extension_bytes = 1016;
	E_ExtensionBytes = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[15]
	// optional goproto.protoc.extension.proto3.Message extension_Message = 1017;
	E_Extension_Message = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[16]
	// repeated bool repeated_extension_bool = 2001;
	E_RepeatedExtensionBool = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[17]
	// repeated goproto.protoc.extension.proto3.Enum repeated_extension_enum = 2002;
	E_RepeatedExtensionEnum = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[18]
	// repeated int32 repeated_extension_int32 = 2003;
	E_RepeatedExtensionInt32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[19]
	// repeated sint32 repeated_extension_sint32 = 2004;
	E_RepeatedExtensionSint32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[20]
	// repeated uint32 repeated_extension_uint32 = 2005;
	E_RepeatedExtensionUint32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[21]
	// repeated int64 repeated_extension_int64 = 2006;
	E_RepeatedExtensionInt64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[22]
	// repeated sint64 repeated_extension_sint64 = 2007;
	E_RepeatedExtensionSint64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[23]
	// repeated uint64 repeated_extension_uint64 = 2008;
	E_RepeatedExtensionUint64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[24]
	// repeated sfixed32 repeated_extension_sfixed32 = 2009;
	E_RepeatedExtensionSfixed32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[25]
	// repeated fixed32 repeated_extension_fixed32 = 2010;
	E_RepeatedExtensionFixed32 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[26]
	// repeated float repeated_extension_float = 2011;
	E_RepeatedExtensionFloat = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[27]
	// repeated sfixed64 repeated_extension_sfixed64 = 2012;
	E_RepeatedExtensionSfixed64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[28]
	// repeated fixed64 repeated_extension_fixed64 = 2013;
	E_RepeatedExtensionFixed64 = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[29]
	// repeated double repeated_extension_double = 2014;
	E_RepeatedExtensionDouble = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[30]
	// repeated string repeated_extension_string = 2015;
	E_RepeatedExtensionString = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[31]
	// repeated bytes repeated_extension_bytes = 2016;
	E_RepeatedExtensionBytes = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[32]
	// repeated goproto.protoc.extension.proto3.Message repeated_extension_Message = 2017;
	E_RepeatedExtension_Message = &file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes[33]
)

var File_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto protoreflect.FileDescriptor

var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDesc = []byte{
	0x0a, 0x37, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2f, 0x65,
	0x78, 0x74, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2a, 0x10, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x08, 0x0a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x3a, 0x47, 0x0a, 0x0e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe9, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f,
	0x6f, 0x6c, 0x3a, 0x6e, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xea, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e,
	0x75, 0x6d, 0x3a, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xeb, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4b, 0x0a,
	0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xec, 0x07, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x4b, 0x0a, 0x10, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xed, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xee, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x3a, 0x4b, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xef, 0x07, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a,
	0x4b, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf0, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x4f, 0x0a, 0x12,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xf1, 0x07, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x11, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x4d, 0x0a,
	0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xf2, 0x07, 0x20, 0x01, 0x28, 0x07, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x49, 0x0a, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xf3, 0x07, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x4f, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf4,
	0x07, 0x20, 0x01, 0x28, 0x10, 0x52, 0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x4d, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf5,
	0x07, 0x20, 0x01, 0x28, 0x06, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x4b, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf6, 0x07, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x3a, 0x4b, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf7, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x3a, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xf8, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x77, 0x0a, 0x11,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xf9, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x58, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xd1, 0x0f, 0x20, 0x03, 0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x6f, 0x6c, 0x3a,
	0x7f, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd2, 0x0f, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x3a, 0x5a, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd3, 0x0f,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x5c, 0x0a, 0x19,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd4, 0x0f, 0x20, 0x03, 0x28,
	0x11, 0x52, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x5c, 0x0a, 0x19, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd5, 0x0f, 0x20, 0x03, 0x28, 0x0d, 0x52,
	0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x5a, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xd6, 0x0f, 0x20, 0x03, 0x28, 0x03, 0x52, 0x16, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x5c, 0x0a, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0xd7, 0x0f, 0x20, 0x03, 0x28, 0x12, 0x52, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x3a, 0x5c, 0x0a, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xd8, 0x0f, 0x20, 0x03, 0x28, 0x04, 0x52, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x3a, 0x60, 0x0a, 0x1b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0xd9, 0x0f, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x3a, 0x5e, 0x0a, 0x1a, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0xda, 0x0f, 0x20, 0x03, 0x28, 0x07, 0x52, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x3a, 0x5a, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xdb, 0x0f, 0x20, 0x03, 0x28, 0x02, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x60,
	0x0a, 0x1b, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdc,
	0x0f, 0x20, 0x03, 0x28, 0x10, 0x52, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x3a, 0x5e, 0x0a, 0x1a, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0xdd, 0x0f, 0x20, 0x03, 0x28, 0x06, 0x52, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x3a, 0x5c, 0x0a, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xde,
	0x0f, 0x20, 0x03, 0x28, 0x01, 0x52, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x3a, 0x5c,
	0x0a, 0x19, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xdf, 0x0f, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x5a, 0x0a, 0x18,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe0, 0x0f, 0x20, 0x03, 0x28, 0x0c,
	0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x88, 0x01, 0x0a, 0x1a, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xe1, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x49, 0x5a, 0x47, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f,
	0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescOnce sync.Once
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescData = file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDesc
)

func file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescGZIP() []byte {
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescOnce.Do(func() {
		file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescData)
	})
	return file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDescData
}

var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_goTypes = []interface{}{
	(Enum)(0),                           // 0: goproto.protoc.extension.proto3.Enum
	(*Message)(nil),                     // 1: goproto.protoc.extension.proto3.Message
	(*descriptorpb.MessageOptions)(nil), // 2: google.protobuf.MessageOptions
}
var file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_depIdxs = []int32{
	2,  // 0: goproto.protoc.extension.proto3.extension_bool:extendee -> google.protobuf.MessageOptions
	2,  // 1: goproto.protoc.extension.proto3.extension_enum:extendee -> google.protobuf.MessageOptions
	2,  // 2: goproto.protoc.extension.proto3.extension_int32:extendee -> google.protobuf.MessageOptions
	2,  // 3: goproto.protoc.extension.proto3.extension_sint32:extendee -> google.protobuf.MessageOptions
	2,  // 4: goproto.protoc.extension.proto3.extension_uint32:extendee -> google.protobuf.MessageOptions
	2,  // 5: goproto.protoc.extension.proto3.extension_int64:extendee -> google.protobuf.MessageOptions
	2,  // 6: goproto.protoc.extension.proto3.extension_sint64:extendee -> google.protobuf.MessageOptions
	2,  // 7: goproto.protoc.extension.proto3.extension_uint64:extendee -> google.protobuf.MessageOptions
	2,  // 8: goproto.protoc.extension.proto3.extension_sfixed32:extendee -> google.protobuf.MessageOptions
	2,  // 9: goproto.protoc.extension.proto3.extension_fixed32:extendee -> google.protobuf.MessageOptions
	2,  // 10: goproto.protoc.extension.proto3.extension_float:extendee -> google.protobuf.MessageOptions
	2,  // 11: goproto.protoc.extension.proto3.extension_sfixed64:extendee -> google.protobuf.MessageOptions
	2,  // 12: goproto.protoc.extension.proto3.extension_fixed64:extendee -> google.protobuf.MessageOptions
	2,  // 13: goproto.protoc.extension.proto3.extension_double:extendee -> google.protobuf.MessageOptions
	2,  // 14: goproto.protoc.extension.proto3.extension_string:extendee -> google.protobuf.MessageOptions
	2,  // 15: goproto.protoc.extension.proto3.extension_bytes:extendee -> google.protobuf.MessageOptions
	2,  // 16: goproto.protoc.extension.proto3.extension_Message:extendee -> google.protobuf.MessageOptions
	2,  // 17: goproto.protoc.extension.proto3.repeated_extension_bool:extendee -> google.protobuf.MessageOptions
	2,  // 18: goproto.protoc.extension.proto3.repeated_extension_enum:extendee -> google.protobuf.MessageOptions
	2,  // 19: goproto.protoc.extension.proto3.repeated_extension_int32:extendee -> google.protobuf.MessageOptions
	2,  // 20: goproto.protoc.extension.proto3.repeated_extension_sint32:extendee -> google.protobuf.MessageOptions
	2,  // 21: goproto.protoc.extension.proto3.repeated_extension_uint32:extendee -> google.protobuf.MessageOptions
	2,  // 22: goproto.protoc.extension.proto3.repeated_extension_int64:extendee -> google.protobuf.MessageOptions
	2,  // 23: goproto.protoc.extension.proto3.repeated_extension_sint64:extendee -> google.protobuf.MessageOptions
	2,  // 24: goproto.protoc.extension.proto3.repeated_extension_uint64:extendee -> google.protobuf.MessageOptions
	2,  // 25: goproto.protoc.extension.proto3.repeated_extension_sfixed32:extendee -> google.protobuf.MessageOptions
	2,  // 26: goproto.protoc.extension.proto3.repeated_extension_fixed32:extendee -> google.protobuf.MessageOptions
	2,  // 27: goproto.protoc.extension.proto3.repeated_extension_float:extendee -> google.protobuf.MessageOptions
	2,  // 28: goproto.protoc.extension.proto3.repeated_extension_sfixed64:extendee -> google.protobuf.MessageOptions
	2,  // 29: goproto.protoc.extension.proto3.repeated_extension_fixed64:extendee -> google.protobuf.MessageOptions
	2,  // 30: goproto.protoc.extension.proto3.repeated_extension_double:extendee -> google.protobuf.MessageOptions
	2,  // 31: goproto.protoc.extension.proto3.repeated_extension_string:extendee -> google.protobuf.MessageOptions
	2,  // 32: goproto.protoc.extension.proto3.repeated_extension_bytes:extendee -> google.protobuf.MessageOptions
	2,  // 33: goproto.protoc.extension.proto3.repeated_extension_Message:extendee -> google.protobuf.MessageOptions
	0,  // 34: goproto.protoc.extension.proto3.extension_enum:type_name -> goproto.protoc.extension.proto3.Enum
	1,  // 35: goproto.protoc.extension.proto3.extension_Message:type_name -> goproto.protoc.extension.proto3.Message
	0,  // 36: goproto.protoc.extension.proto3.repeated_extension_enum:type_name -> goproto.protoc.extension.proto3.Enum
	1,  // 37: goproto.protoc.extension.proto3.repeated_extension_Message:type_name -> goproto.protoc.extension.proto3.Message
	38, // [38:38] is the sub-list for method output_type
	38, // [38:38] is the sub-list for method input_type
	34, // [34:38] is the sub-list for extension type_name
	0,  // [0:34] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_init() }
func file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_init() {
	if File_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 34,
			NumServices:   0,
		},
		GoTypes:           file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_goTypes,
		DependencyIndexes: file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_depIdxs,
		EnumInfos:         file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_enumTypes,
		MessageInfos:      file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_msgTypes,
		ExtensionInfos:    file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_extTypes,
	}.Build()
	File_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto = out.File
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_rawDesc = nil
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_goTypes = nil
	file_cmd_protoc_gen_go_testdata_extensions_proto3_ext3_proto_depIdxs = nil
}
