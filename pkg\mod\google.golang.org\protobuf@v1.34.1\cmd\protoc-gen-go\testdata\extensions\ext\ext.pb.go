// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cmd/protoc-gen-go/testdata/extensions/ext/ext.proto

package ext

import (
	base "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/base"
	extra "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/extensions/extra"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type Enum int32

const (
	Enum_ZERO Enum = 0
)

// Enum value maps for Enum.
var (
	Enum_name = map[int32]string{
		0: "ZERO",
	}
	Enum_value = map[string]int32{
		"ZERO": 0,
	}
)

func (x Enum) Enum() *Enum {
	p := new(Enum)
	*p = x
	return p
}

func (x Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_enumTypes[0].Descriptor()
}

func (Enum) Type() protoreflect.EnumType {
	return &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_enumTypes[0]
}

func (x Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Enum) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Enum(num)
	return nil
}

// Deprecated: Use Enum.Descriptor instead.
func (Enum) EnumDescriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{0}
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data" json:"data,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{0}
}

func (x *Message) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type ExtensionGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExtensionGroup *string `protobuf:"bytes,120,opt,name=extension_group,json=extensionGroup" json:"extension_group,omitempty"`
}

func (x *ExtensionGroup) Reset() {
	*x = ExtensionGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtensionGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtensionGroup) ProtoMessage() {}

func (x *ExtensionGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtensionGroup.ProtoReflect.Descriptor instead.
func (*ExtensionGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{1}
}

func (x *ExtensionGroup) GetExtensionGroup() string {
	if x != nil && x.ExtensionGroup != nil {
		return *x.ExtensionGroup
	}
	return ""
}

// Extend in the scope of another type.
type ExtendingMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExtendingMessage) Reset() {
	*x = ExtendingMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendingMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendingMessage) ProtoMessage() {}

func (x *ExtendingMessage) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendingMessage.ProtoReflect.Descriptor instead.
func (*ExtendingMessage) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{2}
}

type RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RepeatedXGroup []string `protobuf:"bytes,319,rep,name=repeated_x_group,json=repeatedXGroup" json:"repeated_x_group,omitempty"`
}

func (x *RepeatedGroup) Reset() {
	*x = RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RepeatedGroup) ProtoMessage() {}

func (x *RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RepeatedGroup.ProtoReflect.Descriptor instead.
func (*RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{3}
}

func (x *RepeatedGroup) GetRepeatedXGroup() []string {
	if x != nil {
		return x.RepeatedXGroup
	}
	return nil
}

// An extension of an extension.
type Extendable struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields
}

func (x *Extendable) Reset() {
	*x = Extendable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Extendable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Extendable) ProtoMessage() {}

func (x *Extendable) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Extendable.ProtoReflect.Descriptor instead.
func (*Extendable) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{4}
}

// Message set wire format.
type MessageSetWireFormatExtension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MessageSetWireFormatExtension) Reset() {
	*x = MessageSetWireFormatExtension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageSetWireFormatExtension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageSetWireFormatExtension) ProtoMessage() {}

func (x *MessageSetWireFormatExtension) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageSetWireFormatExtension.ProtoReflect.Descriptor instead.
func (*MessageSetWireFormatExtension) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{5}
}

type Message_M struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Message_M) Reset() {
	*x = Message_M{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message_M) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message_M) ProtoMessage() {}

func (x *Message_M) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message_M.ProtoReflect.Descriptor instead.
func (*Message_M) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{0, 0}
}

type ExtendingMessage_ExtendingMessageSubmessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ExtendingMessage_ExtendingMessageSubmessage) Reset() {
	*x = ExtendingMessage_ExtendingMessageSubmessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendingMessage_ExtendingMessageSubmessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendingMessage_ExtendingMessageSubmessage) ProtoMessage() {}

func (x *ExtendingMessage_ExtendingMessageSubmessage) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendingMessage_ExtendingMessageSubmessage.ProtoReflect.Descriptor instead.
func (*ExtendingMessage_ExtendingMessageSubmessage) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP(), []int{2, 0}
}

var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*bool)(nil),
		Field:         101,
		Name:          "goproto.protoc.extension.ext.extension_bool",
		Tag:           "varint,101,opt,name=extension_bool",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*Enum)(nil),
		Field:         102,
		Name:          "goproto.protoc.extension.ext.extension_enum",
		Tag:           "varint,102,opt,name=extension_enum,enum=goproto.protoc.extension.ext.Enum",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int32)(nil),
		Field:         103,
		Name:          "goproto.protoc.extension.ext.extension_int32",
		Tag:           "varint,103,opt,name=extension_int32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int32)(nil),
		Field:         104,
		Name:          "goproto.protoc.extension.ext.extension_sint32",
		Tag:           "zigzag32,104,opt,name=extension_sint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         105,
		Name:          "goproto.protoc.extension.ext.extension_uint32",
		Tag:           "varint,105,opt,name=extension_uint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int64)(nil),
		Field:         106,
		Name:          "goproto.protoc.extension.ext.extension_int64",
		Tag:           "varint,106,opt,name=extension_int64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int64)(nil),
		Field:         107,
		Name:          "goproto.protoc.extension.ext.extension_sint64",
		Tag:           "zigzag64,107,opt,name=extension_sint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         108,
		Name:          "goproto.protoc.extension.ext.extension_uint64",
		Tag:           "varint,108,opt,name=extension_uint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int32)(nil),
		Field:         109,
		Name:          "goproto.protoc.extension.ext.extension_sfixed32",
		Tag:           "fixed32,109,opt,name=extension_sfixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*uint32)(nil),
		Field:         110,
		Name:          "goproto.protoc.extension.ext.extension_fixed32",
		Tag:           "fixed32,110,opt,name=extension_fixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*float32)(nil),
		Field:         111,
		Name:          "goproto.protoc.extension.ext.extension_float",
		Tag:           "fixed32,111,opt,name=extension_float",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*int64)(nil),
		Field:         112,
		Name:          "goproto.protoc.extension.ext.extension_sfixed64",
		Tag:           "fixed64,112,opt,name=extension_sfixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*uint64)(nil),
		Field:         113,
		Name:          "goproto.protoc.extension.ext.extension_fixed64",
		Tag:           "fixed64,113,opt,name=extension_fixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*float64)(nil),
		Field:         114,
		Name:          "goproto.protoc.extension.ext.extension_double",
		Tag:           "fixed64,114,opt,name=extension_double",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*string)(nil),
		Field:         115,
		Name:          "goproto.protoc.extension.ext.extension_string",
		Tag:           "bytes,115,opt,name=extension_string",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]byte)(nil),
		Field:         116,
		Name:          "goproto.protoc.extension.ext.extension_bytes",
		Tag:           "bytes,116,opt,name=extension_bytes",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*Message)(nil),
		Field:         117,
		Name:          "goproto.protoc.extension.ext.extension_Message",
		Tag:           "bytes,117,opt,name=extension_Message",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*Message_M)(nil),
		Field:         118,
		Name:          "goproto.protoc.extension.ext.extension_MessageM",
		Tag:           "bytes,118,opt,name=extension_MessageM",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*ExtensionGroup)(nil),
		Field:         119,
		Name:          "goproto.protoc.extension.ext.extensiongroup",
		Tag:           "group,119,opt,name=ExtensionGroup",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*extra.ExtraMessage)(nil),
		Field:         9,
		Name:          "goproto.protoc.extension.ext.extra_message",
		Tag:           "bytes,9,opt,name=extra_message",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]bool)(nil),
		Field:         301,
		Name:          "goproto.protoc.extension.ext.repeated_x_bool",
		Tag:           "varint,301,rep,name=repeated_x_bool",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]Enum)(nil),
		Field:         302,
		Name:          "goproto.protoc.extension.ext.repeated_x_enum",
		Tag:           "varint,302,rep,name=repeated_x_enum,enum=goproto.protoc.extension.ext.Enum",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         303,
		Name:          "goproto.protoc.extension.ext.repeated_x_int32",
		Tag:           "varint,303,rep,name=repeated_x_int32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         304,
		Name:          "goproto.protoc.extension.ext.repeated_x_sint32",
		Tag:           "zigzag32,304,rep,name=repeated_x_sint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         305,
		Name:          "goproto.protoc.extension.ext.repeated_x_uint32",
		Tag:           "varint,305,rep,name=repeated_x_uint32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         306,
		Name:          "goproto.protoc.extension.ext.repeated_x_int64",
		Tag:           "varint,306,rep,name=repeated_x_int64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         307,
		Name:          "goproto.protoc.extension.ext.repeated_x_sint64",
		Tag:           "zigzag64,307,rep,name=repeated_x_sint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         308,
		Name:          "goproto.protoc.extension.ext.repeated_x_uint64",
		Tag:           "varint,308,rep,name=repeated_x_uint64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int32)(nil),
		Field:         309,
		Name:          "goproto.protoc.extension.ext.repeated_x_sfixed32",
		Tag:           "fixed32,309,rep,name=repeated_x_sfixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]uint32)(nil),
		Field:         310,
		Name:          "goproto.protoc.extension.ext.repeated_x_fixed32",
		Tag:           "fixed32,310,rep,name=repeated_x_fixed32",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]float32)(nil),
		Field:         311,
		Name:          "goproto.protoc.extension.ext.repeated_x_float",
		Tag:           "fixed32,311,rep,name=repeated_x_float",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]int64)(nil),
		Field:         312,
		Name:          "goproto.protoc.extension.ext.repeated_x_sfixed64",
		Tag:           "fixed64,312,rep,name=repeated_x_sfixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]uint64)(nil),
		Field:         313,
		Name:          "goproto.protoc.extension.ext.repeated_x_fixed64",
		Tag:           "fixed64,313,rep,name=repeated_x_fixed64",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]float64)(nil),
		Field:         314,
		Name:          "goproto.protoc.extension.ext.repeated_x_double",
		Tag:           "fixed64,314,rep,name=repeated_x_double",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]string)(nil),
		Field:         315,
		Name:          "goproto.protoc.extension.ext.repeated_x_string",
		Tag:           "bytes,315,rep,name=repeated_x_string",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([][]byte)(nil),
		Field:         316,
		Name:          "goproto.protoc.extension.ext.repeated_x_bytes",
		Tag:           "bytes,316,rep,name=repeated_x_bytes",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]*Message)(nil),
		Field:         317,
		Name:          "goproto.protoc.extension.ext.repeated_x_Message",
		Tag:           "bytes,317,rep,name=repeated_x_Message",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: ([]*RepeatedGroup)(nil),
		Field:         318,
		Name:          "goproto.protoc.extension.ext.repeatedgroup",
		Tag:           "group,318,rep,name=RepeatedGroup",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*Extendable)(nil),
		Field:         400,
		Name:          "goproto.protoc.extension.ext.extendable_field",
		Tag:           "bytes,400,opt,name=extendable_field",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*Extendable)(nil),
		ExtensionType: (*string)(nil),
		Field:         1,
		Name:          "goproto.protoc.extension.ext.extendable_string_field",
		Tag:           "bytes,1,opt,name=extendable_string_field",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.MessageSetWireFormatMessage)(nil),
		ExtensionType: (*MessageSetWireFormatExtension)(nil),
		Field:         101,
		Name:          "goproto.protoc.extension.ext.message_set_extension",
		Tag:           "bytes,101,opt,name=message_set_extension",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*string)(nil),
		Field:         200,
		Name:          "goproto.protoc.extension.ext.ExtendingMessage.extending_message_string",
		Tag:           "bytes,200,opt,name=extending_message_string",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.BaseMessage)(nil),
		ExtensionType: (*ExtendingMessage_ExtendingMessageSubmessage)(nil),
		Field:         201,
		Name:          "goproto.protoc.extension.ext.ExtendingMessage.extending_message_submessage",
		Tag:           "bytes,201,opt,name=extending_message_submessage",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
	{
		ExtendedType:  (*base.MessageSetWireFormatMessage)(nil),
		ExtensionType: (*MessageSetWireFormatExtension)(nil),
		Field:         100,
		Name:          "goproto.protoc.extension.ext.MessageSetWireFormatExtension.message_set_extension",
		Tag:           "bytes,100,opt,name=message_set_extension",
		Filename:      "cmd/protoc-gen-go/testdata/extensions/ext/ext.proto",
	},
}

// Extension fields to base.BaseMessage.
var (
	// optional bool extension_bool = 101;
	E_ExtensionBool = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[0]
	// optional goproto.protoc.extension.ext.Enum extension_enum = 102;
	E_ExtensionEnum = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[1]
	// optional int32 extension_int32 = 103;
	E_ExtensionInt32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[2]
	// optional sint32 extension_sint32 = 104;
	E_ExtensionSint32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[3]
	// optional uint32 extension_uint32 = 105;
	E_ExtensionUint32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[4]
	// optional int64 extension_int64 = 106;
	E_ExtensionInt64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[5]
	// optional sint64 extension_sint64 = 107;
	E_ExtensionSint64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[6]
	// optional uint64 extension_uint64 = 108;
	E_ExtensionUint64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[7]
	// optional sfixed32 extension_sfixed32 = 109;
	E_ExtensionSfixed32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[8]
	// optional fixed32 extension_fixed32 = 110;
	E_ExtensionFixed32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[9]
	// optional float extension_float = 111;
	E_ExtensionFloat = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[10]
	// optional sfixed64 extension_sfixed64 = 112;
	E_ExtensionSfixed64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[11]
	// optional fixed64 extension_fixed64 = 113;
	E_ExtensionFixed64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[12]
	// optional double extension_double = 114;
	E_ExtensionDouble = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[13]
	// optional string extension_string = 115;
	E_ExtensionString = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[14]
	// optional bytes extension_bytes = 116;
	E_ExtensionBytes = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[15]
	// optional goproto.protoc.extension.ext.Message extension_Message = 117;
	E_Extension_Message = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[16]
	// optional goproto.protoc.extension.ext.Message.M extension_MessageM = 118;
	E_Extension_MessageM = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[17]
	// optional goproto.protoc.extension.ext.ExtensionGroup extensiongroup = 119;
	E_Extensiongroup = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[18]
	// optional goproto.protoc.extension.extra.ExtraMessage extra_message = 9;
	E_ExtraMessage = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[19]
	// repeated bool repeated_x_bool = 301;
	E_RepeatedXBool = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[20]
	// repeated goproto.protoc.extension.ext.Enum repeated_x_enum = 302;
	E_RepeatedXEnum = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[21]
	// repeated int32 repeated_x_int32 = 303;
	E_RepeatedXInt32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[22]
	// repeated sint32 repeated_x_sint32 = 304;
	E_RepeatedXSint32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[23]
	// repeated uint32 repeated_x_uint32 = 305;
	E_RepeatedXUint32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[24]
	// repeated int64 repeated_x_int64 = 306;
	E_RepeatedXInt64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[25]
	// repeated sint64 repeated_x_sint64 = 307;
	E_RepeatedXSint64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[26]
	// repeated uint64 repeated_x_uint64 = 308;
	E_RepeatedXUint64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[27]
	// repeated sfixed32 repeated_x_sfixed32 = 309;
	E_RepeatedXSfixed32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[28]
	// repeated fixed32 repeated_x_fixed32 = 310;
	E_RepeatedXFixed32 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[29]
	// repeated float repeated_x_float = 311;
	E_RepeatedXFloat = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[30]
	// repeated sfixed64 repeated_x_sfixed64 = 312;
	E_RepeatedXSfixed64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[31]
	// repeated fixed64 repeated_x_fixed64 = 313;
	E_RepeatedXFixed64 = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[32]
	// repeated double repeated_x_double = 314;
	E_RepeatedXDouble = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[33]
	// repeated string repeated_x_string = 315;
	E_RepeatedXString = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[34]
	// repeated bytes repeated_x_bytes = 316;
	E_RepeatedXBytes = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[35]
	// repeated goproto.protoc.extension.ext.Message repeated_x_Message = 317;
	E_RepeatedX_Message = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[36]
	// repeated goproto.protoc.extension.ext.RepeatedGroup repeatedgroup = 318;
	E_Repeatedgroup = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[37]
	// optional goproto.protoc.extension.ext.Extendable extendable_field = 400;
	E_ExtendableField = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[38]
	// optional string extending_message_string = 200;
	E_ExtendingMessage_ExtendingMessageString = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[41]
	// optional goproto.protoc.extension.ext.ExtendingMessage.ExtendingMessageSubmessage extending_message_submessage = 201;
	E_ExtendingMessage_ExtendingMessageSubmessage = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[42]
)

// Extension fields to Extendable.
var (
	// optional string extendable_string_field = 1;
	E_ExtendableStringField = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[39]
)

// Extension fields to base.MessageSetWireFormatMessage.
var (
	// optional goproto.protoc.extension.ext.MessageSetWireFormatExtension message_set_extension = 101;
	E_MessageSetExtension = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[40]
	// optional goproto.protoc.extension.ext.MessageSetWireFormatExtension message_set_extension = 100;
	E_MessageSetWireFormatExtension_MessageSetExtension = &file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes[43]
)

var File_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto protoreflect.FileDescriptor

var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDesc = []byte{
	0x0a, 0x33, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x65, 0x78, 0x74, 0x2f, 0x65, 0x78, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x65, 0x78, 0x74, 0x1a, 0x35, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d,
	0x67, 0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2f,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x63, 0x6d, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65,
	0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x22, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x03, 0x0a, 0x01, 0x4d, 0x22, 0x39, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x78, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x22, 0xd2, 0x02, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x1c, 0x0a, 0x1a, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x65, 0x0a, 0x18, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xc8, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x32, 0xb8, 0x01, 0x0a,
	0x1c, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61,
	0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xc9, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x49, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x53, 0x75, 0x62, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x1a, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3a, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xbf, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x22, 0x16, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x6c,
	0x65, 0x2a, 0x08, 0x08, 0x01, 0x10, 0x80, 0x80, 0x80, 0x80, 0x02, 0x22, 0xcd, 0x01, 0x0a, 0x1d,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x57, 0x69, 0x72, 0x65, 0x46, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0xab, 0x01,
	0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x57, 0x69, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x57, 0x69, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2a, 0x10, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x3a, 0x51, 0x0a,
	0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12,
	0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x6f, 0x6c,
	0x3a, 0x75, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x66,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x65, 0x78, 0x74, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x53, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x67, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x68, 0x20, 0x01,
	0x28, 0x11, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x69, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x53, 0x0a, 0x0f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61,
	0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x3a,
	0x55, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x6b, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x55, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x6c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x59, 0x0a,
	0x12, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x6d, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x57, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61,
	0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x07, 0x52,
	0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x3a, 0x53, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66,
	0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x6f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x3a, 0x59, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x10, 0x52, 0x11,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x3a, 0x57, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x71, 0x20, 0x01, 0x28, 0x06, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x55, 0x0a, 0x10, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2a,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x72, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x3a, 0x55, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x73, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x53, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x74, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x74, 0x65, 0x73, 0x3a, 0x7e, 0x0a,
	0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x75,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x65, 0x78, 0x74, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x10, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x82, 0x01,
	0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4d, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x76, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x52,
	0x11, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4d, 0x3a, 0x80, 0x01, 0x0a, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x77, 0x20, 0x01, 0x28, 0x0a, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x3a, 0x7d, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x72, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x72, 0x61, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x3a, 0x53, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x78, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0xad, 0x02, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x58, 0x42, 0x6f, 0x6f, 0x6c, 0x3a, 0x77, 0x0a, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x12, 0x2a, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xae, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x22, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x45, 0x6e,
	0x75, 0x6d, 0x3a, 0x55, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0xaf, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x58, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x57, 0x0a, 0x11, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb0, 0x02, 0x20, 0x03, 0x28,
	0x11, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x3a, 0x57, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0xb1, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x58, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x10, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb2, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x3a, 0x57, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0xb3, 0x02, 0x20, 0x03, 0x28, 0x12, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x58, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x3a, 0x57, 0x0a, 0x11, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb4, 0x02, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x55, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x3a, 0x5b, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x78, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb5, 0x02, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x11,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x3a, 0x59, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0xb6, 0x02, 0x20, 0x03, 0x28, 0x07, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x58, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x3a, 0x55, 0x0a, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb7, 0x02, 0x20,
	0x03, 0x28, 0x02, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x46, 0x6c,
	0x6f, 0x61, 0x74, 0x3a, 0x5b, 0x0a, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x78, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xb8, 0x02, 0x20, 0x03, 0x28, 0x10, 0x52, 0x11, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x3a, 0x59, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0xb9, 0x02, 0x20, 0x03, 0x28, 0x06, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x58, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x3a, 0x57, 0x0a, 0x11, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xba, 0x02, 0x20,
	0x03, 0x28, 0x01, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x3a, 0x57, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x78, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xbb, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x3a, 0x55, 0x0a,
	0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x78, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xbc, 0x02,
	0x20, 0x03, 0x28, 0x0c, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x3a, 0x80, 0x01, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x78, 0x5f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xbd, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x58,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x7e, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0xbe, 0x02, 0x20, 0x03, 0x28, 0x0a, 0x32, 0x2b, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x3a, 0x80, 0x01, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x2a, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x90, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x3a, 0x60, 0x0a, 0x17, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x65, 0x78, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x3a, 0xab, 0x01, 0x0a,
	0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x74, 0x57, 0x69, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x78, 0x74, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53,
	0x65, 0x74, 0x57, 0x69, 0x72, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x65,
	0x74, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x46, 0x5a, 0x44, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64,
	0x61, 0x74, 0x61, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x65,
	0x78, 0x74,
}

var (
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescOnce sync.Once
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescData = file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDesc
)

func file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescGZIP() []byte {
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescOnce.Do(func() {
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescData)
	})
	return file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDescData
}

var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_goTypes = []interface{}{
	(Enum)(0),                             // 0: goproto.protoc.extension.ext.Enum
	(*Message)(nil),                       // 1: goproto.protoc.extension.ext.Message
	(*ExtensionGroup)(nil),                // 2: goproto.protoc.extension.ext.ExtensionGroup
	(*ExtendingMessage)(nil),              // 3: goproto.protoc.extension.ext.ExtendingMessage
	(*RepeatedGroup)(nil),                 // 4: goproto.protoc.extension.ext.RepeatedGroup
	(*Extendable)(nil),                    // 5: goproto.protoc.extension.ext.Extendable
	(*MessageSetWireFormatExtension)(nil), // 6: goproto.protoc.extension.ext.MessageSetWireFormatExtension
	(*Message_M)(nil),                     // 7: goproto.protoc.extension.ext.Message.M
	(*ExtendingMessage_ExtendingMessageSubmessage)(nil), // 8: goproto.protoc.extension.ext.ExtendingMessage.ExtendingMessageSubmessage
	(*base.BaseMessage)(nil),                            // 9: goproto.protoc.extension.base.BaseMessage
	(*base.MessageSetWireFormatMessage)(nil),            // 10: goproto.protoc.extension.base.MessageSetWireFormatMessage
	(*extra.ExtraMessage)(nil),                          // 11: goproto.protoc.extension.extra.ExtraMessage
}
var file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_depIdxs = []int32{
	9,  // 0: goproto.protoc.extension.ext.extension_bool:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 1: goproto.protoc.extension.ext.extension_enum:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 2: goproto.protoc.extension.ext.extension_int32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 3: goproto.protoc.extension.ext.extension_sint32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 4: goproto.protoc.extension.ext.extension_uint32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 5: goproto.protoc.extension.ext.extension_int64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 6: goproto.protoc.extension.ext.extension_sint64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 7: goproto.protoc.extension.ext.extension_uint64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 8: goproto.protoc.extension.ext.extension_sfixed32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 9: goproto.protoc.extension.ext.extension_fixed32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 10: goproto.protoc.extension.ext.extension_float:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 11: goproto.protoc.extension.ext.extension_sfixed64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 12: goproto.protoc.extension.ext.extension_fixed64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 13: goproto.protoc.extension.ext.extension_double:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 14: goproto.protoc.extension.ext.extension_string:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 15: goproto.protoc.extension.ext.extension_bytes:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 16: goproto.protoc.extension.ext.extension_Message:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 17: goproto.protoc.extension.ext.extension_MessageM:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 18: goproto.protoc.extension.ext.extensiongroup:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 19: goproto.protoc.extension.ext.extra_message:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 20: goproto.protoc.extension.ext.repeated_x_bool:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 21: goproto.protoc.extension.ext.repeated_x_enum:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 22: goproto.protoc.extension.ext.repeated_x_int32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 23: goproto.protoc.extension.ext.repeated_x_sint32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 24: goproto.protoc.extension.ext.repeated_x_uint32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 25: goproto.protoc.extension.ext.repeated_x_int64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 26: goproto.protoc.extension.ext.repeated_x_sint64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 27: goproto.protoc.extension.ext.repeated_x_uint64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 28: goproto.protoc.extension.ext.repeated_x_sfixed32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 29: goproto.protoc.extension.ext.repeated_x_fixed32:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 30: goproto.protoc.extension.ext.repeated_x_float:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 31: goproto.protoc.extension.ext.repeated_x_sfixed64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 32: goproto.protoc.extension.ext.repeated_x_fixed64:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 33: goproto.protoc.extension.ext.repeated_x_double:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 34: goproto.protoc.extension.ext.repeated_x_string:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 35: goproto.protoc.extension.ext.repeated_x_bytes:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 36: goproto.protoc.extension.ext.repeated_x_Message:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 37: goproto.protoc.extension.ext.repeatedgroup:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 38: goproto.protoc.extension.ext.extendable_field:extendee -> goproto.protoc.extension.base.BaseMessage
	5,  // 39: goproto.protoc.extension.ext.extendable_string_field:extendee -> goproto.protoc.extension.ext.Extendable
	10, // 40: goproto.protoc.extension.ext.message_set_extension:extendee -> goproto.protoc.extension.base.MessageSetWireFormatMessage
	9,  // 41: goproto.protoc.extension.ext.ExtendingMessage.extending_message_string:extendee -> goproto.protoc.extension.base.BaseMessage
	9,  // 42: goproto.protoc.extension.ext.ExtendingMessage.extending_message_submessage:extendee -> goproto.protoc.extension.base.BaseMessage
	10, // 43: goproto.protoc.extension.ext.MessageSetWireFormatExtension.message_set_extension:extendee -> goproto.protoc.extension.base.MessageSetWireFormatMessage
	0,  // 44: goproto.protoc.extension.ext.extension_enum:type_name -> goproto.protoc.extension.ext.Enum
	1,  // 45: goproto.protoc.extension.ext.extension_Message:type_name -> goproto.protoc.extension.ext.Message
	7,  // 46: goproto.protoc.extension.ext.extension_MessageM:type_name -> goproto.protoc.extension.ext.Message.M
	2,  // 47: goproto.protoc.extension.ext.extensiongroup:type_name -> goproto.protoc.extension.ext.ExtensionGroup
	11, // 48: goproto.protoc.extension.ext.extra_message:type_name -> goproto.protoc.extension.extra.ExtraMessage
	0,  // 49: goproto.protoc.extension.ext.repeated_x_enum:type_name -> goproto.protoc.extension.ext.Enum
	1,  // 50: goproto.protoc.extension.ext.repeated_x_Message:type_name -> goproto.protoc.extension.ext.Message
	4,  // 51: goproto.protoc.extension.ext.repeatedgroup:type_name -> goproto.protoc.extension.ext.RepeatedGroup
	5,  // 52: goproto.protoc.extension.ext.extendable_field:type_name -> goproto.protoc.extension.ext.Extendable
	6,  // 53: goproto.protoc.extension.ext.message_set_extension:type_name -> goproto.protoc.extension.ext.MessageSetWireFormatExtension
	8,  // 54: goproto.protoc.extension.ext.ExtendingMessage.extending_message_submessage:type_name -> goproto.protoc.extension.ext.ExtendingMessage.ExtendingMessageSubmessage
	6,  // 55: goproto.protoc.extension.ext.MessageSetWireFormatExtension.message_set_extension:type_name -> goproto.protoc.extension.ext.MessageSetWireFormatExtension
	56, // [56:56] is the sub-list for method output_type
	56, // [56:56] is the sub-list for method input_type
	44, // [44:56] is the sub-list for extension type_name
	0,  // [0:44] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_init() }
func file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_init() {
	if File_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtensionGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendingMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Extendable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageSetWireFormatExtension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message_M); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendingMessage_ExtendingMessageSubmessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 44,
			NumServices:   0,
		},
		GoTypes:           file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_goTypes,
		DependencyIndexes: file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_depIdxs,
		EnumInfos:         file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_enumTypes,
		MessageInfos:      file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_msgTypes,
		ExtensionInfos:    file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_extTypes,
	}.Build()
	File_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto = out.File
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_rawDesc = nil
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_goTypes = nil
	file_cmd_protoc_gen_go_testdata_extensions_ext_ext_proto_depIdxs = nil
}
