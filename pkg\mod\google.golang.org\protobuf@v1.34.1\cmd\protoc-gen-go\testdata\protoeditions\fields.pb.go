// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cmd/protoc-gen-go/testdata/protoeditions/fields.proto

package protoeditions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	math "math"
	reflect "reflect"
	sync "sync"
)

type FieldTestMessage_Enum int32

const (
	FieldTestMessage_ZERO FieldTestMessage_Enum = 0
	FieldTestMessage_ONE  FieldTestMessage_Enum = 1
)

// Enum value maps for FieldTestMessage_Enum.
var (
	FieldTestMessage_Enum_name = map[int32]string{
		0: "ZERO",
		1: "ONE",
	}
	FieldTestMessage_Enum_value = map[string]int32{
		"ZERO": 0,
		"ONE":  1,
	}
)

func (x FieldTestMessage_Enum) Enum() *FieldTestMessage_Enum {
	p := new(FieldTestMessage_Enum)
	*p = x
	return p
}

func (x FieldTestMessage_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldTestMessage_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_enumTypes[0].Descriptor()
}

func (FieldTestMessage_Enum) Type() protoreflect.EnumType {
	return &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_enumTypes[0]
}

func (x FieldTestMessage_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldTestMessage_Enum.Descriptor instead.
func (FieldTestMessage_Enum) EnumDescriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 0}
}

type FieldTestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionalBool        *bool                                `protobuf:"varint,1,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalEnum        *FieldTestMessage_Enum               `protobuf:"varint,2,opt,name=optional_enum,json=optionalEnum,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum" json:"optional_enum,omitempty"`
	OptionalInt32       *int32                               `protobuf:"varint,3,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalSint32      *int32                               `protobuf:"zigzag32,4,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalUint32      *uint32                              `protobuf:"varint,5,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalInt64       *int64                               `protobuf:"varint,6,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalSint64      *int64                               `protobuf:"zigzag64,7,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalUint64      *uint64                              `protobuf:"varint,8,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSfixed32    *int32                               `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalFixed32     *uint32                              `protobuf:"fixed32,10,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFloat       *float32                             `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalSfixed64    *int64                               `protobuf:"fixed64,12,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFixed64     *uint64                              `protobuf:"fixed64,13,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalDouble      *float64                             `protobuf:"fixed64,14,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalString      *string                              `protobuf:"bytes,15,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes       []byte                               `protobuf:"bytes,16,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	Optional_Message    *FieldTestMessage_Message            `protobuf:"bytes,17,opt,name=optional_Message,json=optionalMessage" json:"optional_Message,omitempty"`
	Optionalgroup       *FieldTestMessage_OptionalGroup      `protobuf:"group,18,opt,name=OptionalGroup,json=optionalgroup" json:"optionalgroup,omitempty"`
	RequiredBool        *bool                                `protobuf:"varint,101,req,name=required_bool,json=requiredBool" json:"required_bool,omitempty"`
	RequiredEnum        *FieldTestMessage_Enum               `protobuf:"varint,102,req,name=required_enum,json=requiredEnum,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum" json:"required_enum,omitempty"`
	RequiredInt32       *int32                               `protobuf:"varint,103,req,name=required_int32,json=requiredInt32" json:"required_int32,omitempty"`
	RequiredSint32      *int32                               `protobuf:"zigzag32,104,req,name=required_sint32,json=requiredSint32" json:"required_sint32,omitempty"`
	RequiredUint32      *uint32                              `protobuf:"varint,105,req,name=required_uint32,json=requiredUint32" json:"required_uint32,omitempty"`
	RequiredInt64       *int64                               `protobuf:"varint,106,req,name=required_int64,json=requiredInt64" json:"required_int64,omitempty"`
	RequiredSint64      *int64                               `protobuf:"zigzag64,107,req,name=required_sint64,json=requiredSint64" json:"required_sint64,omitempty"`
	RequiredUint64      *uint64                              `protobuf:"varint,108,req,name=required_uint64,json=requiredUint64" json:"required_uint64,omitempty"`
	RequiredSfixed32    *int32                               `protobuf:"fixed32,109,req,name=required_sfixed32,json=requiredSfixed32" json:"required_sfixed32,omitempty"`
	RequiredFixed32     *uint32                              `protobuf:"fixed32,110,req,name=required_fixed32,json=requiredFixed32" json:"required_fixed32,omitempty"`
	RequiredFloat       *float32                             `protobuf:"fixed32,111,req,name=required_float,json=requiredFloat" json:"required_float,omitempty"`
	RequiredSfixed64    *int64                               `protobuf:"fixed64,112,req,name=required_sfixed64,json=requiredSfixed64" json:"required_sfixed64,omitempty"`
	RequiredFixed64     *uint64                              `protobuf:"fixed64,113,req,name=required_fixed64,json=requiredFixed64" json:"required_fixed64,omitempty"`
	RequiredDouble      *float64                             `protobuf:"fixed64,114,req,name=required_double,json=requiredDouble" json:"required_double,omitempty"`
	RequiredString      *string                              `protobuf:"bytes,115,req,name=required_string,json=requiredString" json:"required_string,omitempty"`
	RequiredBytes       []byte                               `protobuf:"bytes,116,req,name=required_bytes,json=requiredBytes" json:"required_bytes,omitempty"`
	Required_Message    *FieldTestMessage_Message            `protobuf:"bytes,117,req,name=required_Message,json=requiredMessage" json:"required_Message,omitempty"`
	Requiredgroup       *FieldTestMessage_RequiredGroup      `protobuf:"group,118,req,name=RequiredGroup,json=requiredgroup" json:"requiredgroup,omitempty"`
	RepeatedBool        []bool                               `protobuf:"varint,201,rep,packed,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedEnum        []FieldTestMessage_Enum              `protobuf:"varint,202,rep,packed,name=repeated_enum,json=repeatedEnum,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum" json:"repeated_enum,omitempty"`
	RepeatedInt32       []int32                              `protobuf:"varint,203,rep,packed,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedSint32      []int32                              `protobuf:"zigzag32,204,rep,packed,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedUint32      []uint32                             `protobuf:"varint,205,rep,packed,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedInt64       []int64                              `protobuf:"varint,206,rep,packed,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedSint64      []int64                              `protobuf:"zigzag64,207,rep,packed,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedUint64      []uint64                             `protobuf:"varint,208,rep,packed,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSfixed32    []int32                              `protobuf:"fixed32,209,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedFixed32     []uint32                             `protobuf:"fixed32,210,rep,packed,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFloat       []float32                            `protobuf:"fixed32,211,rep,packed,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedSfixed64    []int64                              `protobuf:"fixed64,212,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFixed64     []uint64                             `protobuf:"fixed64,213,rep,packed,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedDouble      []float64                            `protobuf:"fixed64,214,rep,packed,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedString      []string                             `protobuf:"bytes,215,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes       [][]byte                             `protobuf:"bytes,216,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	Repeated_Message    []*FieldTestMessage_Message          `protobuf:"bytes,217,rep,name=repeated_Message,json=repeatedMessage" json:"repeated_Message,omitempty"`
	Repeatedgroup       []*FieldTestMessage_RepeatedGroup    `protobuf:"group,218,rep,name=RepeatedGroup,json=repeatedgroup" json:"repeatedgroup,omitempty"`
	DefaultBool         *bool                                `protobuf:"varint,301,opt,name=default_bool,json=defaultBool,def=1" json:"default_bool,omitempty"`
	DefaultEnum         *FieldTestMessage_Enum               `protobuf:"varint,302,opt,name=default_enum,json=defaultEnum,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum,def=1" json:"default_enum,omitempty"`
	DefaultInt32        *int32                               `protobuf:"varint,303,opt,name=default_int32,json=defaultInt32,def=1" json:"default_int32,omitempty"`
	DefaultSint32       *int32                               `protobuf:"zigzag32,304,opt,name=default_sint32,json=defaultSint32,def=1" json:"default_sint32,omitempty"`
	DefaultUint32       *uint32                              `protobuf:"varint,305,opt,name=default_uint32,json=defaultUint32,def=1" json:"default_uint32,omitempty"`
	DefaultInt64        *int64                               `protobuf:"varint,306,opt,name=default_int64,json=defaultInt64,def=1" json:"default_int64,omitempty"`
	DefaultSint64       *int64                               `protobuf:"zigzag64,307,opt,name=default_sint64,json=defaultSint64,def=1" json:"default_sint64,omitempty"`
	DefaultUint64       *uint64                              `protobuf:"varint,308,opt,name=default_uint64,json=defaultUint64,def=1" json:"default_uint64,omitempty"`
	DefaultSfixed32     *int32                               `protobuf:"fixed32,309,opt,name=default_sfixed32,json=defaultSfixed32,def=1" json:"default_sfixed32,omitempty"`
	DefaultFixed32      *uint32                              `protobuf:"fixed32,310,opt,name=default_fixed32,json=defaultFixed32,def=1" json:"default_fixed32,omitempty"`
	DefaultFloat        *float32                             `protobuf:"fixed32,311,opt,name=default_float,json=defaultFloat,def=3.14" json:"default_float,omitempty"`
	DefaultSfixed64     *int64                               `protobuf:"fixed64,312,opt,name=default_sfixed64,json=defaultSfixed64,def=1" json:"default_sfixed64,omitempty"`
	DefaultFixed64      *uint64                              `protobuf:"fixed64,313,opt,name=default_fixed64,json=defaultFixed64,def=1" json:"default_fixed64,omitempty"`
	DefaultDouble       *float64                             `protobuf:"fixed64,314,opt,name=default_double,json=defaultDouble,def=3.1415" json:"default_double,omitempty"`
	DefaultString       *string                              `protobuf:"bytes,315,opt,name=default_string,json=defaultString,def=hello,\"world!\"\n" json:"default_string,omitempty"`
	DefaultBytes        []byte                               `protobuf:"bytes,316,opt,name=default_bytes,json=defaultBytes,def=hello,\\336\\255\\276\\357" json:"default_bytes,omitempty"`
	DefaultZeroString   *string                              `protobuf:"bytes,350,opt,name=default_zero_string,json=defaultZeroString,def=" json:"default_zero_string,omitempty"`
	DefaultZeroBytes    []byte                               `protobuf:"bytes,351,opt,name=default_zero_bytes,json=defaultZeroBytes,def=" json:"default_zero_bytes,omitempty"`
	DefaultFloatNeginf  *float32                             `protobuf:"fixed32,400,opt,name=default_float_neginf,json=defaultFloatNeginf,def=-inf" json:"default_float_neginf,omitempty"`
	DefaultFloatPosinf  *float32                             `protobuf:"fixed32,401,opt,name=default_float_posinf,json=defaultFloatPosinf,def=inf" json:"default_float_posinf,omitempty"`
	DefaultFloatNan     *float32                             `protobuf:"fixed32,402,opt,name=default_float_nan,json=defaultFloatNan,def=nan" json:"default_float_nan,omitempty"`
	DefaultDoubleNeginf *float64                             `protobuf:"fixed64,403,opt,name=default_double_neginf,json=defaultDoubleNeginf,def=-inf" json:"default_double_neginf,omitempty"`
	DefaultDoublePosinf *float64                             `protobuf:"fixed64,404,opt,name=default_double_posinf,json=defaultDoublePosinf,def=inf" json:"default_double_posinf,omitempty"`
	DefaultDoubleNan    *float64                             `protobuf:"fixed64,405,opt,name=default_double_nan,json=defaultDoubleNan,def=nan" json:"default_double_nan,omitempty"`
	MapInt32Int64       map[int32]int64                      `protobuf:"bytes,500,rep,name=map_int32_int64,json=mapInt32Int64" json:"map_int32_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringMessage    map[string]*FieldTestMessage_Message `protobuf:"bytes,501,rep,name=map_string_message,json=mapStringMessage" json:"map_string_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapFixed64Enum      map[uint64]FieldTestMessage_Enum     `protobuf:"bytes,502,rep,name=map_fixed64_enum,json=mapFixed64Enum" json:"map_fixed64_enum,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum"`
	// Types that are assignable to OneofField:
	//
	//	*FieldTestMessage_OneofBool
	//	*FieldTestMessage_OneofEnum
	//	*FieldTestMessage_OneofInt32
	//	*FieldTestMessage_OneofSint32
	//	*FieldTestMessage_OneofUint32
	//	*FieldTestMessage_OneofInt64
	//	*FieldTestMessage_OneofSint64
	//	*FieldTestMessage_OneofUint64
	//	*FieldTestMessage_OneofSfixed32
	//	*FieldTestMessage_OneofFixed32
	//	*FieldTestMessage_OneofFloat
	//	*FieldTestMessage_OneofSfixed64
	//	*FieldTestMessage_OneofFixed64
	//	*FieldTestMessage_OneofDouble
	//	*FieldTestMessage_OneofString
	//	*FieldTestMessage_OneofBytes
	//	*FieldTestMessage_Oneof_Message
	//	*FieldTestMessage_Oneofgroup
	//	*FieldTestMessage_OneofLargestTag
	OneofField isFieldTestMessage_OneofField `protobuf_oneof:"oneof_field"`
	// Types that are assignable to OneofTwo:
	//
	//	*FieldTestMessage_OneofTwo_1
	//	*FieldTestMessage_OneofTwo_2
	OneofTwo isFieldTestMessage_OneofTwo `protobuf_oneof:"oneof_two"`
}

// Default values for FieldTestMessage fields.
const (
	Default_FieldTestMessage_DefaultBool       = bool(true)
	Default_FieldTestMessage_DefaultEnum       = FieldTestMessage_ONE
	Default_FieldTestMessage_DefaultInt32      = int32(1)
	Default_FieldTestMessage_DefaultSint32     = int32(1)
	Default_FieldTestMessage_DefaultUint32     = uint32(1)
	Default_FieldTestMessage_DefaultInt64      = int64(1)
	Default_FieldTestMessage_DefaultSint64     = int64(1)
	Default_FieldTestMessage_DefaultUint64     = uint64(1)
	Default_FieldTestMessage_DefaultSfixed32   = int32(1)
	Default_FieldTestMessage_DefaultFixed32    = uint32(1)
	Default_FieldTestMessage_DefaultFloat      = float32(3.140000104904175)
	Default_FieldTestMessage_DefaultSfixed64   = int64(1)
	Default_FieldTestMessage_DefaultFixed64    = uint64(1)
	Default_FieldTestMessage_DefaultDouble     = float64(3.1415)
	Default_FieldTestMessage_DefaultString     = string("hello,\"world!\"\n")
	Default_FieldTestMessage_DefaultZeroString = string("")
)

// Default values for FieldTestMessage fields.
var (
	Default_FieldTestMessage_DefaultBytes        = []byte("hello,ޭ\xbe\xef")
	Default_FieldTestMessage_DefaultZeroBytes    = []byte("")
	Default_FieldTestMessage_DefaultFloatNeginf  = float32(math.Inf(-1))
	Default_FieldTestMessage_DefaultFloatPosinf  = float32(math.Inf(+1))
	Default_FieldTestMessage_DefaultFloatNan     = float32(math.NaN())
	Default_FieldTestMessage_DefaultDoubleNeginf = float64(math.Inf(-1))
	Default_FieldTestMessage_DefaultDoublePosinf = float64(math.Inf(+1))
	Default_FieldTestMessage_DefaultDoubleNan    = float64(math.NaN())
)

func (x *FieldTestMessage) Reset() {
	*x = FieldTestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage) ProtoMessage() {}

func (x *FieldTestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage.ProtoReflect.Descriptor instead.
func (*FieldTestMessage) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0}
}

func (x *FieldTestMessage) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *FieldTestMessage) GetOptionalEnum() FieldTestMessage_Enum {
	if x != nil && x.OptionalEnum != nil {
		return *x.OptionalEnum
	}
	return FieldTestMessage_ZERO
}

func (x *FieldTestMessage) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *FieldTestMessage) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *FieldTestMessage) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *FieldTestMessage) GetOptional_Message() *FieldTestMessage_Message {
	if x != nil {
		return x.Optional_Message
	}
	return nil
}

func (x *FieldTestMessage) GetOptionalgroup() *FieldTestMessage_OptionalGroup {
	if x != nil {
		return x.Optionalgroup
	}
	return nil
}

func (x *FieldTestMessage) GetRequiredBool() bool {
	if x != nil && x.RequiredBool != nil {
		return *x.RequiredBool
	}
	return false
}

func (x *FieldTestMessage) GetRequiredEnum() FieldTestMessage_Enum {
	if x != nil && x.RequiredEnum != nil {
		return *x.RequiredEnum
	}
	return FieldTestMessage_ZERO
}

func (x *FieldTestMessage) GetRequiredInt32() int32 {
	if x != nil && x.RequiredInt32 != nil {
		return *x.RequiredInt32
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredSint32() int32 {
	if x != nil && x.RequiredSint32 != nil {
		return *x.RequiredSint32
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredUint32() uint32 {
	if x != nil && x.RequiredUint32 != nil {
		return *x.RequiredUint32
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredInt64() int64 {
	if x != nil && x.RequiredInt64 != nil {
		return *x.RequiredInt64
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredSint64() int64 {
	if x != nil && x.RequiredSint64 != nil {
		return *x.RequiredSint64
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredUint64() uint64 {
	if x != nil && x.RequiredUint64 != nil {
		return *x.RequiredUint64
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredSfixed32() int32 {
	if x != nil && x.RequiredSfixed32 != nil {
		return *x.RequiredSfixed32
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredFixed32() uint32 {
	if x != nil && x.RequiredFixed32 != nil {
		return *x.RequiredFixed32
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredFloat() float32 {
	if x != nil && x.RequiredFloat != nil {
		return *x.RequiredFloat
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredSfixed64() int64 {
	if x != nil && x.RequiredSfixed64 != nil {
		return *x.RequiredSfixed64
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredFixed64() uint64 {
	if x != nil && x.RequiredFixed64 != nil {
		return *x.RequiredFixed64
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredDouble() float64 {
	if x != nil && x.RequiredDouble != nil {
		return *x.RequiredDouble
	}
	return 0
}

func (x *FieldTestMessage) GetRequiredString() string {
	if x != nil && x.RequiredString != nil {
		return *x.RequiredString
	}
	return ""
}

func (x *FieldTestMessage) GetRequiredBytes() []byte {
	if x != nil {
		return x.RequiredBytes
	}
	return nil
}

func (x *FieldTestMessage) GetRequired_Message() *FieldTestMessage_Message {
	if x != nil {
		return x.Required_Message
	}
	return nil
}

func (x *FieldTestMessage) GetRequiredgroup() *FieldTestMessage_RequiredGroup {
	if x != nil {
		return x.Requiredgroup
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedEnum() []FieldTestMessage_Enum {
	if x != nil {
		return x.RepeatedEnum
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *FieldTestMessage) GetRepeated_Message() []*FieldTestMessage_Message {
	if x != nil {
		return x.Repeated_Message
	}
	return nil
}

func (x *FieldTestMessage) GetRepeatedgroup() []*FieldTestMessage_RepeatedGroup {
	if x != nil {
		return x.Repeatedgroup
	}
	return nil
}

func (x *FieldTestMessage) GetDefaultBool() bool {
	if x != nil && x.DefaultBool != nil {
		return *x.DefaultBool
	}
	return Default_FieldTestMessage_DefaultBool
}

func (x *FieldTestMessage) GetDefaultEnum() FieldTestMessage_Enum {
	if x != nil && x.DefaultEnum != nil {
		return *x.DefaultEnum
	}
	return Default_FieldTestMessage_DefaultEnum
}

func (x *FieldTestMessage) GetDefaultInt32() int32 {
	if x != nil && x.DefaultInt32 != nil {
		return *x.DefaultInt32
	}
	return Default_FieldTestMessage_DefaultInt32
}

func (x *FieldTestMessage) GetDefaultSint32() int32 {
	if x != nil && x.DefaultSint32 != nil {
		return *x.DefaultSint32
	}
	return Default_FieldTestMessage_DefaultSint32
}

func (x *FieldTestMessage) GetDefaultUint32() uint32 {
	if x != nil && x.DefaultUint32 != nil {
		return *x.DefaultUint32
	}
	return Default_FieldTestMessage_DefaultUint32
}

func (x *FieldTestMessage) GetDefaultInt64() int64 {
	if x != nil && x.DefaultInt64 != nil {
		return *x.DefaultInt64
	}
	return Default_FieldTestMessage_DefaultInt64
}

func (x *FieldTestMessage) GetDefaultSint64() int64 {
	if x != nil && x.DefaultSint64 != nil {
		return *x.DefaultSint64
	}
	return Default_FieldTestMessage_DefaultSint64
}

func (x *FieldTestMessage) GetDefaultUint64() uint64 {
	if x != nil && x.DefaultUint64 != nil {
		return *x.DefaultUint64
	}
	return Default_FieldTestMessage_DefaultUint64
}

func (x *FieldTestMessage) GetDefaultSfixed32() int32 {
	if x != nil && x.DefaultSfixed32 != nil {
		return *x.DefaultSfixed32
	}
	return Default_FieldTestMessage_DefaultSfixed32
}

func (x *FieldTestMessage) GetDefaultFixed32() uint32 {
	if x != nil && x.DefaultFixed32 != nil {
		return *x.DefaultFixed32
	}
	return Default_FieldTestMessage_DefaultFixed32
}

func (x *FieldTestMessage) GetDefaultFloat() float32 {
	if x != nil && x.DefaultFloat != nil {
		return *x.DefaultFloat
	}
	return Default_FieldTestMessage_DefaultFloat
}

func (x *FieldTestMessage) GetDefaultSfixed64() int64 {
	if x != nil && x.DefaultSfixed64 != nil {
		return *x.DefaultSfixed64
	}
	return Default_FieldTestMessage_DefaultSfixed64
}

func (x *FieldTestMessage) GetDefaultFixed64() uint64 {
	if x != nil && x.DefaultFixed64 != nil {
		return *x.DefaultFixed64
	}
	return Default_FieldTestMessage_DefaultFixed64
}

func (x *FieldTestMessage) GetDefaultDouble() float64 {
	if x != nil && x.DefaultDouble != nil {
		return *x.DefaultDouble
	}
	return Default_FieldTestMessage_DefaultDouble
}

func (x *FieldTestMessage) GetDefaultString() string {
	if x != nil && x.DefaultString != nil {
		return *x.DefaultString
	}
	return Default_FieldTestMessage_DefaultString
}

func (x *FieldTestMessage) GetDefaultBytes() []byte {
	if x != nil && x.DefaultBytes != nil {
		return x.DefaultBytes
	}
	return append([]byte(nil), Default_FieldTestMessage_DefaultBytes...)
}

func (x *FieldTestMessage) GetDefaultZeroString() string {
	if x != nil && x.DefaultZeroString != nil {
		return *x.DefaultZeroString
	}
	return Default_FieldTestMessage_DefaultZeroString
}

func (x *FieldTestMessage) GetDefaultZeroBytes() []byte {
	if x != nil && x.DefaultZeroBytes != nil {
		return x.DefaultZeroBytes
	}
	return append([]byte(nil), Default_FieldTestMessage_DefaultZeroBytes...)
}

func (x *FieldTestMessage) GetDefaultFloatNeginf() float32 {
	if x != nil && x.DefaultFloatNeginf != nil {
		return *x.DefaultFloatNeginf
	}
	return Default_FieldTestMessage_DefaultFloatNeginf
}

func (x *FieldTestMessage) GetDefaultFloatPosinf() float32 {
	if x != nil && x.DefaultFloatPosinf != nil {
		return *x.DefaultFloatPosinf
	}
	return Default_FieldTestMessage_DefaultFloatPosinf
}

func (x *FieldTestMessage) GetDefaultFloatNan() float32 {
	if x != nil && x.DefaultFloatNan != nil {
		return *x.DefaultFloatNan
	}
	return Default_FieldTestMessage_DefaultFloatNan
}

func (x *FieldTestMessage) GetDefaultDoubleNeginf() float64 {
	if x != nil && x.DefaultDoubleNeginf != nil {
		return *x.DefaultDoubleNeginf
	}
	return Default_FieldTestMessage_DefaultDoubleNeginf
}

func (x *FieldTestMessage) GetDefaultDoublePosinf() float64 {
	if x != nil && x.DefaultDoublePosinf != nil {
		return *x.DefaultDoublePosinf
	}
	return Default_FieldTestMessage_DefaultDoublePosinf
}

func (x *FieldTestMessage) GetDefaultDoubleNan() float64 {
	if x != nil && x.DefaultDoubleNan != nil {
		return *x.DefaultDoubleNan
	}
	return Default_FieldTestMessage_DefaultDoubleNan
}

func (x *FieldTestMessage) GetMapInt32Int64() map[int32]int64 {
	if x != nil {
		return x.MapInt32Int64
	}
	return nil
}

func (x *FieldTestMessage) GetMapStringMessage() map[string]*FieldTestMessage_Message {
	if x != nil {
		return x.MapStringMessage
	}
	return nil
}

func (x *FieldTestMessage) GetMapFixed64Enum() map[uint64]FieldTestMessage_Enum {
	if x != nil {
		return x.MapFixed64Enum
	}
	return nil
}

func (m *FieldTestMessage) GetOneofField() isFieldTestMessage_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *FieldTestMessage) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *FieldTestMessage) GetOneofEnum() FieldTestMessage_Enum {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofEnum); ok {
		return x.OneofEnum
	}
	return FieldTestMessage_ZERO
}

func (x *FieldTestMessage) GetOneofInt32() int32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofInt32); ok {
		return x.OneofInt32
	}
	return 0
}

func (x *FieldTestMessage) GetOneofSint32() int32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofSint32); ok {
		return x.OneofSint32
	}
	return 0
}

func (x *FieldTestMessage) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *FieldTestMessage) GetOneofInt64() int64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofInt64); ok {
		return x.OneofInt64
	}
	return 0
}

func (x *FieldTestMessage) GetOneofSint64() int64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofSint64); ok {
		return x.OneofSint64
	}
	return 0
}

func (x *FieldTestMessage) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *FieldTestMessage) GetOneofSfixed32() int32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofSfixed32); ok {
		return x.OneofSfixed32
	}
	return 0
}

func (x *FieldTestMessage) GetOneofFixed32() uint32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofFixed32); ok {
		return x.OneofFixed32
	}
	return 0
}

func (x *FieldTestMessage) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *FieldTestMessage) GetOneofSfixed64() int64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofSfixed64); ok {
		return x.OneofSfixed64
	}
	return 0
}

func (x *FieldTestMessage) GetOneofFixed64() uint64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofFixed64); ok {
		return x.OneofFixed64
	}
	return 0
}

func (x *FieldTestMessage) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *FieldTestMessage) GetOneofString() string {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *FieldTestMessage) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *FieldTestMessage) GetOneof_Message() *FieldTestMessage_Message {
	if x, ok := x.GetOneofField().(*FieldTestMessage_Oneof_Message); ok {
		return x.Oneof_Message
	}
	return nil
}

func (x *FieldTestMessage) GetOneofgroup() *FieldTestMessage_OneofGroup {
	if x, ok := x.GetOneofField().(*FieldTestMessage_Oneofgroup); ok {
		return x.Oneofgroup
	}
	return nil
}

func (x *FieldTestMessage) GetOneofLargestTag() int32 {
	if x, ok := x.GetOneofField().(*FieldTestMessage_OneofLargestTag); ok {
		return x.OneofLargestTag
	}
	return 0
}

func (m *FieldTestMessage) GetOneofTwo() isFieldTestMessage_OneofTwo {
	if m != nil {
		return m.OneofTwo
	}
	return nil
}

func (x *FieldTestMessage) GetOneofTwo_1() int32 {
	if x, ok := x.GetOneofTwo().(*FieldTestMessage_OneofTwo_1); ok {
		return x.OneofTwo_1
	}
	return 0
}

func (x *FieldTestMessage) GetOneofTwo_2() int64 {
	if x, ok := x.GetOneofTwo().(*FieldTestMessage_OneofTwo_2); ok {
		return x.OneofTwo_2
	}
	return 0
}

type isFieldTestMessage_OneofField interface {
	isFieldTestMessage_OneofField()
}

type FieldTestMessage_OneofBool struct {
	OneofBool bool `protobuf:"varint,601,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type FieldTestMessage_OneofEnum struct {
	OneofEnum FieldTestMessage_Enum `protobuf:"varint,602,opt,name=oneof_enum,json=oneofEnum,enum=goproto.protoc.protoeditions.FieldTestMessage_Enum,oneof"`
}

type FieldTestMessage_OneofInt32 struct {
	OneofInt32 int32 `protobuf:"varint,603,opt,name=oneof_int32,json=oneofInt32,oneof"`
}

type FieldTestMessage_OneofSint32 struct {
	OneofSint32 int32 `protobuf:"zigzag32,604,opt,name=oneof_sint32,json=oneofSint32,oneof"`
}

type FieldTestMessage_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,605,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type FieldTestMessage_OneofInt64 struct {
	OneofInt64 int64 `protobuf:"varint,606,opt,name=oneof_int64,json=oneofInt64,oneof"`
}

type FieldTestMessage_OneofSint64 struct {
	OneofSint64 int64 `protobuf:"zigzag64,607,opt,name=oneof_sint64,json=oneofSint64,oneof"`
}

type FieldTestMessage_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,608,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type FieldTestMessage_OneofSfixed32 struct {
	OneofSfixed32 int32 `protobuf:"fixed32,609,opt,name=oneof_sfixed32,json=oneofSfixed32,oneof"`
}

type FieldTestMessage_OneofFixed32 struct {
	OneofFixed32 uint32 `protobuf:"fixed32,610,opt,name=oneof_fixed32,json=oneofFixed32,oneof"`
}

type FieldTestMessage_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,611,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type FieldTestMessage_OneofSfixed64 struct {
	OneofSfixed64 int64 `protobuf:"fixed64,612,opt,name=oneof_sfixed64,json=oneofSfixed64,oneof"`
}

type FieldTestMessage_OneofFixed64 struct {
	OneofFixed64 uint64 `protobuf:"fixed64,613,opt,name=oneof_fixed64,json=oneofFixed64,oneof"`
}

type FieldTestMessage_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,614,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type FieldTestMessage_OneofString struct {
	OneofString string `protobuf:"bytes,615,opt,name=oneof_string,json=oneofString,oneof"`
}

type FieldTestMessage_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,616,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type FieldTestMessage_Oneof_Message struct {
	Oneof_Message *FieldTestMessage_Message `protobuf:"bytes,617,opt,name=oneof_Message,json=oneofMessage,oneof"`
}

type FieldTestMessage_Oneofgroup struct {
	Oneofgroup *FieldTestMessage_OneofGroup `protobuf:"group,618,opt,name=OneofGroup,json=oneofgroup,oneof"`
}

type FieldTestMessage_OneofLargestTag struct {
	OneofLargestTag int32 `protobuf:"varint,536870911,opt,name=oneof_largest_tag,json=oneofLargestTag,oneof"`
}

func (*FieldTestMessage_OneofBool) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofEnum) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofInt32) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofSint32) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofUint32) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofInt64) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofSint64) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofUint64) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofSfixed32) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofFixed32) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofFloat) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofSfixed64) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofFixed64) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofDouble) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofString) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofBytes) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_Oneof_Message) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_Oneofgroup) isFieldTestMessage_OneofField() {}

func (*FieldTestMessage_OneofLargestTag) isFieldTestMessage_OneofField() {}

type isFieldTestMessage_OneofTwo interface {
	isFieldTestMessage_OneofTwo()
}

type FieldTestMessage_OneofTwo_1 struct {
	OneofTwo_1 int32 `protobuf:"varint,700,opt,name=oneof_two_1,json=oneofTwo1,oneof"`
}

type FieldTestMessage_OneofTwo_2 struct {
	OneofTwo_2 int64 `protobuf:"varint,701,opt,name=oneof_two_2,json=oneofTwo2,oneof"`
}

func (*FieldTestMessage_OneofTwo_1) isFieldTestMessage_OneofTwo() {}

func (*FieldTestMessage_OneofTwo_2) isFieldTestMessage_OneofTwo() {}

type FieldTestMessage_OptionalGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Optionalgroup *string `protobuf:"bytes,19,opt,name=optionalgroup" json:"optionalgroup,omitempty"`
}

func (x *FieldTestMessage_OptionalGroup) Reset() {
	*x = FieldTestMessage_OptionalGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage_OptionalGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage_OptionalGroup) ProtoMessage() {}

func (x *FieldTestMessage_OptionalGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage_OptionalGroup.ProtoReflect.Descriptor instead.
func (*FieldTestMessage_OptionalGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 0}
}

func (x *FieldTestMessage_OptionalGroup) GetOptionalgroup() string {
	if x != nil && x.Optionalgroup != nil {
		return *x.Optionalgroup
	}
	return ""
}

type FieldTestMessage_RequiredGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequiredGroup *string `protobuf:"bytes,119,req,name=required_group,json=requiredGroup" json:"required_group,omitempty"`
}

func (x *FieldTestMessage_RequiredGroup) Reset() {
	*x = FieldTestMessage_RequiredGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage_RequiredGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage_RequiredGroup) ProtoMessage() {}

func (x *FieldTestMessage_RequiredGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage_RequiredGroup.ProtoReflect.Descriptor instead.
func (*FieldTestMessage_RequiredGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 1}
}

func (x *FieldTestMessage_RequiredGroup) GetRequiredGroup() string {
	if x != nil && x.RequiredGroup != nil {
		return *x.RequiredGroup
	}
	return ""
}

type FieldTestMessage_RepeatedGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RepeatedGroup []string `protobuf:"bytes,219,rep,name=repeated_group,json=repeatedGroup" json:"repeated_group,omitempty"`
}

func (x *FieldTestMessage_RepeatedGroup) Reset() {
	*x = FieldTestMessage_RepeatedGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage_RepeatedGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage_RepeatedGroup) ProtoMessage() {}

func (x *FieldTestMessage_RepeatedGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage_RepeatedGroup.ProtoReflect.Descriptor instead.
func (*FieldTestMessage_RepeatedGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 2}
}

func (x *FieldTestMessage_RepeatedGroup) GetRepeatedGroup() []string {
	if x != nil {
		return x.RepeatedGroup
	}
	return nil
}

type FieldTestMessage_OneofGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OneofGroupField *string `protobuf:"bytes,619,opt,name=oneof_group_field,json=oneofGroupField" json:"oneof_group_field,omitempty"`
}

func (x *FieldTestMessage_OneofGroup) Reset() {
	*x = FieldTestMessage_OneofGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage_OneofGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage_OneofGroup) ProtoMessage() {}

func (x *FieldTestMessage_OneofGroup) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage_OneofGroup.ProtoReflect.Descriptor instead.
func (*FieldTestMessage_OneofGroup) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 6}
}

func (x *FieldTestMessage_OneofGroup) GetOneofGroupField() string {
	if x != nil && x.OneofGroupField != nil {
		return *x.OneofGroupField
	}
	return ""
}

type FieldTestMessage_Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FieldTestMessage_Message) Reset() {
	*x = FieldTestMessage_Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldTestMessage_Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldTestMessage_Message) ProtoMessage() {}

func (x *FieldTestMessage_Message) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldTestMessage_Message.ProtoReflect.Descriptor instead.
func (*FieldTestMessage_Message) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP(), []int{0, 7}
}

var File_cmd_protoc_gen_go_testdata_protoeditions_fields_proto protoreflect.FileDescriptor

var file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDesc = []byte{
	0x0a, 0x35, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc0, 0x2f, 0x0a, 0x10, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12,
	0x58, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x18, 0x04, 0x20, 0x01, 0x28, 0x11, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x06, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x10, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x69,
	0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2a, 0x0a, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x65, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x5f, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x66, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x67, 0x20, 0x01, 0x28, 0x05, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x68, 0x20, 0x01, 0x28, 0x11, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x69, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x6b, 0x20, 0x01, 0x28, 0x12, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x75,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x6c, 0x20, 0x01, 0x28, 0x04, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x08, 0x03, 0x52, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x32, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x6d, 0x20, 0x01, 0x28, 0x0f, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x08, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x30, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x07,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x02,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x32, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x70, 0x20, 0x01, 0x28,
	0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x30, 0x0a, 0x10, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x71,
	0x20, 0x01, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18,
	0x72, 0x20, 0x01, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x0f,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18,
	0x73, 0x20, 0x01, 0x28, 0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0e, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x74,
	0x20, 0x01, 0x28, 0x0c, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x68, 0x0a, 0x10, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x75,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x08, 0x03, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x6b, 0x0a, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x76, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x07, 0xaa, 0x01, 0x04, 0x08, 0x03,
	0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x18, 0xc9, 0x01, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x59, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0xca, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x18, 0xcb, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xcc, 0x01,
	0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xcd, 0x01, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x26,
	0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0xce, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xcf, 0x01, 0x20, 0x03, 0x28, 0x12,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0xd0, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2c, 0x0a, 0x11, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18,
	0xd1, 0x01, 0x20, 0x03, 0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xd2, 0x01, 0x20,
	0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0xd3, 0x01, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2c, 0x0a, 0x11,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x18, 0xd4, 0x01, 0x20, 0x03, 0x28, 0x10, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2a, 0x0a, 0x10, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0xd5,
	0x01, 0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46,
	0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0xd6, 0x01, 0x20, 0x03, 0x28, 0x01,
	0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0xd7, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xd8, 0x01, 0x20,
	0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x62, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xd9, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x6a, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xda, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x28, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x18, 0xad, 0x02, 0x20, 0x01, 0x28, 0x08, 0x3a, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52,
	0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x5c, 0x0a, 0x0c,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0xae, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x3a, 0x03, 0x4f, 0x4e, 0x45, 0x52, 0x0b, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xaf, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x3a, 0x01, 0x31, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xb0, 0x02, 0x20, 0x01, 0x28, 0x11, 0x3a, 0x01, 0x31, 0x52,
	0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x29,
	0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0xb1, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x3a, 0x01, 0x31, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0d, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xb2, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x3a, 0x01, 0x31, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x29, 0x0a, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x18, 0xb3, 0x02, 0x20, 0x01, 0x28, 0x12, 0x3a, 0x01, 0x31, 0x52, 0x0d,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a,
	0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0xb4, 0x02, 0x20, 0x01, 0x28, 0x04, 0x3a, 0x01, 0x31, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2d, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xb5, 0x02, 0x20,
	0x01, 0x28, 0x0f, 0x3a, 0x01, 0x31, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xb6, 0x02, 0x20, 0x01, 0x28,
	0x07, 0x3a, 0x01, 0x31, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0xb7, 0x02, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x04, 0x33, 0x2e,
	0x31, 0x34, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74,
	0x12, 0x2d, 0x0a, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0xb8, 0x02, 0x20, 0x01, 0x28, 0x10, 0x3a, 0x01, 0x31, 0x52, 0x0f,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12,
	0x2b, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x18, 0xb9, 0x02, 0x20, 0x01, 0x28, 0x06, 0x3a, 0x01, 0x31, 0x52, 0x0e, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0xba,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x06, 0x33, 0x2e, 0x31, 0x34, 0x31, 0x35, 0x52, 0x0d, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0xbb,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x0f, 0x68, 0x65, 0x6c, 0x6c, 0x6f, 0x2c, 0x22, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x21, 0x22, 0x0a, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x0a, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xbc, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x3a, 0x16, 0x68,
	0x65, 0x6c, 0x6c, 0x6f, 0x2c, 0x5c, 0x33, 0x33, 0x36, 0x5c, 0x32, 0x35, 0x35, 0x5c, 0x32, 0x37,
	0x36, 0x5c, 0x33, 0x35, 0x37, 0x52, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x42, 0x79,
	0x74, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x7a,
	0x65, 0x72, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0xde, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x3a, 0x00, 0x52, 0x11, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5a, 0x65, 0x72, 0x6f,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x2f, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0xdf, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x3a, 0x00, 0x52, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5a, 0x65,
	0x72, 0x6f, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6e, 0x65, 0x67, 0x69, 0x6e, 0x66, 0x18,
	0x90, 0x03, 0x20, 0x01, 0x28, 0x02, 0x3a, 0x04, 0x2d, 0x69, 0x6e, 0x66, 0x52, 0x12, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4e, 0x65, 0x67, 0x69, 0x6e, 0x66,
	0x12, 0x36, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x6e, 0x66, 0x18, 0x91, 0x03, 0x20, 0x01, 0x28, 0x02, 0x3a,
	0x03, 0x69, 0x6e, 0x66, 0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x50, 0x6f, 0x73, 0x69, 0x6e, 0x66, 0x12, 0x30, 0x0a, 0x11, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6e, 0x18, 0x92, 0x03,
	0x20, 0x01, 0x28, 0x02, 0x3a, 0x03, 0x6e, 0x61, 0x6e, 0x52, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x4e, 0x61, 0x6e, 0x12, 0x39, 0x0a, 0x15, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x65, 0x67,
	0x69, 0x6e, 0x66, 0x18, 0x93, 0x03, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x04, 0x2d, 0x69, 0x6e, 0x66,
	0x52, 0x13, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x4e,
	0x65, 0x67, 0x69, 0x6e, 0x66, 0x12, 0x38, 0x0a, 0x15, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x6e, 0x66, 0x18, 0x94,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x03, 0x69, 0x6e, 0x66, 0x52, 0x13, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x6e, 0x66, 0x12,
	0x32, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6e, 0x18, 0x95, 0x03, 0x20, 0x01, 0x28, 0x01, 0x3a, 0x03, 0x6e, 0x61,
	0x6e, 0x52, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x4e, 0x61, 0x6e, 0x12, 0x6a, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xf4, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e,
	0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x61,
	0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x73, 0x0a, 0x12, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xf5, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x6d, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0xf6, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x42, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x6f, 0x6f,
	0x6c, 0x18, 0xd9, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x55, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0xda, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54,
	0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x48,
	0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0b,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xdb, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0xdc, 0x04, 0x20, 0x01, 0x28, 0x11, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xdd, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x00, 0x52,
	0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x22, 0x0a, 0x0b,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xde, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x49, 0x6e, 0x74, 0x36, 0x34,
	0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0xdf, 0x04, 0x20, 0x01, 0x28, 0x12, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0xe0, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52,
	0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x28, 0x0a, 0x0e,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xe1,
	0x04, 0x20, 0x01, 0x28, 0x0f, 0x48, 0x00, 0x52, 0x0d, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x26, 0x0a, 0x0d, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0xe2, 0x04, 0x20, 0x01, 0x28, 0x07, 0x48, 0x00,
	0x52, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x22,
	0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0xe3, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x12, 0x28, 0x0a, 0x0e, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0xe4, 0x04, 0x20, 0x01, 0x28, 0x10, 0x48, 0x00, 0x52, 0x0d, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x26, 0x0a, 0x0d,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0xe5, 0x04,
	0x20, 0x01, 0x28, 0x06, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x18, 0xe6, 0x04, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0c, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0xe7, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x22, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0xe8, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x42,
	0x79, 0x74, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x0d, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0xe9, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x63, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x18, 0xea, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65,
	0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4f, 0x6e, 0x65, 0x6f, 0x66, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x48, 0x00, 0x52, 0x0a, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x30, 0x0a, 0x11, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x5f, 0x6c, 0x61, 0x72, 0x67, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x67, 0x18, 0xff,
	0xff, 0xff, 0xff, 0x01, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x0f, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x4c, 0x61, 0x72, 0x67, 0x65, 0x73, 0x74, 0x54, 0x61, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x74, 0x77, 0x6f, 0x5f, 0x31, 0x18, 0xbc, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x54, 0x77, 0x6f, 0x31, 0x12, 0x21,
	0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x74, 0x77, 0x6f, 0x5f, 0x32, 0x18, 0xbd, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x54, 0x77, 0x6f,
	0x32, 0x1a, 0x35, 0x0a, 0x0d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x3d, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2c, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x77, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x08, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0x37, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x26, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0xdb, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x7b, 0x0a, 0x15, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67,
	0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x76, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x75,
	0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x54, 0x65, 0x73, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0a, 0x4f, 0x6e, 0x65, 0x6f, 0x66,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0xeb, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x1a, 0x09, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x19, 0x0a,
	0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x08, 0x0a, 0x04, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x4f, 0x4e, 0x45, 0x10, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f,
	0x66, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x5f, 0x74, 0x77, 0x6f, 0x4a, 0x06, 0x08, 0x90, 0x4e, 0x10, 0x91, 0x4e, 0x4a, 0x06, 0x08, 0x91,
	0x4e, 0x10, 0x92, 0x4e, 0x52, 0x0c, 0x54, 0x45, 0x4e, 0x5f, 0x54, 0x48, 0x4f, 0x55, 0x53, 0x41,
	0x4e, 0x44, 0x52, 0x14, 0x54, 0x45, 0x4e, 0x5f, 0x54, 0x48, 0x4f, 0x55, 0x53, 0x41, 0x4e, 0x44,
	0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x4f, 0x4e, 0x45, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x62,
	0x08, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescOnce sync.Once
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescData = file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDesc
)

func file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescGZIP() []byte {
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescOnce.Do(func() {
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescData)
	})
	return file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDescData
}

var file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_goTypes = []interface{}{
	(FieldTestMessage_Enum)(0),             // 0: goproto.protoc.protoeditions.FieldTestMessage.Enum
	(*FieldTestMessage)(nil),               // 1: goproto.protoc.protoeditions.FieldTestMessage
	(*FieldTestMessage_OptionalGroup)(nil), // 2: goproto.protoc.protoeditions.FieldTestMessage.OptionalGroup
	(*FieldTestMessage_RequiredGroup)(nil), // 3: goproto.protoc.protoeditions.FieldTestMessage.RequiredGroup
	(*FieldTestMessage_RepeatedGroup)(nil), // 4: goproto.protoc.protoeditions.FieldTestMessage.RepeatedGroup
	nil,                                    // 5: goproto.protoc.protoeditions.FieldTestMessage.MapInt32Int64Entry
	nil,                                    // 6: goproto.protoc.protoeditions.FieldTestMessage.MapStringMessageEntry
	nil,                                    // 7: goproto.protoc.protoeditions.FieldTestMessage.MapFixed64EnumEntry
	(*FieldTestMessage_OneofGroup)(nil),    // 8: goproto.protoc.protoeditions.FieldTestMessage.OneofGroup
	(*FieldTestMessage_Message)(nil),       // 9: goproto.protoc.protoeditions.FieldTestMessage.Message
}
var file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_depIdxs = []int32{
	0,  // 0: goproto.protoc.protoeditions.FieldTestMessage.optional_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	9,  // 1: goproto.protoc.protoeditions.FieldTestMessage.optional_Message:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Message
	2,  // 2: goproto.protoc.protoeditions.FieldTestMessage.optionalgroup:type_name -> goproto.protoc.protoeditions.FieldTestMessage.OptionalGroup
	0,  // 3: goproto.protoc.protoeditions.FieldTestMessage.required_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	9,  // 4: goproto.protoc.protoeditions.FieldTestMessage.required_Message:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Message
	3,  // 5: goproto.protoc.protoeditions.FieldTestMessage.requiredgroup:type_name -> goproto.protoc.protoeditions.FieldTestMessage.RequiredGroup
	0,  // 6: goproto.protoc.protoeditions.FieldTestMessage.repeated_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	9,  // 7: goproto.protoc.protoeditions.FieldTestMessage.repeated_Message:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Message
	4,  // 8: goproto.protoc.protoeditions.FieldTestMessage.repeatedgroup:type_name -> goproto.protoc.protoeditions.FieldTestMessage.RepeatedGroup
	0,  // 9: goproto.protoc.protoeditions.FieldTestMessage.default_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	5,  // 10: goproto.protoc.protoeditions.FieldTestMessage.map_int32_int64:type_name -> goproto.protoc.protoeditions.FieldTestMessage.MapInt32Int64Entry
	6,  // 11: goproto.protoc.protoeditions.FieldTestMessage.map_string_message:type_name -> goproto.protoc.protoeditions.FieldTestMessage.MapStringMessageEntry
	7,  // 12: goproto.protoc.protoeditions.FieldTestMessage.map_fixed64_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.MapFixed64EnumEntry
	0,  // 13: goproto.protoc.protoeditions.FieldTestMessage.oneof_enum:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	9,  // 14: goproto.protoc.protoeditions.FieldTestMessage.oneof_Message:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Message
	8,  // 15: goproto.protoc.protoeditions.FieldTestMessage.oneofgroup:type_name -> goproto.protoc.protoeditions.FieldTestMessage.OneofGroup
	9,  // 16: goproto.protoc.protoeditions.FieldTestMessage.MapStringMessageEntry.value:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Message
	0,  // 17: goproto.protoc.protoeditions.FieldTestMessage.MapFixed64EnumEntry.value:type_name -> goproto.protoc.protoeditions.FieldTestMessage.Enum
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_init() }
func file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_init() {
	if File_cmd_protoc_gen_go_testdata_protoeditions_fields_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage_OptionalGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage_RequiredGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage_RepeatedGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage_OneofGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldTestMessage_Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*FieldTestMessage_OneofBool)(nil),
		(*FieldTestMessage_OneofEnum)(nil),
		(*FieldTestMessage_OneofInt32)(nil),
		(*FieldTestMessage_OneofSint32)(nil),
		(*FieldTestMessage_OneofUint32)(nil),
		(*FieldTestMessage_OneofInt64)(nil),
		(*FieldTestMessage_OneofSint64)(nil),
		(*FieldTestMessage_OneofUint64)(nil),
		(*FieldTestMessage_OneofSfixed32)(nil),
		(*FieldTestMessage_OneofFixed32)(nil),
		(*FieldTestMessage_OneofFloat)(nil),
		(*FieldTestMessage_OneofSfixed64)(nil),
		(*FieldTestMessage_OneofFixed64)(nil),
		(*FieldTestMessage_OneofDouble)(nil),
		(*FieldTestMessage_OneofString)(nil),
		(*FieldTestMessage_OneofBytes)(nil),
		(*FieldTestMessage_Oneof_Message)(nil),
		(*FieldTestMessage_Oneofgroup)(nil),
		(*FieldTestMessage_OneofLargestTag)(nil),
		(*FieldTestMessage_OneofTwo_1)(nil),
		(*FieldTestMessage_OneofTwo_2)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_goTypes,
		DependencyIndexes: file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_depIdxs,
		EnumInfos:         file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_enumTypes,
		MessageInfos:      file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_msgTypes,
	}.Build()
	File_cmd_protoc_gen_go_testdata_protoeditions_fields_proto = out.File
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_rawDesc = nil
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_goTypes = nil
	file_cmd_protoc_gen_go_testdata_protoeditions_fields_proto_depIdxs = nil
}
