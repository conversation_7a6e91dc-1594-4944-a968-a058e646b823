/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package xdsresource

import (
	"regexp"
	"testing"
)

func (s) TestPathFullMatcherMatch(t *testing.T) {
	tests := []struct {
		name            string
		fullPath        string
		caseInsensitive bool
		path            string
		want            bool
	}{
		{name: "match", fullPath: "/s/m", path: "/s/m", want: true},
		{name: "case insensitive match", fullPath: "/s/m", caseInsensitive: true, path: "/S/m", want: true},
		{name: "case insensitive match 2", fullPath: "/s/M", caseInsensitive: true, path: "/S/m", want: true},
		{name: "not match", fullPath: "/s/m", path: "/a/b", want: false},
		{name: "case insensitive not match", fullPath: "/s/m", caseInsensitive: true, path: "/a/b", want: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fpm := newPathExactMatcher(tt.fullPath, tt.caseInsensitive)
			if got := fpm.match(tt.path); got != tt.want {
				t.Errorf("{%q}.match(%q) = %v, want %v", tt.fullPath, tt.path, got, tt.want)
			}
		})
	}
}

func (s) TestPathPrefixMatcherMatch(t *testing.T) {
	tests := []struct {
		name            string
		prefix          string
		caseInsensitive bool
		path            string
		want            bool
	}{
		{name: "match", prefix: "/s/", path: "/s/m", want: true},
		{name: "case insensitive match", prefix: "/s/", caseInsensitive: true, path: "/S/m", want: true},
		{name: "case insensitive match 2", prefix: "/S/", caseInsensitive: true, path: "/s/m", want: true},
		{name: "not match", prefix: "/s/", path: "/a/b", want: false},
		{name: "case insensitive not match", prefix: "/s/", caseInsensitive: true, path: "/a/b", want: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fpm := newPathPrefixMatcher(tt.prefix, tt.caseInsensitive)
			if got := fpm.match(tt.path); got != tt.want {
				t.Errorf("{%q}.match(%q) = %v, want %v", tt.prefix, tt.path, got, tt.want)
			}
		})
	}
}

func (s) TestPathRegexMatcherMatch(t *testing.T) {
	tests := []struct {
		name      string
		regexPath string
		path      string
		want      bool
	}{
		{name: "match", regexPath: "^/s+/m.*$", path: "/sss/me", want: true},
		{name: "not match", regexPath: "^/s+/m*$", path: "/sss/b", want: false},
		{name: "no match because only part of path matches with regex", regexPath: "^a+$", path: "ab", want: false},
		{name: "match because full path matches with regex", regexPath: "^a+$", path: "aa", want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fpm := newPathRegexMatcher(regexp.MustCompile(tt.regexPath))
			if got := fpm.match(tt.path); got != tt.want {
				t.Errorf("{%q}.match(%q) = %v, want %v", tt.regexPath, tt.path, got, tt.want)
			}
		})
	}
}
