// Copyright 2018 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cmd/protoc-gen-go/testdata/import_public/a.proto

package import_public

import (
	sub "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/import_public/sub"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

// Symbols defined in public import of cmd/protoc-gen-go/testdata/import_public/sub/a.proto.

type E = sub.E

const E_ZERO = sub.E_ZERO

var E_name = sub.E_name
var E_value = sub.E_value

type M_Subenum = sub.M_Subenum

const M_M_ZERO = sub.M_M_ZERO

var M_Subenum_name = sub.M_Subenum_name
var M_Subenum_value = sub.M_Subenum_value

type M_Submessage_Submessage_Subenum = sub.M_Submessage_Submessage_Subenum

const M_Submessage_M_SUBMESSAGE_ZERO = sub.M_Submessage_M_SUBMESSAGE_ZERO

var M_Submessage_Submessage_Subenum_name = sub.M_Submessage_Submessage_Subenum_name
var M_Submessage_Submessage_Subenum_value = sub.M_Submessage_Submessage_Subenum_value

type M = sub.M

const Default_M_S = sub.Default_M_S

var Default_M_B = sub.Default_M_B
var Default_M_F = sub.Default_M_F

type M_OneofInt32 = sub.M_OneofInt32
type M_OneofInt64 = sub.M_OneofInt64
type M_Submessage = sub.M_Submessage
type M_Submessage_SubmessageOneofInt32 = sub.M_Submessage_SubmessageOneofInt32
type M_Submessage_SubmessageOneofInt64 = sub.M_Submessage_SubmessageOneofInt64

var E_ExtensionField = sub.E_ExtensionField

type Public struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	M     *sub.M `protobuf:"bytes,1,opt,name=m" json:"m,omitempty"`
	E     *sub.E `protobuf:"varint,2,opt,name=e,enum=goproto.protoc.import_public.sub.E" json:"e,omitempty"`
	Local *Local `protobuf:"bytes,3,opt,name=local" json:"local,omitempty"`
}

func (x *Public) Reset() {
	*x = Public{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cmd_protoc_gen_go_testdata_import_public_a_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Public) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Public) ProtoMessage() {}

func (x *Public) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_protoc_gen_go_testdata_import_public_a_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Public.ProtoReflect.Descriptor instead.
func (*Public) Descriptor() ([]byte, []int) {
	return file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescGZIP(), []int{0}
}

func (x *Public) GetM() *sub.M {
	if x != nil {
		return x.M
	}
	return nil
}

func (x *Public) GetE() sub.E {
	if x != nil && x.E != nil {
		return *x.E
	}
	return sub.E(0)
}

func (x *Public) GetLocal() *Local {
	if x != nil {
		return x.Local
	}
	return nil
}

var File_cmd_protoc_gen_go_testdata_import_public_a_proto protoreflect.FileDescriptor

var file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDesc = []byte{
	0x0a, 0x30, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x2e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x1a, 0x30, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e,
	0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x62, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x34, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67,
	0x65, 0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2f, 0x73, 0x75, 0x62,
	0x2f, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x01, 0x0a, 0x06, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x12, 0x31, 0x0a, 0x01, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x73, 0x75,
	0x62, 0x2e, 0x4d, 0x52, 0x01, 0x6d, 0x12, 0x31, 0x0a, 0x01, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x2e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x2e, 0x73, 0x75, 0x62, 0x2e, 0x45, 0x52, 0x01, 0x65, 0x12, 0x39, 0x0a, 0x05, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2e, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x52, 0x05, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x63, 0x6d, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65,
	0x6e, 0x2d, 0x67, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x69, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x50, 0x00, 0x50, 0x01,
}

var (
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescOnce sync.Once
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescData = file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDesc
)

func file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescGZIP() []byte {
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescOnce.Do(func() {
		file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescData = protoimpl.X.CompressGZIP(file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescData)
	})
	return file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDescData
}

var file_cmd_protoc_gen_go_testdata_import_public_a_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_cmd_protoc_gen_go_testdata_import_public_a_proto_goTypes = []interface{}{
	(*Public)(nil), // 0: goproto.protoc.import_public.Public
	(*sub.M)(nil),  // 1: goproto.protoc.import_public.sub.M
	(sub.E)(0),     // 2: goproto.protoc.import_public.sub.E
	(*Local)(nil),  // 3: goproto.protoc.import_public.Local
}
var file_cmd_protoc_gen_go_testdata_import_public_a_proto_depIdxs = []int32{
	1, // 0: goproto.protoc.import_public.Public.m:type_name -> goproto.protoc.import_public.sub.M
	2, // 1: goproto.protoc.import_public.Public.e:type_name -> goproto.protoc.import_public.sub.E
	3, // 2: goproto.protoc.import_public.Public.local:type_name -> goproto.protoc.import_public.Local
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_cmd_protoc_gen_go_testdata_import_public_a_proto_init() }
func file_cmd_protoc_gen_go_testdata_import_public_a_proto_init() {
	if File_cmd_protoc_gen_go_testdata_import_public_a_proto != nil {
		return
	}
	file_cmd_protoc_gen_go_testdata_import_public_b_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_cmd_protoc_gen_go_testdata_import_public_a_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Public); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_protoc_gen_go_testdata_import_public_a_proto_goTypes,
		DependencyIndexes: file_cmd_protoc_gen_go_testdata_import_public_a_proto_depIdxs,
		MessageInfos:      file_cmd_protoc_gen_go_testdata_import_public_a_proto_msgTypes,
	}.Build()
	File_cmd_protoc_gen_go_testdata_import_public_a_proto = out.File
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_rawDesc = nil
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_goTypes = nil
	file_cmd_protoc_gen_go_testdata_import_public_a_proto_depIdxs = nil
}
