// Code generated by protoc-gen-go. DO NOT EDIT.
// source: conformance/test_protos/test_messages_edition2023.proto

package editions

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

type ForeignEnumEdition2023 int32

const (
	ForeignEnumEdition2023_FOREIGN_FOO ForeignEnumEdition2023 = 0
	ForeignEnumEdition2023_FOREIGN_BAR ForeignEnumEdition2023 = 1
	ForeignEnumEdition2023_FOREIGN_BAZ ForeignEnumEdition2023 = 2
)

// Enum value maps for ForeignEnumEdition2023.
var (
	ForeignEnumEdition2023_name = map[int32]string{
		0: "FOREIGN_FOO",
		1: "FOREIGN_BAR",
		2: "FOREIGN_BAZ",
	}
	ForeignEnumEdition2023_value = map[string]int32{
		"FOREIGN_FOO": 0,
		"FOREIGN_BAR": 1,
		"FOREIGN_BAZ": 2,
	}
)

func (x ForeignEnumEdition2023) Enum() *ForeignEnumEdition2023 {
	p := new(ForeignEnumEdition2023)
	*p = x
	return p
}

func (x ForeignEnumEdition2023) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForeignEnumEdition2023) Descriptor() protoreflect.EnumDescriptor {
	return file_conformance_test_protos_test_messages_edition2023_proto_enumTypes[0].Descriptor()
}

func (ForeignEnumEdition2023) Type() protoreflect.EnumType {
	return &file_conformance_test_protos_test_messages_edition2023_proto_enumTypes[0]
}

func (x ForeignEnumEdition2023) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForeignEnumEdition2023.Descriptor instead.
func (ForeignEnumEdition2023) EnumDescriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{0}
}

type TestAllTypesEdition2023_NestedEnum int32

const (
	TestAllTypesEdition2023_FOO TestAllTypesEdition2023_NestedEnum = 0
	TestAllTypesEdition2023_BAR TestAllTypesEdition2023_NestedEnum = 1
	TestAllTypesEdition2023_BAZ TestAllTypesEdition2023_NestedEnum = 2
	TestAllTypesEdition2023_NEG TestAllTypesEdition2023_NestedEnum = -1 // Intentionally negative.
)

// Enum value maps for TestAllTypesEdition2023_NestedEnum.
var (
	TestAllTypesEdition2023_NestedEnum_name = map[int32]string{
		0:  "FOO",
		1:  "BAR",
		2:  "BAZ",
		-1: "NEG",
	}
	TestAllTypesEdition2023_NestedEnum_value = map[string]int32{
		"FOO": 0,
		"BAR": 1,
		"BAZ": 2,
		"NEG": -1,
	}
)

func (x TestAllTypesEdition2023_NestedEnum) Enum() *TestAllTypesEdition2023_NestedEnum {
	p := new(TestAllTypesEdition2023_NestedEnum)
	*p = x
	return p
}

func (x TestAllTypesEdition2023_NestedEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestAllTypesEdition2023_NestedEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_conformance_test_protos_test_messages_edition2023_proto_enumTypes[1].Descriptor()
}

func (TestAllTypesEdition2023_NestedEnum) Type() protoreflect.EnumType {
	return &file_conformance_test_protos_test_messages_edition2023_proto_enumTypes[1]
}

func (x TestAllTypesEdition2023_NestedEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestAllTypesEdition2023_NestedEnum.Descriptor instead.
func (TestAllTypesEdition2023_NestedEnum) EnumDescriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{0, 0}
}

type TestAllTypesEdition2023 struct {
	state           protoimpl.MessageState
	sizeCache       protoimpl.SizeCache
	unknownFields   protoimpl.UnknownFields
	extensionFields protoimpl.ExtensionFields

	// Singular
	OptionalInt32          *int32                                 `protobuf:"varint,1,opt,name=optional_int32,json=optionalInt32" json:"optional_int32,omitempty"`
	OptionalInt64          *int64                                 `protobuf:"varint,2,opt,name=optional_int64,json=optionalInt64" json:"optional_int64,omitempty"`
	OptionalUint32         *uint32                                `protobuf:"varint,3,opt,name=optional_uint32,json=optionalUint32" json:"optional_uint32,omitempty"`
	OptionalUint64         *uint64                                `protobuf:"varint,4,opt,name=optional_uint64,json=optionalUint64" json:"optional_uint64,omitempty"`
	OptionalSint32         *int32                                 `protobuf:"zigzag32,5,opt,name=optional_sint32,json=optionalSint32" json:"optional_sint32,omitempty"`
	OptionalSint64         *int64                                 `protobuf:"zigzag64,6,opt,name=optional_sint64,json=optionalSint64" json:"optional_sint64,omitempty"`
	OptionalFixed32        *uint32                                `protobuf:"fixed32,7,opt,name=optional_fixed32,json=optionalFixed32" json:"optional_fixed32,omitempty"`
	OptionalFixed64        *uint64                                `protobuf:"fixed64,8,opt,name=optional_fixed64,json=optionalFixed64" json:"optional_fixed64,omitempty"`
	OptionalSfixed32       *int32                                 `protobuf:"fixed32,9,opt,name=optional_sfixed32,json=optionalSfixed32" json:"optional_sfixed32,omitempty"`
	OptionalSfixed64       *int64                                 `protobuf:"fixed64,10,opt,name=optional_sfixed64,json=optionalSfixed64" json:"optional_sfixed64,omitempty"`
	OptionalFloat          *float32                               `protobuf:"fixed32,11,opt,name=optional_float,json=optionalFloat" json:"optional_float,omitempty"`
	OptionalDouble         *float64                               `protobuf:"fixed64,12,opt,name=optional_double,json=optionalDouble" json:"optional_double,omitempty"`
	OptionalBool           *bool                                  `protobuf:"varint,13,opt,name=optional_bool,json=optionalBool" json:"optional_bool,omitempty"`
	OptionalString         *string                                `protobuf:"bytes,14,opt,name=optional_string,json=optionalString" json:"optional_string,omitempty"`
	OptionalBytes          []byte                                 `protobuf:"bytes,15,opt,name=optional_bytes,json=optionalBytes" json:"optional_bytes,omitempty"`
	OptionalNestedMessage  *TestAllTypesEdition2023_NestedMessage `protobuf:"bytes,18,opt,name=optional_nested_message,json=optionalNestedMessage" json:"optional_nested_message,omitempty"`
	OptionalForeignMessage *ForeignMessageEdition2023             `protobuf:"bytes,19,opt,name=optional_foreign_message,json=optionalForeignMessage" json:"optional_foreign_message,omitempty"`
	OptionalNestedEnum     *TestAllTypesEdition2023_NestedEnum    `protobuf:"varint,21,opt,name=optional_nested_enum,json=optionalNestedEnum,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum" json:"optional_nested_enum,omitempty"`
	OptionalForeignEnum    *ForeignEnumEdition2023                `protobuf:"varint,22,opt,name=optional_foreign_enum,json=optionalForeignEnum,enum=protobuf_test_messages.editions.ForeignEnumEdition2023" json:"optional_foreign_enum,omitempty"`
	OptionalStringPiece    *string                                `protobuf:"bytes,24,opt,name=optional_string_piece,json=optionalStringPiece" json:"optional_string_piece,omitempty"`
	OptionalCord           *string                                `protobuf:"bytes,25,opt,name=optional_cord,json=optionalCord" json:"optional_cord,omitempty"`
	RecursiveMessage       *TestAllTypesEdition2023               `protobuf:"bytes,27,opt,name=recursive_message,json=recursiveMessage" json:"recursive_message,omitempty"`
	// Repeated
	RepeatedInt32          []int32                                  `protobuf:"varint,31,rep,packed,name=repeated_int32,json=repeatedInt32" json:"repeated_int32,omitempty"`
	RepeatedInt64          []int64                                  `protobuf:"varint,32,rep,packed,name=repeated_int64,json=repeatedInt64" json:"repeated_int64,omitempty"`
	RepeatedUint32         []uint32                                 `protobuf:"varint,33,rep,packed,name=repeated_uint32,json=repeatedUint32" json:"repeated_uint32,omitempty"`
	RepeatedUint64         []uint64                                 `protobuf:"varint,34,rep,packed,name=repeated_uint64,json=repeatedUint64" json:"repeated_uint64,omitempty"`
	RepeatedSint32         []int32                                  `protobuf:"zigzag32,35,rep,packed,name=repeated_sint32,json=repeatedSint32" json:"repeated_sint32,omitempty"`
	RepeatedSint64         []int64                                  `protobuf:"zigzag64,36,rep,packed,name=repeated_sint64,json=repeatedSint64" json:"repeated_sint64,omitempty"`
	RepeatedFixed32        []uint32                                 `protobuf:"fixed32,37,rep,packed,name=repeated_fixed32,json=repeatedFixed32" json:"repeated_fixed32,omitempty"`
	RepeatedFixed64        []uint64                                 `protobuf:"fixed64,38,rep,packed,name=repeated_fixed64,json=repeatedFixed64" json:"repeated_fixed64,omitempty"`
	RepeatedSfixed32       []int32                                  `protobuf:"fixed32,39,rep,packed,name=repeated_sfixed32,json=repeatedSfixed32" json:"repeated_sfixed32,omitempty"`
	RepeatedSfixed64       []int64                                  `protobuf:"fixed64,40,rep,packed,name=repeated_sfixed64,json=repeatedSfixed64" json:"repeated_sfixed64,omitempty"`
	RepeatedFloat          []float32                                `protobuf:"fixed32,41,rep,packed,name=repeated_float,json=repeatedFloat" json:"repeated_float,omitempty"`
	RepeatedDouble         []float64                                `protobuf:"fixed64,42,rep,packed,name=repeated_double,json=repeatedDouble" json:"repeated_double,omitempty"`
	RepeatedBool           []bool                                   `protobuf:"varint,43,rep,packed,name=repeated_bool,json=repeatedBool" json:"repeated_bool,omitempty"`
	RepeatedString         []string                                 `protobuf:"bytes,44,rep,name=repeated_string,json=repeatedString" json:"repeated_string,omitempty"`
	RepeatedBytes          [][]byte                                 `protobuf:"bytes,45,rep,name=repeated_bytes,json=repeatedBytes" json:"repeated_bytes,omitempty"`
	RepeatedNestedMessage  []*TestAllTypesEdition2023_NestedMessage `protobuf:"bytes,48,rep,name=repeated_nested_message,json=repeatedNestedMessage" json:"repeated_nested_message,omitempty"`
	RepeatedForeignMessage []*ForeignMessageEdition2023             `protobuf:"bytes,49,rep,name=repeated_foreign_message,json=repeatedForeignMessage" json:"repeated_foreign_message,omitempty"`
	RepeatedNestedEnum     []TestAllTypesEdition2023_NestedEnum     `protobuf:"varint,51,rep,packed,name=repeated_nested_enum,json=repeatedNestedEnum,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum" json:"repeated_nested_enum,omitempty"`
	RepeatedForeignEnum    []ForeignEnumEdition2023                 `protobuf:"varint,52,rep,packed,name=repeated_foreign_enum,json=repeatedForeignEnum,enum=protobuf_test_messages.editions.ForeignEnumEdition2023" json:"repeated_foreign_enum,omitempty"`
	RepeatedStringPiece    []string                                 `protobuf:"bytes,54,rep,name=repeated_string_piece,json=repeatedStringPiece" json:"repeated_string_piece,omitempty"`
	RepeatedCord           []string                                 `protobuf:"bytes,55,rep,name=repeated_cord,json=repeatedCord" json:"repeated_cord,omitempty"`
	// Packed
	PackedInt32      []int32                              `protobuf:"varint,75,rep,packed,name=packed_int32,json=packedInt32" json:"packed_int32,omitempty"`
	PackedInt64      []int64                              `protobuf:"varint,76,rep,packed,name=packed_int64,json=packedInt64" json:"packed_int64,omitempty"`
	PackedUint32     []uint32                             `protobuf:"varint,77,rep,packed,name=packed_uint32,json=packedUint32" json:"packed_uint32,omitempty"`
	PackedUint64     []uint64                             `protobuf:"varint,78,rep,packed,name=packed_uint64,json=packedUint64" json:"packed_uint64,omitempty"`
	PackedSint32     []int32                              `protobuf:"zigzag32,79,rep,packed,name=packed_sint32,json=packedSint32" json:"packed_sint32,omitempty"`
	PackedSint64     []int64                              `protobuf:"zigzag64,80,rep,packed,name=packed_sint64,json=packedSint64" json:"packed_sint64,omitempty"`
	PackedFixed32    []uint32                             `protobuf:"fixed32,81,rep,packed,name=packed_fixed32,json=packedFixed32" json:"packed_fixed32,omitempty"`
	PackedFixed64    []uint64                             `protobuf:"fixed64,82,rep,packed,name=packed_fixed64,json=packedFixed64" json:"packed_fixed64,omitempty"`
	PackedSfixed32   []int32                              `protobuf:"fixed32,83,rep,packed,name=packed_sfixed32,json=packedSfixed32" json:"packed_sfixed32,omitempty"`
	PackedSfixed64   []int64                              `protobuf:"fixed64,84,rep,packed,name=packed_sfixed64,json=packedSfixed64" json:"packed_sfixed64,omitempty"`
	PackedFloat      []float32                            `protobuf:"fixed32,85,rep,packed,name=packed_float,json=packedFloat" json:"packed_float,omitempty"`
	PackedDouble     []float64                            `protobuf:"fixed64,86,rep,packed,name=packed_double,json=packedDouble" json:"packed_double,omitempty"`
	PackedBool       []bool                               `protobuf:"varint,87,rep,packed,name=packed_bool,json=packedBool" json:"packed_bool,omitempty"`
	PackedNestedEnum []TestAllTypesEdition2023_NestedEnum `protobuf:"varint,88,rep,packed,name=packed_nested_enum,json=packedNestedEnum,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum" json:"packed_nested_enum,omitempty"`
	// Unpacked
	UnpackedInt32      []int32                              `protobuf:"varint,89,rep,name=unpacked_int32,json=unpackedInt32" json:"unpacked_int32,omitempty"`
	UnpackedInt64      []int64                              `protobuf:"varint,90,rep,name=unpacked_int64,json=unpackedInt64" json:"unpacked_int64,omitempty"`
	UnpackedUint32     []uint32                             `protobuf:"varint,91,rep,name=unpacked_uint32,json=unpackedUint32" json:"unpacked_uint32,omitempty"`
	UnpackedUint64     []uint64                             `protobuf:"varint,92,rep,name=unpacked_uint64,json=unpackedUint64" json:"unpacked_uint64,omitempty"`
	UnpackedSint32     []int32                              `protobuf:"zigzag32,93,rep,name=unpacked_sint32,json=unpackedSint32" json:"unpacked_sint32,omitempty"`
	UnpackedSint64     []int64                              `protobuf:"zigzag64,94,rep,name=unpacked_sint64,json=unpackedSint64" json:"unpacked_sint64,omitempty"`
	UnpackedFixed32    []uint32                             `protobuf:"fixed32,95,rep,name=unpacked_fixed32,json=unpackedFixed32" json:"unpacked_fixed32,omitempty"`
	UnpackedFixed64    []uint64                             `protobuf:"fixed64,96,rep,name=unpacked_fixed64,json=unpackedFixed64" json:"unpacked_fixed64,omitempty"`
	UnpackedSfixed32   []int32                              `protobuf:"fixed32,97,rep,name=unpacked_sfixed32,json=unpackedSfixed32" json:"unpacked_sfixed32,omitempty"`
	UnpackedSfixed64   []int64                              `protobuf:"fixed64,98,rep,name=unpacked_sfixed64,json=unpackedSfixed64" json:"unpacked_sfixed64,omitempty"`
	UnpackedFloat      []float32                            `protobuf:"fixed32,99,rep,name=unpacked_float,json=unpackedFloat" json:"unpacked_float,omitempty"`
	UnpackedDouble     []float64                            `protobuf:"fixed64,100,rep,name=unpacked_double,json=unpackedDouble" json:"unpacked_double,omitempty"`
	UnpackedBool       []bool                               `protobuf:"varint,101,rep,name=unpacked_bool,json=unpackedBool" json:"unpacked_bool,omitempty"`
	UnpackedNestedEnum []TestAllTypesEdition2023_NestedEnum `protobuf:"varint,102,rep,name=unpacked_nested_enum,json=unpackedNestedEnum,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum" json:"unpacked_nested_enum,omitempty"`
	// Map
	MapInt32Int32           map[int32]int32                                   `protobuf:"bytes,56,rep,name=map_int32_int32,json=mapInt32Int32" json:"map_int32_int32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapInt64Int64           map[int64]int64                                   `protobuf:"bytes,57,rep,name=map_int64_int64,json=mapInt64Int64" json:"map_int64_int64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint32Uint32         map[uint32]uint32                                 `protobuf:"bytes,58,rep,name=map_uint32_uint32,json=mapUint32Uint32" json:"map_uint32_uint32,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapUint64Uint64         map[uint64]uint64                                 `protobuf:"bytes,59,rep,name=map_uint64_uint64,json=mapUint64Uint64" json:"map_uint64_uint64,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapSint32Sint32         map[int32]int32                                   `protobuf:"bytes,60,rep,name=map_sint32_sint32,json=mapSint32Sint32" json:"map_sint32_sint32,omitempty" protobuf_key:"zigzag32,1,opt,name=key" protobuf_val:"zigzag32,2,opt,name=value"`
	MapSint64Sint64         map[int64]int64                                   `protobuf:"bytes,61,rep,name=map_sint64_sint64,json=mapSint64Sint64" json:"map_sint64_sint64,omitempty" protobuf_key:"zigzag64,1,opt,name=key" protobuf_val:"zigzag64,2,opt,name=value"`
	MapFixed32Fixed32       map[uint32]uint32                                 `protobuf:"bytes,62,rep,name=map_fixed32_fixed32,json=mapFixed32Fixed32" json:"map_fixed32_fixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapFixed64Fixed64       map[uint64]uint64                                 `protobuf:"bytes,63,rep,name=map_fixed64_fixed64,json=mapFixed64Fixed64" json:"map_fixed64_fixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapSfixed32Sfixed32     map[int32]int32                                   `protobuf:"bytes,64,rep,name=map_sfixed32_sfixed32,json=mapSfixed32Sfixed32" json:"map_sfixed32_sfixed32,omitempty" protobuf_key:"fixed32,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapSfixed64Sfixed64     map[int64]int64                                   `protobuf:"bytes,65,rep,name=map_sfixed64_sfixed64,json=mapSfixed64Sfixed64" json:"map_sfixed64_sfixed64,omitempty" protobuf_key:"fixed64,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapInt32Float           map[int32]float32                                 `protobuf:"bytes,66,rep,name=map_int32_float,json=mapInt32Float" json:"map_int32_float,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`
	MapInt32Double          map[int32]float64                                 `protobuf:"bytes,67,rep,name=map_int32_double,json=mapInt32Double" json:"map_int32_double,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	MapBoolBool             map[bool]bool                                     `protobuf:"bytes,68,rep,name=map_bool_bool,json=mapBoolBool" json:"map_bool_bool,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MapStringString         map[string]string                                 `protobuf:"bytes,69,rep,name=map_string_string,json=mapStringString" json:"map_string_string,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringBytes          map[string][]byte                                 `protobuf:"bytes,70,rep,name=map_string_bytes,json=mapStringBytes" json:"map_string_bytes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedMessage  map[string]*TestAllTypesEdition2023_NestedMessage `protobuf:"bytes,71,rep,name=map_string_nested_message,json=mapStringNestedMessage" json:"map_string_nested_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringForeignMessage map[string]*ForeignMessageEdition2023             `protobuf:"bytes,72,rep,name=map_string_foreign_message,json=mapStringForeignMessage" json:"map_string_foreign_message,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MapStringNestedEnum     map[string]TestAllTypesEdition2023_NestedEnum     `protobuf:"bytes,73,rep,name=map_string_nested_enum,json=mapStringNestedEnum" json:"map_string_nested_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum"`
	MapStringForeignEnum    map[string]ForeignEnumEdition2023                 `protobuf:"bytes,74,rep,name=map_string_foreign_enum,json=mapStringForeignEnum" json:"map_string_foreign_enum,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=protobuf_test_messages.editions.ForeignEnumEdition2023"`
	// Types that are assignable to OneofField:
	//
	//	*TestAllTypesEdition2023_OneofUint32
	//	*TestAllTypesEdition2023_OneofNestedMessage
	//	*TestAllTypesEdition2023_OneofString
	//	*TestAllTypesEdition2023_OneofBytes
	//	*TestAllTypesEdition2023_OneofBool
	//	*TestAllTypesEdition2023_OneofUint64
	//	*TestAllTypesEdition2023_OneofFloat
	//	*TestAllTypesEdition2023_OneofDouble
	//	*TestAllTypesEdition2023_OneofEnum
	OneofField     isTestAllTypesEdition2023_OneofField   `protobuf_oneof:"oneof_field"`
	Groupliketype  *TestAllTypesEdition2023_GroupLikeType `protobuf:"group,201,opt,name=GroupLikeType,json=groupliketype" json:"groupliketype,omitempty"`
	DelimitedField *TestAllTypesEdition2023_GroupLikeType `protobuf:"group,202,opt,name=GroupLikeType,json=delimitedField" json:"delimited_field,omitempty"`
}

func (x *TestAllTypesEdition2023) Reset() {
	*x = TestAllTypesEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesEdition2023) ProtoMessage() {}

func (x *TestAllTypesEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesEdition2023.ProtoReflect.Descriptor instead.
func (*TestAllTypesEdition2023) Descriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{0}
}

func (x *TestAllTypesEdition2023) GetOptionalInt32() int32 {
	if x != nil && x.OptionalInt32 != nil {
		return *x.OptionalInt32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalInt64() int64 {
	if x != nil && x.OptionalInt64 != nil {
		return *x.OptionalInt64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalUint32() uint32 {
	if x != nil && x.OptionalUint32 != nil {
		return *x.OptionalUint32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalUint64() uint64 {
	if x != nil && x.OptionalUint64 != nil {
		return *x.OptionalUint64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalSint32() int32 {
	if x != nil && x.OptionalSint32 != nil {
		return *x.OptionalSint32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalSint64() int64 {
	if x != nil && x.OptionalSint64 != nil {
		return *x.OptionalSint64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalFixed32() uint32 {
	if x != nil && x.OptionalFixed32 != nil {
		return *x.OptionalFixed32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalFixed64() uint64 {
	if x != nil && x.OptionalFixed64 != nil {
		return *x.OptionalFixed64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalSfixed32() int32 {
	if x != nil && x.OptionalSfixed32 != nil {
		return *x.OptionalSfixed32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalSfixed64() int64 {
	if x != nil && x.OptionalSfixed64 != nil {
		return *x.OptionalSfixed64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalFloat() float32 {
	if x != nil && x.OptionalFloat != nil {
		return *x.OptionalFloat
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalDouble() float64 {
	if x != nil && x.OptionalDouble != nil {
		return *x.OptionalDouble
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOptionalBool() bool {
	if x != nil && x.OptionalBool != nil {
		return *x.OptionalBool
	}
	return false
}

func (x *TestAllTypesEdition2023) GetOptionalString() string {
	if x != nil && x.OptionalString != nil {
		return *x.OptionalString
	}
	return ""
}

func (x *TestAllTypesEdition2023) GetOptionalBytes() []byte {
	if x != nil {
		return x.OptionalBytes
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOptionalNestedMessage() *TestAllTypesEdition2023_NestedMessage {
	if x != nil {
		return x.OptionalNestedMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOptionalForeignMessage() *ForeignMessageEdition2023 {
	if x != nil {
		return x.OptionalForeignMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOptionalNestedEnum() TestAllTypesEdition2023_NestedEnum {
	if x != nil && x.OptionalNestedEnum != nil {
		return *x.OptionalNestedEnum
	}
	return TestAllTypesEdition2023_FOO
}

func (x *TestAllTypesEdition2023) GetOptionalForeignEnum() ForeignEnumEdition2023 {
	if x != nil && x.OptionalForeignEnum != nil {
		return *x.OptionalForeignEnum
	}
	return ForeignEnumEdition2023_FOREIGN_FOO
}

func (x *TestAllTypesEdition2023) GetOptionalStringPiece() string {
	if x != nil && x.OptionalStringPiece != nil {
		return *x.OptionalStringPiece
	}
	return ""
}

func (x *TestAllTypesEdition2023) GetOptionalCord() string {
	if x != nil && x.OptionalCord != nil {
		return *x.OptionalCord
	}
	return ""
}

func (x *TestAllTypesEdition2023) GetRecursiveMessage() *TestAllTypesEdition2023 {
	if x != nil {
		return x.RecursiveMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedInt32() []int32 {
	if x != nil {
		return x.RepeatedInt32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedInt64() []int64 {
	if x != nil {
		return x.RepeatedInt64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedUint32() []uint32 {
	if x != nil {
		return x.RepeatedUint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedUint64() []uint64 {
	if x != nil {
		return x.RepeatedUint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedSint32() []int32 {
	if x != nil {
		return x.RepeatedSint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedSint64() []int64 {
	if x != nil {
		return x.RepeatedSint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedFixed32() []uint32 {
	if x != nil {
		return x.RepeatedFixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedFixed64() []uint64 {
	if x != nil {
		return x.RepeatedFixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedSfixed32() []int32 {
	if x != nil {
		return x.RepeatedSfixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedSfixed64() []int64 {
	if x != nil {
		return x.RepeatedSfixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedFloat() []float32 {
	if x != nil {
		return x.RepeatedFloat
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedDouble() []float64 {
	if x != nil {
		return x.RepeatedDouble
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedBool() []bool {
	if x != nil {
		return x.RepeatedBool
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedString() []string {
	if x != nil {
		return x.RepeatedString
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedBytes() [][]byte {
	if x != nil {
		return x.RepeatedBytes
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedNestedMessage() []*TestAllTypesEdition2023_NestedMessage {
	if x != nil {
		return x.RepeatedNestedMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedForeignMessage() []*ForeignMessageEdition2023 {
	if x != nil {
		return x.RepeatedForeignMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedNestedEnum() []TestAllTypesEdition2023_NestedEnum {
	if x != nil {
		return x.RepeatedNestedEnum
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedForeignEnum() []ForeignEnumEdition2023 {
	if x != nil {
		return x.RepeatedForeignEnum
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedStringPiece() []string {
	if x != nil {
		return x.RepeatedStringPiece
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetRepeatedCord() []string {
	if x != nil {
		return x.RepeatedCord
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedInt32() []int32 {
	if x != nil {
		return x.PackedInt32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedInt64() []int64 {
	if x != nil {
		return x.PackedInt64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedUint32() []uint32 {
	if x != nil {
		return x.PackedUint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedUint64() []uint64 {
	if x != nil {
		return x.PackedUint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedSint32() []int32 {
	if x != nil {
		return x.PackedSint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedSint64() []int64 {
	if x != nil {
		return x.PackedSint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedFixed32() []uint32 {
	if x != nil {
		return x.PackedFixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedFixed64() []uint64 {
	if x != nil {
		return x.PackedFixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedSfixed32() []int32 {
	if x != nil {
		return x.PackedSfixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedSfixed64() []int64 {
	if x != nil {
		return x.PackedSfixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedFloat() []float32 {
	if x != nil {
		return x.PackedFloat
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedDouble() []float64 {
	if x != nil {
		return x.PackedDouble
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedBool() []bool {
	if x != nil {
		return x.PackedBool
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetPackedNestedEnum() []TestAllTypesEdition2023_NestedEnum {
	if x != nil {
		return x.PackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedInt32() []int32 {
	if x != nil {
		return x.UnpackedInt32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedInt64() []int64 {
	if x != nil {
		return x.UnpackedInt64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedUint32() []uint32 {
	if x != nil {
		return x.UnpackedUint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedUint64() []uint64 {
	if x != nil {
		return x.UnpackedUint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedSint32() []int32 {
	if x != nil {
		return x.UnpackedSint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedSint64() []int64 {
	if x != nil {
		return x.UnpackedSint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedFixed32() []uint32 {
	if x != nil {
		return x.UnpackedFixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedFixed64() []uint64 {
	if x != nil {
		return x.UnpackedFixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedSfixed32() []int32 {
	if x != nil {
		return x.UnpackedSfixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedSfixed64() []int64 {
	if x != nil {
		return x.UnpackedSfixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedFloat() []float32 {
	if x != nil {
		return x.UnpackedFloat
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedDouble() []float64 {
	if x != nil {
		return x.UnpackedDouble
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedBool() []bool {
	if x != nil {
		return x.UnpackedBool
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetUnpackedNestedEnum() []TestAllTypesEdition2023_NestedEnum {
	if x != nil {
		return x.UnpackedNestedEnum
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapInt32Int32() map[int32]int32 {
	if x != nil {
		return x.MapInt32Int32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapInt64Int64() map[int64]int64 {
	if x != nil {
		return x.MapInt64Int64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapUint32Uint32() map[uint32]uint32 {
	if x != nil {
		return x.MapUint32Uint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapUint64Uint64() map[uint64]uint64 {
	if x != nil {
		return x.MapUint64Uint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapSint32Sint32() map[int32]int32 {
	if x != nil {
		return x.MapSint32Sint32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapSint64Sint64() map[int64]int64 {
	if x != nil {
		return x.MapSint64Sint64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapFixed32Fixed32() map[uint32]uint32 {
	if x != nil {
		return x.MapFixed32Fixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapFixed64Fixed64() map[uint64]uint64 {
	if x != nil {
		return x.MapFixed64Fixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapSfixed32Sfixed32() map[int32]int32 {
	if x != nil {
		return x.MapSfixed32Sfixed32
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapSfixed64Sfixed64() map[int64]int64 {
	if x != nil {
		return x.MapSfixed64Sfixed64
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapInt32Float() map[int32]float32 {
	if x != nil {
		return x.MapInt32Float
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapInt32Double() map[int32]float64 {
	if x != nil {
		return x.MapInt32Double
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapBoolBool() map[bool]bool {
	if x != nil {
		return x.MapBoolBool
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringString() map[string]string {
	if x != nil {
		return x.MapStringString
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringBytes() map[string][]byte {
	if x != nil {
		return x.MapStringBytes
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringNestedMessage() map[string]*TestAllTypesEdition2023_NestedMessage {
	if x != nil {
		return x.MapStringNestedMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringForeignMessage() map[string]*ForeignMessageEdition2023 {
	if x != nil {
		return x.MapStringForeignMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringNestedEnum() map[string]TestAllTypesEdition2023_NestedEnum {
	if x != nil {
		return x.MapStringNestedEnum
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetMapStringForeignEnum() map[string]ForeignEnumEdition2023 {
	if x != nil {
		return x.MapStringForeignEnum
	}
	return nil
}

func (m *TestAllTypesEdition2023) GetOneofField() isTestAllTypesEdition2023_OneofField {
	if m != nil {
		return m.OneofField
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOneofUint32() uint32 {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofUint32); ok {
		return x.OneofUint32
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOneofNestedMessage() *TestAllTypesEdition2023_NestedMessage {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofNestedMessage); ok {
		return x.OneofNestedMessage
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOneofString() string {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofString); ok {
		return x.OneofString
	}
	return ""
}

func (x *TestAllTypesEdition2023) GetOneofBytes() []byte {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofBytes); ok {
		return x.OneofBytes
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetOneofBool() bool {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofBool); ok {
		return x.OneofBool
	}
	return false
}

func (x *TestAllTypesEdition2023) GetOneofUint64() uint64 {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofUint64); ok {
		return x.OneofUint64
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOneofFloat() float32 {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofFloat); ok {
		return x.OneofFloat
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOneofDouble() float64 {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofDouble); ok {
		return x.OneofDouble
	}
	return 0
}

func (x *TestAllTypesEdition2023) GetOneofEnum() TestAllTypesEdition2023_NestedEnum {
	if x, ok := x.GetOneofField().(*TestAllTypesEdition2023_OneofEnum); ok {
		return x.OneofEnum
	}
	return TestAllTypesEdition2023_FOO
}

func (x *TestAllTypesEdition2023) GetGroupliketype() *TestAllTypesEdition2023_GroupLikeType {
	if x != nil {
		return x.Groupliketype
	}
	return nil
}

func (x *TestAllTypesEdition2023) GetDelimitedField() *TestAllTypesEdition2023_GroupLikeType {
	if x != nil {
		return x.DelimitedField
	}
	return nil
}

type isTestAllTypesEdition2023_OneofField interface {
	isTestAllTypesEdition2023_OneofField()
}

type TestAllTypesEdition2023_OneofUint32 struct {
	OneofUint32 uint32 `protobuf:"varint,111,opt,name=oneof_uint32,json=oneofUint32,oneof"`
}

type TestAllTypesEdition2023_OneofNestedMessage struct {
	OneofNestedMessage *TestAllTypesEdition2023_NestedMessage `protobuf:"bytes,112,opt,name=oneof_nested_message,json=oneofNestedMessage,oneof"`
}

type TestAllTypesEdition2023_OneofString struct {
	OneofString string `protobuf:"bytes,113,opt,name=oneof_string,json=oneofString,oneof"`
}

type TestAllTypesEdition2023_OneofBytes struct {
	OneofBytes []byte `protobuf:"bytes,114,opt,name=oneof_bytes,json=oneofBytes,oneof"`
}

type TestAllTypesEdition2023_OneofBool struct {
	OneofBool bool `protobuf:"varint,115,opt,name=oneof_bool,json=oneofBool,oneof"`
}

type TestAllTypesEdition2023_OneofUint64 struct {
	OneofUint64 uint64 `protobuf:"varint,116,opt,name=oneof_uint64,json=oneofUint64,oneof"`
}

type TestAllTypesEdition2023_OneofFloat struct {
	OneofFloat float32 `protobuf:"fixed32,117,opt,name=oneof_float,json=oneofFloat,oneof"`
}

type TestAllTypesEdition2023_OneofDouble struct {
	OneofDouble float64 `protobuf:"fixed64,118,opt,name=oneof_double,json=oneofDouble,oneof"`
}

type TestAllTypesEdition2023_OneofEnum struct {
	OneofEnum TestAllTypesEdition2023_NestedEnum `protobuf:"varint,119,opt,name=oneof_enum,json=oneofEnum,enum=protobuf_test_messages.editions.TestAllTypesEdition2023_NestedEnum,oneof"`
}

func (*TestAllTypesEdition2023_OneofUint32) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofNestedMessage) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofString) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofBytes) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofBool) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofUint64) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofFloat) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofDouble) isTestAllTypesEdition2023_OneofField() {}

func (*TestAllTypesEdition2023_OneofEnum) isTestAllTypesEdition2023_OneofField() {}

type ForeignMessageEdition2023 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
}

func (x *ForeignMessageEdition2023) Reset() {
	*x = ForeignMessageEdition2023{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeignMessageEdition2023) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeignMessageEdition2023) ProtoMessage() {}

func (x *ForeignMessageEdition2023) ProtoReflect() protoreflect.Message {
	mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeignMessageEdition2023.ProtoReflect.Descriptor instead.
func (*ForeignMessageEdition2023) Descriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{1}
}

func (x *ForeignMessageEdition2023) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

type GroupLikeType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	C *int32 `protobuf:"varint,1,opt,name=c" json:"c,omitempty"`
}

func (x *GroupLikeType) Reset() {
	*x = GroupLikeType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupLikeType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupLikeType) ProtoMessage() {}

func (x *GroupLikeType) ProtoReflect() protoreflect.Message {
	mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupLikeType.ProtoReflect.Descriptor instead.
func (*GroupLikeType) Descriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{2}
}

func (x *GroupLikeType) GetC() int32 {
	if x != nil && x.C != nil {
		return *x.C
	}
	return 0
}

type TestAllTypesEdition2023_NestedMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	A           *int32                   `protobuf:"varint,1,opt,name=a" json:"a,omitempty"`
	Corecursive *TestAllTypesEdition2023 `protobuf:"bytes,2,opt,name=corecursive" json:"corecursive,omitempty"`
}

func (x *TestAllTypesEdition2023_NestedMessage) Reset() {
	*x = TestAllTypesEdition2023_NestedMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesEdition2023_NestedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesEdition2023_NestedMessage) ProtoMessage() {}

func (x *TestAllTypesEdition2023_NestedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesEdition2023_NestedMessage.ProtoReflect.Descriptor instead.
func (*TestAllTypesEdition2023_NestedMessage) Descriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TestAllTypesEdition2023_NestedMessage) GetA() int32 {
	if x != nil && x.A != nil {
		return *x.A
	}
	return 0
}

func (x *TestAllTypesEdition2023_NestedMessage) GetCorecursive() *TestAllTypesEdition2023 {
	if x != nil {
		return x.Corecursive
	}
	return nil
}

// groups
type TestAllTypesEdition2023_GroupLikeType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInt32  *int32  `protobuf:"varint,202,opt,name=group_int32,json=groupInt32" json:"group_int32,omitempty"`
	GroupUint32 *uint32 `protobuf:"varint,203,opt,name=group_uint32,json=groupUint32" json:"group_uint32,omitempty"`
}

func (x *TestAllTypesEdition2023_GroupLikeType) Reset() {
	*x = TestAllTypesEdition2023_GroupLikeType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestAllTypesEdition2023_GroupLikeType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestAllTypesEdition2023_GroupLikeType) ProtoMessage() {}

func (x *TestAllTypesEdition2023_GroupLikeType) ProtoReflect() protoreflect.Message {
	mi := &file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestAllTypesEdition2023_GroupLikeType.ProtoReflect.Descriptor instead.
func (*TestAllTypesEdition2023_GroupLikeType) Descriptor() ([]byte, []int) {
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP(), []int{0, 20}
}

func (x *TestAllTypesEdition2023_GroupLikeType) GetGroupInt32() int32 {
	if x != nil && x.GroupInt32 != nil {
		return *x.GroupInt32
	}
	return 0
}

func (x *TestAllTypesEdition2023_GroupLikeType) GetGroupUint32() uint32 {
	if x != nil && x.GroupUint32 != nil {
		return *x.GroupUint32
	}
	return 0
}

var file_conformance_test_protos_test_messages_edition2023_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*TestAllTypesEdition2023)(nil),
		ExtensionType: (*int32)(nil),
		Field:         120,
		Name:          "protobuf_test_messages.editions.extension_int32",
		Tag:           "varint,120,opt,name=extension_int32",
		Filename:      "conformance/test_protos/test_messages_edition2023.proto",
	},
	{
		ExtendedType:  (*TestAllTypesEdition2023)(nil),
		ExtensionType: (*GroupLikeType)(nil),
		Field:         121,
		Name:          "protobuf_test_messages.editions.groupliketype",
		Tag:           "bytes,121,opt,name=groupliketype",
		Filename:      "conformance/test_protos/test_messages_edition2023.proto",
	},
	{
		ExtendedType:  (*TestAllTypesEdition2023)(nil),
		ExtensionType: (*GroupLikeType)(nil),
		Field:         122,
		Name:          "protobuf_test_messages.editions.delimited_ext",
		Tag:           "bytes,122,opt,name=delimited_ext",
		Filename:      "conformance/test_protos/test_messages_edition2023.proto",
	},
}

// Extension fields to TestAllTypesEdition2023.
var (
	// optional int32 extension_int32 = 120;
	E_ExtensionInt32 = &file_conformance_test_protos_test_messages_edition2023_proto_extTypes[0]
	// optional protobuf_test_messages.editions.GroupLikeType groupliketype = 121;
	E_Groupliketype = &file_conformance_test_protos_test_messages_edition2023_proto_extTypes[1]
	// optional protobuf_test_messages.editions.GroupLikeType delimited_ext = 122;
	E_DelimitedExt = &file_conformance_test_protos_test_messages_edition2023_proto_extTypes[2]
)

var File_conformance_test_protos_test_messages_edition2023_proto protoreflect.FileDescriptor

var file_conformance_test_protos_test_messages_edition2023_proto_rawDesc = []byte{
	0x0a, 0x37, 0x63, 0x6f, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x2f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x5f, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf8, 0x45, 0x0a, 0x17, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a,
	0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x11, 0x52,
	0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x06, 0x20, 0x01, 0x28, 0x12, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x07, 0x52, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x08, 0x20, 0x01, 0x28, 0x06, 0x52, 0x0f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b,
	0x0a, 0x11, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x10, 0x52, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x27, 0x0a, 0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0c, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x7e, 0x0a,
	0x17, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x74, 0x0a,
	0x18, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x52, 0x16, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x12, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x6b, 0x0a, 0x15, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x36, 0x0a, 0x15, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08, 0x02, 0x52, 0x13, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12,
	0x27, 0x0a, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x72, 0x64, 0x12, 0x65, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x75,
	0x72, 0x73, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74,
	0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x52, 0x10, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x20, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0x21, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x22, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x18, 0x23, 0x20, 0x03, 0x28, 0x11, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x24, 0x20, 0x03, 0x28,
	0x12, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x25, 0x20, 0x03, 0x28, 0x07, 0x52, 0x0f, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x29, 0x0a, 0x10,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34,
	0x18, 0x26, 0x20, 0x03, 0x28, 0x06, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x27, 0x20, 0x03,
	0x28, 0x0f, 0x52, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x28, 0x20, 0x03, 0x28, 0x10, 0x52,
	0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x29, 0x20, 0x03, 0x28, 0x02, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x2a, 0x20, 0x03, 0x28,
	0x01, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x6f,
	0x6f, 0x6c, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x2c, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x25, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x2d, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x7e, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x30, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x74, 0x0a, 0x18, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x31, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x52, 0x16, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f,
	0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x75, 0x0a, 0x14,
	0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x12, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x6b, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x34, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x52, 0x13, 0x72, 0x65, 0x70,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x36, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x70, 0x69, 0x65, 0x63, 0x65, 0x18, 0x36, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x02, 0x08, 0x02, 0x52, 0x13, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x69, 0x65, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0d, 0x72, 0x65, 0x70, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x37, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x02, 0x08, 0x01, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x72,
	0x64, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x18, 0x4b, 0x20, 0x03, 0x28, 0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x28, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x4c, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x4d, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x18, 0x4e, 0x20, 0x03, 0x28, 0x04, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2a, 0x0a,
	0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x4f,
	0x20, 0x03, 0x28, 0x11, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2a, 0x0a, 0x0d, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x50, 0x20, 0x03, 0x28, 0x12,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x51, 0x20, 0x03, 0x28, 0x07, 0x42, 0x05, 0xaa,
	0x01, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65,
	0x64, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69,
	0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x52, 0x20, 0x03, 0x28, 0x06, 0x42, 0x05, 0xaa, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x33, 0x32, 0x18, 0x53, 0x20, 0x03, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78,
	0x65, 0x64, 0x36, 0x34, 0x18, 0x54, 0x20, 0x03, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x28, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x61,
	0x74, 0x18, 0x55, 0x20, 0x03, 0x28, 0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x2a, 0x0a, 0x0d, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x56, 0x20, 0x03,
	0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18, 0x57, 0x20, 0x03, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01,
	0x02, 0x18, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12,
	0x78, 0x0a, 0x12, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x58, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65,
	0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x01, 0x52, 0x10, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x2c, 0x0a, 0x0e, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x59, 0x20, 0x03, 0x28,
	0x05, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2c, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5a, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x49, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5b, 0x20, 0x03, 0x28, 0x0d, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5c, 0x20, 0x03, 0x28, 0x04, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x55,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x5d, 0x20, 0x03, 0x28, 0x11, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x5e, 0x20, 0x03, 0x28, 0x12, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53,
	0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x30, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x5f, 0x20, 0x03, 0x28, 0x07, 0x42,
	0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x30, 0x0a, 0x10, 0x75, 0x6e, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x60, 0x20, 0x03, 0x28,
	0x06, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b,
	0x65, 0x64, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x12, 0x32, 0x0a, 0x11, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x61,
	0x20, 0x03, 0x28, 0x0f, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x10, 0x75, 0x6e, 0x70,
	0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x32, 0x0a,
	0x11, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x18, 0x62, 0x20, 0x03, 0x28, 0x10, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52,
	0x10, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x2c, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x66, 0x6c,
	0x6f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x03, 0x28, 0x02, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02,
	0x52, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12,
	0x2e, 0x0a, 0x0f, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x18, 0x64, 0x20, 0x03, 0x28, 0x01, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52,
	0x0e, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12,
	0x2a, 0x0a, 0x0d, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x6f, 0x6c,
	0x18, 0x65, 0x20, 0x03, 0x28, 0x08, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x0c, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x7c, 0x0a, 0x14, 0x75,
	0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x18, 0x66, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x05,
	0xaa, 0x01, 0x02, 0x18, 0x02, 0x52, 0x12, 0x75, 0x6e, 0x70, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x4e,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x73, 0x0a, 0x0f, 0x6d, 0x61, 0x70,
	0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x38, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70,
	0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x73,
	0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x18, 0x39, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x79, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d,
	0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x79,
	0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x75, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x18, 0x3b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x55, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x79, 0x0a, 0x11, 0x6d, 0x61, 0x70,
	0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x3c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x12, 0x79, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x69, 0x6e, 0x74,
	0x36, 0x34, 0x5f, 0x73, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x3d, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f,
	0x6d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12,
	0x7f, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x5f, 0x66,
	0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x3e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x6d,
	0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32,
	0x12, 0x7f, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x18, 0x3f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11,
	0x6d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x18, 0x40, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33,
	0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x12, 0x85, 0x01, 0x0a, 0x15, 0x6d, 0x61,
	0x70, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x5f, 0x73, 0x66, 0x69, 0x78, 0x65,
	0x64, 0x36, 0x34, 0x18, 0x41, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53,
	0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61,
	0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36,
	0x34, 0x12, 0x73, 0x0a, 0x0f, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x5f, 0x66,
	0x6c, 0x6f, 0x61, 0x74, 0x18, 0x42, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46, 0x6c, 0x6f,
	0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x76, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f, 0x69, 0x6e,
	0x74, 0x33, 0x32, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x43, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x4c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e,
	0x6d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x12, 0x6d,
	0x0a, 0x0d, 0x6d, 0x61, 0x70, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x6f, 0x6f, 0x6c, 0x18,
	0x44, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e,
	0x4d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6d, 0x61, 0x70, 0x42, 0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x79, 0x0a,
	0x11, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x45, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x76, 0x0a, 0x10, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x46, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0e, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73,
	0x12, 0x8f, 0x01, 0x0a, 0x19, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x47,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x6d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x48, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69,
	0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17,
	0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x18, 0x49, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41,
	0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d,
	0x12, 0x89, 0x01, 0x0a, 0x17, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x66, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x18, 0x4a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x52, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4d, 0x61, 0x70,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75,
	0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x6d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x23, 0x0a, 0x0c,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0x6f, 0x20, 0x01,
	0x28, 0x0d, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x33,
	0x32, 0x12, 0x7a, 0x0a, 0x14, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x6e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x70, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x46, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x12, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a,
	0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x71, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62, 0x79, 0x74, 0x65,
	0x73, 0x18, 0x72, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66,
	0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x62,
	0x6f, 0x6f, 0x6c, 0x18, 0x73, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6f, 0x6e, 0x65,
	0x6f, 0x66, 0x42, 0x6f, 0x6f, 0x6c, 0x12, 0x23, 0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x18, 0x74, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x12, 0x21, 0x0a, 0x0b, 0x6f,
	0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x18, 0x75, 0x20, 0x01, 0x28, 0x02,
	0x48, 0x00, 0x52, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x12, 0x23,
	0x0a, 0x0c, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x18, 0x76,
	0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x12, 0x64, 0x0a, 0x0a, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x18, 0x77, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x48, 0x00, 0x52, 0x09,
	0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x74, 0x0a, 0x0d, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x6c, 0x69, 0x6b, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0xc9, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02,
	0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x6c, 0x69, 0x6b, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x77, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0xca, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0e, 0x64, 0x65, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x1a, 0x79, 0x0a, 0x0d, 0x4e, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x0c, 0x0a, 0x01, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x61, 0x12, 0x5a, 0x0a, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63,
	0x75, 0x72, 0x73, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54,
	0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x52, 0x0b, 0x63, 0x6f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x73,
	0x69, 0x76, 0x65, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x36,
	0x34, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x55, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d,
	0x61, 0x70, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x55, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x53, 0x69, 0x6e, 0x74,
	0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x11, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x11, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x53, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x12, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x12, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a, 0x16, 0x4d, 0x61, 0x70, 0x46, 0x69,
	0x78, 0x65, 0x64, 0x33, 0x32, 0x46, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x07, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x07, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x44, 0x0a,
	0x16, 0x4d, 0x61, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x46, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x06, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x06, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d, 0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x33, 0x32, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x33, 0x32, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0f, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x4d,
	0x61, 0x70, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64, 0x36, 0x34, 0x53, 0x66, 0x69, 0x78, 0x65, 0x64,
	0x36, 0x34, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x10, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x10, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x46,
	0x6c, 0x6f, 0x61, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13, 0x4d, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x42,
	0x6f, 0x6f, 0x6c, 0x42, 0x6f, 0x6f, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x42, 0x0a, 0x14, 0x4d, 0x61, 0x70, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x41, 0x0a, 0x13,
	0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x91, 0x01, 0x0a, 0x1b, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x5c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x46, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x86, 0x01, 0x0a, 0x1c, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x50, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32,
	0x33, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x8b, 0x01, 0x0a,
	0x18, 0x4d, 0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x45, 0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x59, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73,
	0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x32, 0x30, 0x32, 0x33, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e, 0x75, 0x6d, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x80, 0x01, 0x0a, 0x19, 0x4d,
	0x61, 0x70, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4d, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x55, 0x0a,
	0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x18, 0xca, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0x22, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x69, 0x6e, 0x74, 0x33, 0x32,
	0x18, 0xcb, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x69,
	0x6e, 0x74, 0x33, 0x32, 0x22, 0x39, 0x0a, 0x0a, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x42,
	0x41, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x12, 0x10, 0x0a,
	0x03, 0x4e, 0x45, 0x47, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x2a,
	0x05, 0x08, 0x78, 0x10, 0xc9, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x6f, 0x6e, 0x65, 0x6f, 0x66, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x29, 0x0a, 0x19, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30,
	0x32, 0x33, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x63,
	0x22, 0x1d, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0c, 0x0a, 0x01, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x63, 0x2a,
	0x4b, 0x0a, 0x16, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x52,
	0x45, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x4f, 0x4f, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f,
	0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x46,
	0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f, 0x42, 0x41, 0x5a, 0x10, 0x02, 0x3a, 0x61, 0x0a, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x33, 0x32, 0x12,
	0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x18, 0x78, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0e, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x3a,
	0x95, 0x01, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x6c, 0x69, 0x6b, 0x65, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x18, 0x79, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x6c,
	0x69, 0x6b, 0x65, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x94, 0x01, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78, 0x74, 0x12, 0x38, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74,
	0x41, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32,
	0x30, 0x32, 0x33, 0x18, 0x7a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4c, 0x69, 0x6b, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x05, 0xaa, 0x01, 0x02, 0x28, 0x02,
	0x52, 0x0c, 0x64, 0x65, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x45, 0x78, 0x74, 0x42, 0x3c,
	0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x65, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0x30, 0x32, 0x33, 0x50,
	0x01, 0xa2, 0x02, 0x08, 0x45, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x62, 0x08, 0x65, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x70, 0xe8, 0x07,
}

var (
	file_conformance_test_protos_test_messages_edition2023_proto_rawDescOnce sync.Once
	file_conformance_test_protos_test_messages_edition2023_proto_rawDescData = file_conformance_test_protos_test_messages_edition2023_proto_rawDesc
)

func file_conformance_test_protos_test_messages_edition2023_proto_rawDescGZIP() []byte {
	file_conformance_test_protos_test_messages_edition2023_proto_rawDescOnce.Do(func() {
		file_conformance_test_protos_test_messages_edition2023_proto_rawDescData = protoimpl.X.CompressGZIP(file_conformance_test_protos_test_messages_edition2023_proto_rawDescData)
	})
	return file_conformance_test_protos_test_messages_edition2023_proto_rawDescData
}

var file_conformance_test_protos_test_messages_edition2023_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_conformance_test_protos_test_messages_edition2023_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_conformance_test_protos_test_messages_edition2023_proto_goTypes = []interface{}{
	(ForeignEnumEdition2023)(0),                   // 0: protobuf_test_messages.editions.ForeignEnumEdition2023
	(TestAllTypesEdition2023_NestedEnum)(0),       // 1: protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	(*TestAllTypesEdition2023)(nil),               // 2: protobuf_test_messages.editions.TestAllTypesEdition2023
	(*ForeignMessageEdition2023)(nil),             // 3: protobuf_test_messages.editions.ForeignMessageEdition2023
	(*GroupLikeType)(nil),                         // 4: protobuf_test_messages.editions.GroupLikeType
	(*TestAllTypesEdition2023_NestedMessage)(nil), // 5: protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage
	nil, // 6: protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32Int32Entry
	nil, // 7: protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt64Int64Entry
	nil, // 8: protobuf_test_messages.editions.TestAllTypesEdition2023.MapUint32Uint32Entry
	nil, // 9: protobuf_test_messages.editions.TestAllTypesEdition2023.MapUint64Uint64Entry
	nil, // 10: protobuf_test_messages.editions.TestAllTypesEdition2023.MapSint32Sint32Entry
	nil, // 11: protobuf_test_messages.editions.TestAllTypesEdition2023.MapSint64Sint64Entry
	nil, // 12: protobuf_test_messages.editions.TestAllTypesEdition2023.MapFixed32Fixed32Entry
	nil, // 13: protobuf_test_messages.editions.TestAllTypesEdition2023.MapFixed64Fixed64Entry
	nil, // 14: protobuf_test_messages.editions.TestAllTypesEdition2023.MapSfixed32Sfixed32Entry
	nil, // 15: protobuf_test_messages.editions.TestAllTypesEdition2023.MapSfixed64Sfixed64Entry
	nil, // 16: protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32FloatEntry
	nil, // 17: protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32DoubleEntry
	nil, // 18: protobuf_test_messages.editions.TestAllTypesEdition2023.MapBoolBoolEntry
	nil, // 19: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringStringEntry
	nil, // 20: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringBytesEntry
	nil, // 21: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedMessageEntry
	nil, // 22: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignMessageEntry
	nil, // 23: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedEnumEntry
	nil, // 24: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignEnumEntry
	(*TestAllTypesEdition2023_GroupLikeType)(nil), // 25: protobuf_test_messages.editions.TestAllTypesEdition2023.GroupLikeType
}
var file_conformance_test_protos_test_messages_edition2023_proto_depIdxs = []int32{
	5,  // 0: protobuf_test_messages.editions.TestAllTypesEdition2023.optional_nested_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage
	3,  // 1: protobuf_test_messages.editions.TestAllTypesEdition2023.optional_foreign_message:type_name -> protobuf_test_messages.editions.ForeignMessageEdition2023
	1,  // 2: protobuf_test_messages.editions.TestAllTypesEdition2023.optional_nested_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	0,  // 3: protobuf_test_messages.editions.TestAllTypesEdition2023.optional_foreign_enum:type_name -> protobuf_test_messages.editions.ForeignEnumEdition2023
	2,  // 4: protobuf_test_messages.editions.TestAllTypesEdition2023.recursive_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023
	5,  // 5: protobuf_test_messages.editions.TestAllTypesEdition2023.repeated_nested_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage
	3,  // 6: protobuf_test_messages.editions.TestAllTypesEdition2023.repeated_foreign_message:type_name -> protobuf_test_messages.editions.ForeignMessageEdition2023
	1,  // 7: protobuf_test_messages.editions.TestAllTypesEdition2023.repeated_nested_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	0,  // 8: protobuf_test_messages.editions.TestAllTypesEdition2023.repeated_foreign_enum:type_name -> protobuf_test_messages.editions.ForeignEnumEdition2023
	1,  // 9: protobuf_test_messages.editions.TestAllTypesEdition2023.packed_nested_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	1,  // 10: protobuf_test_messages.editions.TestAllTypesEdition2023.unpacked_nested_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	6,  // 11: protobuf_test_messages.editions.TestAllTypesEdition2023.map_int32_int32:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32Int32Entry
	7,  // 12: protobuf_test_messages.editions.TestAllTypesEdition2023.map_int64_int64:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt64Int64Entry
	8,  // 13: protobuf_test_messages.editions.TestAllTypesEdition2023.map_uint32_uint32:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapUint32Uint32Entry
	9,  // 14: protobuf_test_messages.editions.TestAllTypesEdition2023.map_uint64_uint64:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapUint64Uint64Entry
	10, // 15: protobuf_test_messages.editions.TestAllTypesEdition2023.map_sint32_sint32:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapSint32Sint32Entry
	11, // 16: protobuf_test_messages.editions.TestAllTypesEdition2023.map_sint64_sint64:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapSint64Sint64Entry
	12, // 17: protobuf_test_messages.editions.TestAllTypesEdition2023.map_fixed32_fixed32:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapFixed32Fixed32Entry
	13, // 18: protobuf_test_messages.editions.TestAllTypesEdition2023.map_fixed64_fixed64:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapFixed64Fixed64Entry
	14, // 19: protobuf_test_messages.editions.TestAllTypesEdition2023.map_sfixed32_sfixed32:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapSfixed32Sfixed32Entry
	15, // 20: protobuf_test_messages.editions.TestAllTypesEdition2023.map_sfixed64_sfixed64:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapSfixed64Sfixed64Entry
	16, // 21: protobuf_test_messages.editions.TestAllTypesEdition2023.map_int32_float:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32FloatEntry
	17, // 22: protobuf_test_messages.editions.TestAllTypesEdition2023.map_int32_double:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapInt32DoubleEntry
	18, // 23: protobuf_test_messages.editions.TestAllTypesEdition2023.map_bool_bool:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapBoolBoolEntry
	19, // 24: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_string:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringStringEntry
	20, // 25: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_bytes:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringBytesEntry
	21, // 26: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_nested_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedMessageEntry
	22, // 27: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_foreign_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignMessageEntry
	23, // 28: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_nested_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedEnumEntry
	24, // 29: protobuf_test_messages.editions.TestAllTypesEdition2023.map_string_foreign_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignEnumEntry
	5,  // 30: protobuf_test_messages.editions.TestAllTypesEdition2023.oneof_nested_message:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage
	1,  // 31: protobuf_test_messages.editions.TestAllTypesEdition2023.oneof_enum:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	25, // 32: protobuf_test_messages.editions.TestAllTypesEdition2023.groupliketype:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.GroupLikeType
	25, // 33: protobuf_test_messages.editions.TestAllTypesEdition2023.delimited_field:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.GroupLikeType
	2,  // 34: protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage.corecursive:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023
	5,  // 35: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedMessageEntry.value:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedMessage
	3,  // 36: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignMessageEntry.value:type_name -> protobuf_test_messages.editions.ForeignMessageEdition2023
	1,  // 37: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringNestedEnumEntry.value:type_name -> protobuf_test_messages.editions.TestAllTypesEdition2023.NestedEnum
	0,  // 38: protobuf_test_messages.editions.TestAllTypesEdition2023.MapStringForeignEnumEntry.value:type_name -> protobuf_test_messages.editions.ForeignEnumEdition2023
	2,  // 39: protobuf_test_messages.editions.extension_int32:extendee -> protobuf_test_messages.editions.TestAllTypesEdition2023
	2,  // 40: protobuf_test_messages.editions.groupliketype:extendee -> protobuf_test_messages.editions.TestAllTypesEdition2023
	2,  // 41: protobuf_test_messages.editions.delimited_ext:extendee -> protobuf_test_messages.editions.TestAllTypesEdition2023
	4,  // 42: protobuf_test_messages.editions.groupliketype:type_name -> protobuf_test_messages.editions.GroupLikeType
	4,  // 43: protobuf_test_messages.editions.delimited_ext:type_name -> protobuf_test_messages.editions.GroupLikeType
	44, // [44:44] is the sub-list for method output_type
	44, // [44:44] is the sub-list for method input_type
	42, // [42:44] is the sub-list for extension type_name
	39, // [39:42] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_conformance_test_protos_test_messages_edition2023_proto_init() }
func file_conformance_test_protos_test_messages_edition2023_proto_init() {
	if File_conformance_test_protos_test_messages_edition2023_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			case 3:
				return &v.extensionFields
			default:
				return nil
			}
		}
		file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeignMessageEdition2023); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupLikeType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesEdition2023_NestedMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestAllTypesEdition2023_GroupLikeType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_conformance_test_protos_test_messages_edition2023_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TestAllTypesEdition2023_OneofUint32)(nil),
		(*TestAllTypesEdition2023_OneofNestedMessage)(nil),
		(*TestAllTypesEdition2023_OneofString)(nil),
		(*TestAllTypesEdition2023_OneofBytes)(nil),
		(*TestAllTypesEdition2023_OneofBool)(nil),
		(*TestAllTypesEdition2023_OneofUint64)(nil),
		(*TestAllTypesEdition2023_OneofFloat)(nil),
		(*TestAllTypesEdition2023_OneofDouble)(nil),
		(*TestAllTypesEdition2023_OneofEnum)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conformance_test_protos_test_messages_edition2023_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   24,
			NumExtensions: 3,
			NumServices:   0,
		},
		GoTypes:           file_conformance_test_protos_test_messages_edition2023_proto_goTypes,
		DependencyIndexes: file_conformance_test_protos_test_messages_edition2023_proto_depIdxs,
		EnumInfos:         file_conformance_test_protos_test_messages_edition2023_proto_enumTypes,
		MessageInfos:      file_conformance_test_protos_test_messages_edition2023_proto_msgTypes,
		ExtensionInfos:    file_conformance_test_protos_test_messages_edition2023_proto_extTypes,
	}.Build()
	File_conformance_test_protos_test_messages_edition2023_proto = out.File
	file_conformance_test_protos_test_messages_edition2023_proto_rawDesc = nil
	file_conformance_test_protos_test_messages_edition2023_proto_goTypes = nil
	file_conformance_test_protos_test_messages_edition2023_proto_depIdxs = nil
}
