// Copyright 2020 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

syntax = "proto2";

package oneoftest;

option go_package = "google.golang.org/protobuf/cmd/protoc-gen-go/testdata/issue780_oneof_conflict";

message Foo {
  oneof bar {  // must be generated as Bar field in Foo struct
    string get_bar = 1;
  }
}
